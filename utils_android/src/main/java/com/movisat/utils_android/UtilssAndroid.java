package com.movisat.utils_android;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Build;
import android.util.Base64;

import com.movisat.utils.Utilss;

import java.io.ByteArrayOutputStream;
import java.net.InetAddress;
import java.util.ArrayList;
import java.util.List;

public class UtilssAndroid {
    public static List<AbisType> getAbis() {
        List<AbisType> types = new ArrayList();
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
            // on newer Android versions, we'll return only the most important Abi version
            for (int i = 0; i < Build.SUPPORTED_ABIS.length; i++)
                types.add(getAbisType(Build.SUPPORTED_ABIS[i]));
        } else {
            // on pre-Lollip versions, we got only one Abi
            types.add(getAbisType(Build.CPU_ABI));
        }

        return types;
    }

    public static boolean hasAbis(AbisType[] abis) {
        if (Utilss.isNullOrEmpty(abis)) return false;

        List<AbisType> current = getAbis();
        if (Utilss.isNullOrEmpty(current)) return false;

        for (AbisType a : abis)
            if (current.contains(a)) return true;

        return false;

    }

    private static AbisType getAbisType(String abis) {
        switch (abis) {
            case "armeabi-v7a":
                return AbisType.armeabi_v7a;
            case "armeabi":
                return AbisType.armeabi;
            case "x86":
                return AbisType.x86;
            case "mips":
                return AbisType.mips;
            case "arm64-v8a":
                return AbisType.arm64_v8a;
            case "x86_64":
                return AbisType.x86_64;
        }
        return AbisType.unknow;
    }

    public static boolean isAndroidGreater11() {
        return android.os.Build.VERSION.SDK_INT > Build.VERSION_CODES.R;
    }

    public static String bitmapToBase64(Bitmap bitmap) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream);
        return Base64.encodeToString(outputStream.toByteArray(), Base64.DEFAULT);
    }

    public static Bitmap base64ToBitmap(String base64) throws IllegalArgumentException {
        byte[] decodedBytes = Base64.decode(
                base64.substring(base64.indexOf(",") + 1),
                Base64.DEFAULT
        );
        return BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.length);
    }

    /// Comprueba si se puede conectar con Google.
    ///
    /// Requiere los permisos:
    /// <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    /// <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    public static boolean isInternetAvailable() {
        try {
            InetAddress ipAddr = InetAddress.getByName("www.google.com");
            return !ipAddr.equals("");
        } catch (Exception e) {
            return false;
        }
    }
}
