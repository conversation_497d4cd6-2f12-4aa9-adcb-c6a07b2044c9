# 25090301
- HU-AP-ER-001/AP-627: Se añade la nueva tabla pdfs a la base de datos local
- HU-AP-ER-001/AP-627: Se añaden botones que muestran los pdfs descargados
- HU-AP-ER-001/AP-627: Variable en intranet btnOcultarPdf para ocultar los botones de pdf
- HU-AP-ER-001/AP-627: Al seleccionar un pdf se abre el visor de pdfs externo llamado MuPDF-Mini

# 25070401
- 7304/AP-498: No suena la notificación de Mensaje recibido si el mensaje es de hace más de 24 horas
- 7258/AP-521: El botón para procesar manual en ruta se puede ocular con una variable en la intranet
- 7257/AP-520: Fuerzo la sincronización de los modelos de datos para los bicompartimentados
- 7237/AP-480: Corregidos errores del brillo, cambio a 51 el DRACO con el brillo bueno y a 21 el brillo del DRACO malo
- 7227/AP-479: El bluetooth se activa y desactiva al tocar el botón en ajustes, sin salir de la aplicación

# 25060201
- Corregido error al deserializar la fecha de baja de los elementos de la ruta

# 25053001
- Corregido procesado manual en lavado

# 25052901
- Corregidos errores de procesado manual

# 25052701
- Ahora para los bicompartimentados llega sensor procesar 42 y una entrada digital con el tipo de residuo
- Cuando la entrada digital no está en la base de datos, no se muestra nigún elemento
- Cuando la entrada digital está en base de datos, pero no corresponde a un residuo de la ruta, no se muestra ningún elemento
- Se elimina el mensaje "No hay elementos que cumpla las condiciones para procesar"

# 25052601
- Solucionado error al mostrar icono de la báscula
- Solucionada error al mostrar la lista de elementos
- Solucionado error al traer los datos del servidor en la lista de entradas digitales y residuo
- Al procesar elementos se usa la fecha de la entrada digital o evento

# 25052301
- 7210/AP-459: Posibilidad de ocultar el botón de Descarga en la ruta mediante la variable en intranet btnOcultarDescarga [[Mantis]](https://bugs.movisat.com/view.php?id=7210)[[YouTrack]](https://youtrack.movisat.com:8081/issue/AP-459)
- 7233/AP-466: Elimino pantalla Filtro Elementos para el mapa principal del código [[Mantis]](https://bugs.movisat.com/view.php?id=7233)[[YouTrack]](https://youtrack.movisat.com:8081/issue/AP-466)
- 7229/AP-463: Añado icono cuando la báscula está conectada por bluetooth [[Mantis]](https://bugs.movisat.com/view.php?id=7229)[[YouTrack]](https://youtrack.movisat.com:8081/issue/AP-463)
- 7230/AP-464: Procesamiento de elementos en vehículos bicompartimentados por entrada digital y residuo [[Mantis]](https://bugs.movisat.com/view.php?id=7230)[[YouTrack]](https://youtrack.movisat.com:8081/issue/AP-464)

# 25050201
- 7069/AP-402: Añado llamada a la API en caso de no tener idRutaH en estados de ruta distintos a actividad iniciada [[Mantis]](https://bugs.movisat.com/view.php?id=7069)[[YouTrack]](https://youtrack.movisat.com:8081/issue/AP-402)
- 7172/AP-428: Elimino el botón azul atrás en la pantalla de selección de nivel de llenado y desactivo la posibilidad de ir atrás con los gestos o controles del dispositivo [[Mantis]](https://bugs.movisat.com/view.php?id=7172)[[YouTrack]](https://youtrack.movisat.com:8081/issue/AP-428)
- 7173/AP-410: Arreglo los problemas de procesado de tag de ecorutas [[Mantis]](https://bugs.movisat.com/view.php?id=7173)[[YouTrack]](https://youtrack.movisat.com:8081/issue/AP-410)
- AP-410: Reviso y arreglo a fondo el procesado de tag, automático, por nivel de llenado y por lavado [[YouTrack]](https://youtrack.movisat.com:8081/issue/AP-410)
- AP-410: Corrijo el mensaje al leer un tag fuera de ruta para procesado automático y por lavado [[YouTrack]](https://youtrack.movisat.com:8081/issue/AP-410)
- AP-410: Limpio la cola de tag al iniciar y finalizar actividad para evitar bloqueos [[YouTrack]](https://youtrack.movisat.com:8081/issue/AP-410)
- AP-410: Arreglo los tag fuera de ruta que no se procesaban por nivel de llenado [[YouTrack]](https://youtrack.movisat.com:8081/issue/AP-410)
- AP-410: Impido que se pueda procesar un mismo tag fuera de ruta varias veces [[YouTrack]](https://youtrack.movisat.com:8081/issue/AP-410)
- AP-442: Arreglo un cierre de la app aleatorio por la localización [[YouTrack]](https://youtrack.movisat.com:8081/issue/AP-442)

# 25030601
- 7048: Revierto el cámbio de añadir 0 a la derecha, compruebo el id de la tarjeta tal cual lo lee EcoRutas sin hacer modificaciones

# 25030502
- 7040: Actualizo la migración de la base de datos para que funcione el inicio de turno en los equipos sin borrar datos
- 7042: En la primera sincronización de EcoRutas, cuando inicio turno por primera vez con un trabajador, obtengo la hora actual del DRACO en vez de poner 01/01/1970
- 7045: Se ha incluido el volumen de notificaciones en el mismo controlador que el volumen del sistema y se controlan los dos a la vez
- 7048: Rellena el ID NFC con 0 a la derecha hasta completar 14 caracteres
- AP-349: Se comprueba el tipo de objeto que recibe EcoRutas al leer el tag para UHF y 134, en vez de buscar el valor del dracoSensor.puertoCL7206

# 25022601
- 7001: Se ha mejorado la implementacion de las camaras wifi, no se desconectan y actualizan correctamente la visualización cuando volvemos a la pantalla de selección de cámaras
- 7001: Revierto la versión de la librería de cámaras wifi a v3.0.6 por que subir la versión no arregló el error. Para evitar problemas futuros de versiones dejamos la que siempre hemos usado, v3.0.6.

# 25022401
- 6982: Corregido el procesado de todos los tags en cola
- No se procesan los tags en cola se quedan fuera del inicio de la acividad
- Se marca la fecha inicio de ruta con la fecha del primer tag leido dentro del incio de la actividad

# 25021301
- 7001: Nueva versión librería de cámaras WiFi v3.0.8
- 7001: Reducción del tamaño de imagen de la cámara principal a 400kb

# 25020701
- 6919: Mejora del brillo con DMV2 más recientes
- 6993: Nueva funcionalidad para identificarse con NFC
- Ajustes menores

# 25012002
- 6919: Obligo a aceptar permisos al arrancar la app.

# 25012001
- 6919: Se ajusta capa negra transparente encima de la app, va aumentando progresivamente la opacidad hasta el 80% más o menos, desde el 50 hasta el 0.

# 25011601
- 6919: Se simula brillo más bajo añadiendo una capa negra transparente encima de la app que va aumentando progresivamente la opacidad hasta el 40% más o menos, desde el 20 hasta el 0.

# 24112701
- 6775: Se añade botón en ajustes para reiniciar la Zona WIFI.

# 24112001
- 6894: Solucionado bucle de inicialización.
- 6775: Se integra cámara interna cuando no tiene wifi/datos ni la url del proyecto.
- 6910: Mejora para evitar la pérdida de paquetes.
- El procesado por tag en rutas que no son de niveles de llenado mostraba un mensaje diciendo que ya había sido procesado.

# 24101001
- Mantis 6775: Desarrollo cámaras wifi.
- Mantis 6769: Se comprueba url para detectar cambio de proyecto.
- Se cambia endpoin de elementos por el de reducidos.

# 24092001
- Mantis 6760: Fallo al leer un TAG que no está en ruta. Al crear el objeto de tag se estaban invirtiendo los parámetros tag y matricula.
- Mantis 6769: Cambio de EQUIPO de proyectos. Se borran datos de la app en caso de que el código de móvil no coincida.

# 24091001
- Mantis 6774: No se procesan elementos tras lectura de tag.
- Mantis 6764: Te "echa" de la ruta al recoger todos los elementos.
- Mantis 6765: No aparece la opción DESCARGA en la pestaña ELEMENTOS.
- Mantis 6760: Fallo al leer un TAG que no está en ruta.

# 24080201
- Se añaden cambios del Navegador de Here, incluida la actualización a la v4.15.
- Botones para subir/bajar volumen en el navegador.
- Mantis 6587: Se envía sensor 100 con el procesado manual.
- Mantis 6617: Optimizaciones en el acceso a datos.
- Se amplian logs a 30 días y se limpian logs.

# 24070401
- Mantis 6672: Corrijo tipo de dato al leer ruta de la base de datos.
- Mantis 6666: Corrijo consultas de los trabajadores en las que se confundía entre empresa y codigoMovil.
- Añado código de salida que borra los datos de la app y muestro toast en caso de código incorrecto.

# 24061301
- Añado variable elemProcAuto para procesar automáticamente el elemento más cercano.
- Mejoras en la navegación simulada para versiones de exposición.

# 24061001
- Mantis 6617: Se mejora la carga de información.

# 24052801
- Mantis 6615: Se elimina Sygic.
- Mantis 6589: Se eliminan vista tras volver de la vista de rutas, se pone la principal.
- Mantis 6600: Se soluciona problema al actualizar los elementos de una ruta.
- Fix para poder actualiizar con ruta iniciada
- Reducidos logs a 7 días.
- Se actualiza variable en el draco al iniciar/finalizar actividad.
- Se actualiza fecha de sincronización solo si vienen datos.

# 24042201
- Corrijo error que pedía sensores (kms...) varias veces.
- Obtengo token si es necesario entre paginas de elementos.

# 24041901
- 6552: Problemas con las asginaciones de rutas
  - Se corrigen errores en las asignaciones
  - Se usa la fecha del servidor para las sincronizaciones
- Error en callingapp

# 24041702
- Rollback error de casting

# 24041701
- Refactor confirmación de rutas.
- 6543: Fecha hora del DESCANSO INICIADO

# 24041501
- Corrección en la inserción de algunos jobs.
- Corrección error que pedía nivel de llenado dos veces.
- Se elimina animación en diálogos.
- Confirmación la sincronización de rutas una vez.
- Se registra en Sentry intento de inicio de actidad sin fecha.
- Se registra en Sentry cuanto falla 10 veces el envío de un paquete.

# 24041101
- Corrijo error al enviar cambio de estado de ruta.

# 24041002
- Se añade bandeja de salida personalizada para el envio de paquetes.

# 24041001
- Guardo guid de rutaH con el codigo de ruta para al menos no mezclar diferentes rutas. Uso el guid para obtener el idRutah en lugar del codigo de ruta para al menos mezclar entre diferente historicos. Intento obtener el idRutah al generar el paquete, sino se sigue obteniendo al mandarlo.
- Se añade contexto a Sentry.

# 24031501
- 6477: Fecha de paquete con las jornadas
- Cambio valores por defecto de la conf del mapa
- 6475: La app se cierra de manera inesperada
  - Se detecta error en sentry, no encuentra el constructor vacio
  - Limito longitud de linea en los logs de RequestInterceptor
- 6436: Falta de cierre de identificación empleado
  - Genero paquetes de fin de ruta y fin de actividad aunque el estado anterior no corresponda

# 24022801
- Mantis 6460: Se superpone nombre empleado con info del turno iniciado
- Mantis 6459: No se ajusta texto al marcar revisiones

# 24022201
- Mantis 6436: Faltan de cierre de identificación
 - Error al borrrar asignaciones que no exiten en el equipo.
 - Fecha incorrecta en el fin de actividad.
 - Error al obtener el idRutaH al inicair la actividad.
- Mantis 6439: Elementos de la ruta sin texto
 - Error al obtener la imagen del modelo que impedía mostrar los textos.

# 24020901
- Mantis 6380: Solucionado bug que abría dos veces el nivel de llenado.
- Mantis 6404: Se sincronizan mensajes aún estando en ruta.

# 24011801
- Misma hora para las revisiones finales que para el fin de turno.

# 24011701
- Mantis 6374: Se añaden restricciones al introducir jornadas.
- Se habilitan botones al sincronizar moviles

# 24011001
- Mantis 6068: No cambia al modo navegación de Sygic en pestaña "Navegador".
```
- Corrección. Navegador Here. No se reproducían las voces.
- Corrección. Navegador Sygic. Volvía a cargar navegación al inicio de ruta cuando se cambiaba de pestaña.
```

# 23121801
- Mantis 6319: Soluciono error que impedía mostrar las revisiones al iniciar turno

# 23121401
- Mantis 6319: En Personal tras finalizar actividad se preseleccionan todos los empleados. Se evita el solapamiento entre revisiones y jornada.
- Mantis 6317: Soluciono problema de sincronización de contactos en la agenda telefónica.

# 23112702
- Mantis 6252: Al iniciar jornada solo se podrá seleccionar entre ayer y hoy, al finalizar hoy o mañana.

# 23112701
- Mantis 6281: No finalizaba actividad porque no se guardaba el estado de la ruta en caché.
- Mantis 6252: Desarrollo inicio/fin de jornada.

# 23111001
- 0006068: No cambia al modo navegación de Sygic en pestaña "Navegador".
```
- Actualizada la versión del navegador Sygic a 21.5.1.
- Añadido sonido cuando se sale de la ruta.
- Evitado que vuelva a enrutar al inicio de la ruta cuando ha iniciado la ruta optimizada.
```

# 23102601
- Mantis 6239. Solucionado bug que paraba el servicio de ubicaciones.

# 23101901
- Mantis 6210. Se añade fecha de inicio de turno y de actividad.

# 23080201
- 0005748: Procesado de contenedores por tag con Nivel Llenado (Tecno01)
  - Arreglo el fallo por el cual la fecha del sensor nivel de llenado no coincidía con la del procesado ni con la del TAG
  - De paso arreglo otros fallos que encuentro
- 0006050: Fallos aleatorios en los elementos procesados de la comparativa de ruta.
  - Busco elemento por tag corto en el caso de no encontrarlo como largo, si no no procesa.
- 0006069: La comparativa no tiene en cuenta todas las lecturas de tag para estimar procesados
  - Cuando EcoRutas recibe un TAG para procesar un elemento primero busca un elemento que tenga dicho tag, si no lo encuentra
  - entonces busca un elemento cuyo tag coincida con los ocho caracteres de la derecha del TAG recibido por si se trata de un
  - elemento con TAG corto.

# 23072501
- Corrección. 0006068: No cambia al modo navegación de Sygic en pestaña "Navegador".
```
Aunque estuviese asignado el navegador Sygic de rutas optimizadas, si la ruta iniciada no estaba marcada como optimizada (campo optimización), no se mostraba este tipo de navegador.

Se ha eliminado esta comprobación, para que siempre se muestre el tipo de navegador asignado al equipo, independientemente del campo definido en la propia ruta.
```

# 23071901
- Añadido sentry.
- 0006069: La comparativa no tiene en cuenta todas las lecturas de tag para estimar procesados.
```
- Se han añadido mensajes informativos para que el usuario pueda saber qué ha pasado al leer el TAG.
- Se han añadido mensajes de log.
```

# 23062101
- Mantis 0006052:  Ajustes UHF EcoSAT (autorizados por Ptoledo, reunión 12/06/2023)
  - Sólo se procesan elementos cuyo tipo de recogida coincida con el del vehículo.

# 23060601
- Corrección. 0005969: Faltan nombres de calles en elementos de EcoRutas, quitar boton otros dentro de actividad.
- Corrección. 0005954: Fallo a la hora de procesar por niveles de llenado. Se ha optimizado la recuperación de los datos de la ruta para que tarde menos tiempo y sea menos probable que aparezca el mensaje de "La aplicación no responde".

# 23040501
- Corrección. Mantis 0005904: Implantar MODO NOCHE en EcoRutas. Correcciones adicionales.
- Corrección. Mantis 0005929: Procesado de elemento mediante lectura RFID con Nivel llenado. La aplicación enviaba al servidor 2 eventos de procesado y 1 nivel de llenado. La fecha del nivel de llenado era igual a la del primer procesado, pero el servidor solo se quedaba con el último procesado, por lo que no podía vincular el procesado y el nivel de llenado recibido. Se evita que se envíen eventos repetidos de procesado.
# 23032901
- Mantis 0005904: Implantar MODO NOCHE en EcoRutas. Añadida obtención del amanecer y anochecer dinámicamente para activar el modo noche.
# 23032301
- Mantis 0005904: Implantar MODO NOCHE en EcoRutas. Revertidos cambios generales en la aplicación para el modo noche. Se ha dejado solo en el mapa y en navegador Here.
# 23031601
- Mantis 0005904: Implantar MODO NOCHE en EcoRutas.
# 23030901
### Correcciones
- Mantis 0005905: Añadir información de DESCRIPCIÓN ELEMENTO en EcoRutas. Página de ruta, pestaña elemento. Añadida la descripción del elemento. El resto de apartados ya estaban hechos.
- Mantis 0005759: Visualización de info en RUTAS INCIDENCIAS. Página de ruta, pestaña elementos. Añadido motivo de la incidencias en rutas de incidencias.
- Mantis 0005766: El equipo no procesa elementos de manera aleatoria. Gestión mensaje del servidor 500 (error interno). Antes no se controlaba y el paquete de envío se borraba localmente.
- Mantis 0005926: Las rutas de LV no se ajustan bien. Página de rutas. Ocultadas pestañas de elementos y navegación en rutas de limpieza viaria.
# 22111003
- Mantis 0005954: Fallo a la hora de procesar por niveles de llenado. Mejoras de rendimiento.
- Corrección. Página de listado de rutas. Ocultados datos de la ruta en la barra superior, al finalizar la ruta.

# 22111002
- Mantis 0005954: Fallo a la hora de procesar por niveles de llenado. Añadidos logs para poder encontrar la causa del fallo.
- Eliminado sentry.

# 22111001
- Corrección. Fallo diagnosis.

# 22102501
- Corrección. Revertido mantis Mantis 0005740. Al procesar 1 único elemento, aparecía la página de listados de elementos y tras procesar uno del listado, la página no se cerraba automáticamente.
- Corrección. Cuando había varios elementos en el rango establecido, se procesaba automáticamente el más cercano. Esto pasaba con el evento de alzada cuando no estaba activa la variable de niveles de llenado.

# 22102402
- Corrección. No se mostraban los elementos de la ruta. Se han eliminado mensajes de log que se hacían con el nuevo servicio de logs. En subprocesos no parece funcionar bien. Al imprimir algunas llamadas al servidor, parece que fallaba.

# 22102401
- Corrección. Procesado de elementos. Cuando existen varios elementos en el radio definido, no se muestra directamente la página de selección de nivel de llenado. Antes se debe seleccionar el elemento a procesar.
- Corrección. Procesado de elementos. Se estaban realizando procesados de elementos automáticamente en el evento de alzada. Error debido a los últimos cambios relacionados con el procesado por lectura de TAGs.

# 22101101
- Corrección. Revertido cambio anterior en una condición para mostrar u ocultar el botón de procesar.
- Página de ajustes. Botón de bluetooth siempre visible, sin restricciones de configuraciones de la intranet.
- Corrección. Limitada cantidad de acciones de procesamiento de elementos a 1 por segundo, para evitar que se abran varias pantallas de niveles de llenado.

# 22100501
- Corrección. Eliminada comprobación de permisos en el navegador Sygic que impedían que se iniciase.

# 22100301
- Corrección. Evitar que se cierre la pantalla de la ruta al procesar todos los elementos. Mantis 0005740.
- Corrección. Procesar un elemento al realizar una lectura de TAG en rutas con actividad de niveles de llenado. Mantis 0005740.

# 22092801
- Ajustes para el botón de conexión bluetooth
- Añadido navegador Here mediante variable de la intranet "tipo_navegador"
-- Mantis 0005686: Fallos en el Navegador HERE