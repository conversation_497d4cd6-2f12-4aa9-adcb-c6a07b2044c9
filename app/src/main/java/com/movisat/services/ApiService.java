package com.movisat.services;

import com.movisat.models.elementos.ElementosModel;
import com.movisat.models.elementos.GuidElementosModel;
import com.movisat.models.elementos.LecturasLlenadoModel;
import com.movisat.models.incidencias.IncidenciasAsociacionModel;
import com.movisat.models.incidencias.IncidenciasModeloModel;
import com.movisat.models.incidencias.IncidenciasMotivoModel;
import com.movisat.models.incidencias.IncidenciasTipoModel;
import com.movisat.models.mensajes.MensajesModel;
import com.movisat.models.mensajes.MensajesPredefinidosModel;
import com.movisat.models.modelos.ModelosModel;
import com.movisat.models.moviles.MovilesModel;
import com.movisat.models.reparaciones.ReparacionesModel;
import com.movisat.models.revisiones.RevisionesAtributosModel;
import com.movisat.models.revisiones.RevisionesModel;
import com.movisat.models.rutas.AreasModel;
import com.movisat.models.rutas.AsignacionesModel;
import com.movisat.models.rutas.ProcesadoPorActividadModel;
import com.movisat.models.rutas.RutasModel;
import com.movisat.models.rutas.SensoresModel;
import com.movisat.models.tags.TagsModel;
import com.movisat.models.trabajadores.CategoriaTrabajadoresModel;
import com.movisat.models.trabajadores.DescansosModel;
import com.movisat.models.trabajadores.TrabajadoresModel;
import com.movisat.models.voluminosos.VoluminososModel;
import com.movisat.viewmodels.general.ConfiguracionEcoRutasViewModel;
import com.movisat.viewmodels.general.ConfirmarRecepcionViewModel;
import com.movisat.viewmodels.general.ParametrosListaIdsViewModel;
import com.movisat.viewmodels.general.ParametrosViewModel;
import com.movisat.viewmodels.general.TokenViewModel;
import com.movisat.viewmodels.incidencias.IncidenciaHViewModel;
import com.movisat.viewmodels.lecturallenado.LecturaLlenadoViewModel;
import com.movisat.viewmodels.posiciones.PosicionViewModel;
import com.movisat.viewmodels.reparaciones.ReparacionesHViewModel;
import com.movisat.viewmodels.revisiones.RevisionesViewModel;
import com.movisat.viewmodels.rutas.ElementoProcesadoViewModel;
import com.movisat.viewmodels.rutas.IdRutaHViewModel;
import com.movisat.viewmodels.rutas.RutasEnEquipoViewModel;
import com.movisat.viewmodels.rutas.RutasEstadoViewModel;
import com.movisat.viewmodels.rutas.RutasHViewModel;
import com.movisat.viewmodels.sensores.SensoresViewModel;
import com.movisat.viewmodels.trabajadores.DescansosHViewModel;
import com.movisat.viewmodels.trabajadores.JornadasViewModel;
import com.movisat.viewmodels.trabajadores.TurnosViewModel;
import com.movisat.viewmodels.voluminosos.VoluminosoRecogidaViewModel;

import java.util.List;

import retrofit.Call;
import retrofit.http.Body;
import retrofit.http.Field;
import retrofit.http.FormUrlEncoded;
import retrofit.http.GET;
import retrofit.http.POST;
import retrofit.http.Path;

/**
 * Interfaz que contiene las llamadas a la API
 */
public interface ApiService {

    //Token
    @FormUrlEncoded
    @POST("/token")
    Call<TokenViewModel> getToken(@Field("grant_type") String grantType,
                                  @Field("username") String username,
                                  @Field("password") String password,
                                  @Field("client_id") String clientId,
                                  @Field("client_secret") String clientSecret);

    @FormUrlEncoded
    @POST("/token")
    Call<TokenViewModel> getRefreshToken(@Field("grant_type") String grantType,
                                         @Field("client_id") String clientId,
                                         @Field("client_secret") String clientSecret,
                                         @Field("refresh_token") String refreshToken);

    //Moviles
    @POST("/moviles/fechamod")
    Call<List<MovilesModel>> getMoviles(@Body ParametrosViewModel params);

    //Elementos
    @POST("/moviles/elementos")
    Call<List<ElementosModel>> getElementosByIMEI(@Body ParametrosViewModel params);

    //Elementos por paquetes
    @POST("moviles/sincronizar/elementos")
    Call<List<ElementosModel>> getElementosByIMEIv2(@Body ParametrosViewModel params);

    @POST("/moviles/elementos/modelos")
    Call<List<ModelosModel>> getModelosElementosByIMEI(@Body ParametrosViewModel params);

    //Trabajadores
    @POST("api/v2/trabajadores/obsoletos/{imei}")
    Call<ParametrosListaIdsViewModel> getTrabajadoresABorrar(@Path("imei") String imei, @Body ParametrosListaIdsViewModel descansosEnEquipo);

    @POST("api/v2/trabajadores/asignados/{imei}")
    Call<List<TrabajadoresModel>> getTrabajadores(@Path("imei") String imei, @Body ParametrosListaIdsViewModel params);

    //Categorias de trabajadores
    @POST("/moviles/trabajadores/categorias")
    Call<List<CategoriaTrabajadoresModel>> getCategoriasTrabajadores(@Body ParametrosViewModel params);

    @POST("api/v2/descansos/obsoletos/{imei}")
    Call<ParametrosListaIdsViewModel> getDescansosABorrar(@Path("imei") String imei, @Body ParametrosListaIdsViewModel descansosEnEquipo);

    @POST("api/v2/descansos/asignados/{imei}")
    Call<List<DescansosModel>> getDescansos(@Path("imei") String imei, @Body ParametrosListaIdsViewModel params);

    @POST("api/v2/incidencias/tipo/obsoletas/{imei}")
    Call<ParametrosListaIdsViewModel> getIncidenciasTipoABorrar(@Path("imei") String imei,
                                                                @Body ParametrosListaIdsViewModel params);

    @POST("api/v2/incidencias/tipo/asignadas/{imei}")
    Call<List<IncidenciasTipoModel>> getIncidenciasTipo(@Path("imei") String imei, @Body ParametrosListaIdsViewModel params);

    @POST("/moviles/incidencias/motivos")
    Call<List<IncidenciasMotivoModel>> getMotivosIncidencias(@Body ParametrosViewModel params);

    @POST("/moviles/incidencias/modelos")
    Call<List<IncidenciasModeloModel>> getModelosIncidencias(@Body ParametrosViewModel params);

    @POST("/moviles/incidencias/insert/{imei}")
    Call<IncidenciaHViewModel> insertIncidenciaH(@Path("imei") String imei, @Body IncidenciaHViewModel params);

    //Rutas asignadas
    @POST("/moviles/rutas/asignadas/{imei}?version=2")
    Call<List<RutasModel>> getRutasBy(@Path("imei") String imei, @Body List<RutasEnEquipoViewModel> rutasEnEquipo);

    //Rutas asignadas
    @POST("/moviles/rutas/obsoletas/{imei}")
    Call<int[]> getRutasABorrarBy(@Path("imei") String imei, @Body List<RutasEnEquipoViewModel> rutasEnEquipo);

    // Confirma la recepción de rutas
    @POST("/moviles/rutas/recibidas/{imei}")
    Call<Void> confirmaRutasRecibidas(@Path("imei") String imei, @Body List<RutasEnEquipoViewModel> rutasRecibidas);

    //Mensajes
    @POST("/moviles/mensajes/servidor")
    Call<List<MensajesModel>> getMensajes(@Body ParametrosViewModel params);

    @POST("/moviles/mensajes/predefinidos")
    Call<List<MensajesPredefinidosModel>> getMensajesPredefinidos(@Body ParametrosViewModel params);

    @POST("/moviles/mensajes/insert")
    Call<MensajesModel> insertMensaje(@Body MensajesModel mensaje);

    @POST("/api/descansos/descansoh/insert")
    Call<DescansosHViewModel> insertDescansoH(@Body DescansosHViewModel params);

    @POST("/api/descansos/descansosh/insert")
    Call<Void> insertDescansosH(@Body List<DescansosHViewModel> params);

    @POST("/api/trabajadores/turnosh/insert")
    Call<Void> insertTurnosH(@Body List<TurnosViewModel> params);

    @POST("moviles/insert/trabajadores/jornada/{imei}")
    Call<Void> insertJornadasH(@Path("imei") String imei, @Body List<JornadasViewModel> params);

    @POST("/api/rutash/estado/ruta/save/v2")
    Call<RutasHViewModel> insertEstadoRutaV2(@Body RutasEstadoViewModel params);

    @POST("/api/rutash/estado/ruta/save")
    Call<RutasHViewModel> insertEstadoRuta(@Body RutasEstadoViewModel params);

    @POST("/api/rutash/procesar/elemento/v2")
    Call<Void> insertElementosProcesadosV2(@Body ElementoProcesadoViewModel params);

    @POST("/api/rutash/procesar/elemento")
    Call<Void> insertElementosProcesados(@Body ElementoProcesadoViewModel params);

    //Sensores
    @POST("/api/sensores/insert/sensores")
    Call<Void> insertSensores(@Body List<SensoresModel> params);

    //Revisiones
    @POST("api/v2/revisiones/atributos/get")
    Call<List<RevisionesAtributosModel>> getAtributosRevisiones(@Body ParametrosViewModel params);

    @POST("api/v2/revisiones/obsoletas/{imei}")
    Call<ParametrosListaIdsViewModel> getRevisionesABorrar(@Path("imei") String imei,
                                                           @Body ParametrosListaIdsViewModel params);

    @POST("api/v2/revisiones/asignadas/{imei}")
    Call<List<RevisionesModel>> getRevisiones(@Path("imei") String imei, @Body ParametrosListaIdsViewModel params);

    @POST("/api/v2/revisiones/insert/{imei}")
    Call<Void> insertRevision(@Path("imei") String imei, @Body List<RevisionesViewModel> params);


    //@GET("api/v2/{imei}/{recepcion}")
    //Call<Void> setConfirmacion(@Path("imei") String imei, @Path("recepcion") String recepcion);

    @POST("api/v2/confirmacion/recepcion")
    Call<Void> setConfirmacion(@Body ConfirmarRecepcionViewModel recepcion);


    //Lectura de Llenado
    @POST("api/sensores/insert/sensor/lectura/llenado")
    Call<Void> insertLecturaLlenado(@Body LecturaLlenadoViewModel lectura);

    //Lectura de Llenado
    @POST("api/sensores/get/lecturas/llenado")
    Call<List<LecturasLlenadoModel>> getLecturasLlenado(@Body ParametrosViewModel param);


    //Areas
    @POST("/moviles/zonas/ambito")
    Call<List<AreasModel>> getAreas(@Body ParametrosViewModel param);


    //Obtenemos la configuraciones de sensores y revisiones de la configuracion que se pasa como parametro
    @POST("api/flota/v2/configuracion/{idconfiguracion}")
    Call<ConfiguracionEcoRutasViewModel> getConfiguracion(@Path("idconfiguracion") int idConfiguracion,
                                                          @Body ParametrosViewModel params);

    //Obtenemos la relacion entre tipos de procesado y actividades de ruta
    @POST("api/rutas/procesado/actividades")
    Call<List<ProcesadoPorActividadModel>> getProcesadoPorActividad(@Body ParametrosViewModel param);

    @POST("api/v2/reparaciones/obsoletas/{imei}")
    Call<ParametrosListaIdsViewModel> getReparacionesABorrar(@Path("imei") String imei,
                                                             @Body ParametrosListaIdsViewModel params);

    @POST("api/v2/reparaciones/asignadas/{imei}")
    Call<List<ReparacionesModel>> getReparaciones(@Path("imei") String imei, @Body ParametrosListaIdsViewModel params);

    @POST("api/v2/reparaciones/reparacionesh/insert")
    Call<ReparacionesHViewModel> insertReparacionH(@Body ReparacionesHViewModel params);

    @POST("/moviles/insert/posiciones/{imei}/{modo}")
    Call<PosicionViewModel> insertPosiciones(@Path("imei") String imei, @Path("modo") String modo,
                                             @Body List<PosicionViewModel> params);

    //Sensor
    @POST("/api/sensores/insert/sensor")
    Call<Void> insertSensor(@Body SensoresViewModel params);

    //Obtener GUID para sincronizar elementos
    @POST("moviles/elementos/instantanea")
    Call<GuidElementosModel> getElementosGUID(@Body ParametrosViewModel params);

    //Obtener una página
    @POST("/moviles/sincronizar/elementos/reducidos")
    Call<List<ElementosModel>> getElementosByGUID(@Body ParametrosViewModel params);

    @POST("/moviles/tags")
    Call<List<TagsModel>> getTags(@Body ParametrosViewModel params);

    @POST("/moviles/rutas/asignaciones")
    Call<List<AsignacionesModel>> getAsignaciones(@Body ParametrosViewModel params);

    @GET("api/voluminosos/tipos/{idempresa}")
    Call<List<com.movisat.models.voluminosos.VoluminososTipoModel>> getVoluminososTipo(@Path("idempresa") int idEmpresa);

    @GET("api/voluminosos/modelos/{idempresa}")
    Call<List<VoluminososModel>> getVoluminososModelos(@Path("idempresa") int idEmpresa);

    @POST("/api/voluminosos/save/")
    Call<Void> setVoluminosoRecogida(@Body VoluminosoRecogidaViewModel params);

    @GET("moviles/incidencias/entradas/digitales/{imei}")
    Call<List<IncidenciasAsociacionModel>> getIncidenciasAsociacion(@Path("imei") String imei);

    @GET("moviles/bd/fecha")
    Call<String> getFechaBD();

    @GET("api/rutash/guid/{guidRutaH}")
    Call<IdRutaHViewModel> getIdRutaHByGuid(@Path("guidRutaH") String guidRutaH);
}
