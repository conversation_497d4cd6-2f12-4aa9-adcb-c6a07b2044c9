package com.movisat.activities;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.view.Menu;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.environment.Preferences;
import com.google.android.gms.maps.model.LatLng;
import com.movisat.activities.activacion.ActivacionActivity;
import com.movisat.activities.agenda.MyContactsActivity;
import com.movisat.activities.ajustes.AjustesActivity;
import com.movisat.activities.map.MapActivity;
import com.movisat.activities.mensajeria.MensajeriaActivity;
import com.movisat.activities.personal.PersonalActivity;
import com.movisat.activities.rutas.RutasActivity;
import com.movisat.activities.rutas.RutasFragmentActivity;
import com.movisat.activities.screenSlide.ScreenSlideActivity;
import com.movisat.application.RutasApplication;
import com.movisat.dao.rutas.RutasDAOProxy;
import com.movisat.dao.rutas.RutasElementosDAOProxy;
import com.movisat.events.map.onDayModeActivated;
import com.movisat.events.map.onNightModeActivated;
import com.movisat.events.moviles.OnNotifyAfterSincronizacionMoviles;
import com.movisat.events.sincronización.OnSincronizacionEnCurso;
import com.movisat.events.sincronización.OnSincronizacionFinalizada;
import com.movisat.handlers.BrightnessHandler;
import com.movisat.listeners.gps.MyLocationListener;
import com.movisat.log.Logg;
import com.movisat.log_provider_sentry.LoggProviderSentry;
import com.movisat.managers.ConfiguracionManager;
import com.movisat.managers.RutasManager;
import com.movisat.models.configuracion.ConfiguracionModel;
import com.movisat.services.SunsetInfoApi;
import com.movisat.utilities.AdminUtils;
import com.movisat.utilities.Utils;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

import cn.pedant.SweetAlert.SweetAlertDialog;
import de.greenrobot.event.EventBus;

public class MainActivity extends BaseActivity {
    private static MainActivity instance = null;
    RutasApplication app = null;
    private Button btnRutas;
    private Button btnPersonal;
    private Button btnMap;
    private Button btnMensajeria;
    private Button btnAjustes;
    private Button btnContacts;
    private LinearLayout layoutBtnContacts;
    private static final LatLng LAT_LONG_MADRID = new LatLng(40.4166359, -3.7038101);

    //genero el actionbar
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        try {
            getMenuInflater().inflate(R.menu.menu_main, menu);
            return true;
        } catch (Throwable e) {
            logger.WriteError(e);
            return false;
        }
    }

    public static MainActivity getInstance() {
        return instance;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        delayOrSetNightMode();
        instance = this;
        LocalBroadcastManager.getInstance(this).registerReceiver(onNotice, new IntentFilter("Msg"));
    }

    @SuppressLint("NewApi")
    @Override
    protected void setContentView() {
        try {
            setContentView(R.layout.activity_main);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void init() {
        try {
            instance = this;
            app = (RutasApplication) getApplication();

            //Creamos botones
            btnRutas = findViewById(R.id.btnRutas);
            //btnRutas.setEnabled(false);
            btnAjustes = findViewById(R.id.btnAjustes);
            btnMap = findViewById(R.id.btnMap);
            btnMensajeria = findViewById(R.id.btnMensajeria);
            btnPersonal = findViewById(R.id.btnPersonal);
            btnContacts = findViewById(R.id.btnContacts);
            layoutBtnContacts = findViewById(R.id.layoutBtnContacts);

            // Antes de nada checqueamos los permisos de la aplicación
            RutasApplication.getInstance().checkPermissions();

            // Se comprueba el estado de los servicios de Google Play
            app.checkGooglePlayServices();

            // Inicializamos aplicación
            if (ActivacionActivity.checkLicense(this)) {

                checkGpsActive();
                checkFirstSincro();

            } else {

                Intent i = new Intent(this, ActivacionActivity.class);
                startActivityForResult(i, ACTIVITY_ACTIVACION);
            }

            Map<String, Object> deviceContext = new HashMap<>();
            deviceContext.put("imei", RutasApplication.getInstance().getImei());
            deviceContext.put("empresa", RutasApplication.getInstance().getEmpresa());
            deviceContext.put("codigoMovil", RutasApplication.getInstance().getCodigoMovil());

            Logg.addProvider(new LoggProviderSentry(this, deviceContext));

        } catch (Throwable e) {
            logger.WriteError(e);
        }

    }

    private void delayOrSetNightMode() {
        try {
            LatLng location = MyLocationListener.getInstance().getUltimaPosicionGPSLatLng();
            if (location == null) {
                location = LAT_LONG_MADRID;
            }
            boolean hasInternet = RutasApplication.getInstance().hasNetwork();
            if (hasInternet) {
                SunsetInfoApi.execute(location, response -> {
                    if (response.isNotEmpty()) {
                        Preferences.nightHoursToday.set(response.prefsFormat());
                    }
                    auxDelaySetNightMode();
                });
                return;
            }
            auxDelaySetNightMode();

        } catch (Throwable e) {
            Logg.error(getSubtag(), "[delayOrSetNightMode] " + e.getMessage());
        }
    }

    private void auxDelaySetNightMode() {
        Handler handler = new Handler();
        BrightnessHandler brightnessHandler = new BrightnessHandler(this);

        Runnable runnable = new Runnable() {
            boolean setNightModeNeeded = Utils.isNowNightTime();

            @Override
            public void run() {
                try {
                    if (setNightModeNeeded) {
                        Preferences.isNightModeMap.set(true);
                        ConfiguracionModel brilloMinimo = ConfiguracionManager.getConfBy(RutasApplication.CLAVE_BRILLO_MINIMO, "0");
                        if (Integer.parseInt(brilloMinimo.valor) > 2) {
                            brightnessHandler.setBrillo(51);
                        } else {
                            brightnessHandler.setBrillo(21);
                        }
                        EventBus.getDefault().post(new onNightModeActivated());
                        setNightModeNeeded = false;
                        handler.postDelayed(this, calculateTimeForSunrise());
                        return;
                    }
                    Preferences.isNightModeMap.set(false);
                    brightnessHandler.setBrillo(255);
                    EventBus.getDefault().post(new onDayModeActivated());
                    setNightModeNeeded = true;
                    handler.postDelayed(this, calculateTimeForSunset());

                } catch (Throwable e) {
                    Logg.error(getSubtag(), "[auxDelaySetNightMode.run] " + e.getMessage());
                }
            }
        };
        handler.post(runnable);
    }

    private long calculateTimeForSunset() {
        Calendar now = Calendar.getInstance();
        Calendar later = Calendar.getInstance();
        String[] nightTimes = Preferences.nightHoursToday.get().split("-");
        String[] timesSunset = nightTimes[0].split(":");

        later.set(Calendar.HOUR_OF_DAY, Integer.parseInt(timesSunset[0]));
        later.set(Calendar.MINUTE, Integer.parseInt(timesSunset[1]));
        later.set(Calendar.SECOND, Integer.parseInt(timesSunset[2]));

        return later.getTimeInMillis() - now.getTimeInMillis();
    }

    private long calculateTimeForSunrise() {
        Calendar now = Calendar.getInstance();
        Calendar later = Calendar.getInstance();

        String[] nightTimes = Preferences.nightHoursToday.get().split("-");
        String[] timesSunrise = nightTimes[1].split(":");

        int hourNow = now.get(Calendar.HOUR_OF_DAY);
        int hourLater = Integer.parseInt(timesSunrise[0]);
        int hoursToAdd;

        if ((hourLater - hourNow) < 0) {
            hoursToAdd = (24 - hourNow) + hourLater;
        } else {
            hoursToAdd = hourLater - hourNow;
        }

        later.add(Calendar.HOUR_OF_DAY, hoursToAdd);
        later.set(Calendar.MINUTE, Integer.parseInt(timesSunrise[1]));
        later.set(Calendar.SECOND, Integer.parseInt(timesSunrise[2]));

        return later.getTimeInMillis() - now.getTimeInMillis();
    }

    public void checkGpsActive() {

        if (!MyLocationListener.isGPSActive()) {

            final SweetAlertDialog dlg = new SweetAlertDialog(MainActivity.getInstance(), SweetAlertDialog.WARNING_TYPE)
                    .setTitleText(MainActivity.getInstance().getString(R.string.atencion))
                    .setContentText(MainActivity.getInstance().getString(R.string.gps_desactivado))
                    .setConfirmText(MainActivity.getInstance().getString(R.string.si))
                    .setCancelText(MainActivity.getInstance().getString(R.string.no));

            dlg.setConfirmClickListener(sweetAlertDialog -> {
                try {
                    dlg.dismiss();
                    Intent intent = new Intent(
                            android.provider.Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                    MainActivity.getInstance()
                            .startActivity(intent);
                } catch (Throwable e) {
                }
            }).show();

            dlg.setCancelClickListener(sweetAlertDialog -> {
                try {
                    dlg.dismiss();
                } catch (Throwable e) {
                }
            }).show();
        }
    }

    public void checkFirstSincro() {

        try {

            app.initialize();

            if (app.isPrimeraSincronizacion()) {
                Intent i = new Intent(this, ScreenSlideActivity.class);
                startActivityForResult(i, ACTIVITY_SCREEN_SLIDE);
            }

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }


    @Override
    protected void setControls() {
        try {
            setTituloActionBar(R.string.title_ecosat_rutas);

            btnRutas.setOnClickListener(v -> {
                try {
                    int idRutaIniciada = new RutasDAOProxy().getRutaIniciadaId(app.getEmpresa());

                    Intent i;
                    // Si no hay ruta iniciada o no quedan elementos por procesar lanzo la actividad de rutas
                    if (idRutaIniciada == 0) {
                        i = new Intent(MainActivity.getInstance(), RutasActivity.class);
                    } else if (new RutasElementosDAOProxy().getNumElementosSinProcesarByRuta(idRutaIniciada, app.getEmpresa()) == 0) {
                        i = new Intent(MainActivity.getInstance(), RutasActivity.class);
                    } else {
                        // Si hay ruta iniciada, marco la ruta como seleccionada. Para poder filtrar el navegador.
                        // Al retroceder desde la página de listados de rutas, se quedaba la última ruta seleccionada guardada.
                        RutasManager.getInstance().setRutaIniciada();

                        // Hay ruta iniciada, lanzo la actividad de elementos de ruta
                        i = new Intent(MainActivity.getInstance(), RutasFragmentActivity.class);
                        i.putExtra("idRuta", idRutaIniciada);
                    }
                    startActivity(i);
                } catch (Throwable e) {
                    logger.WriteError(e);
                }
            });

            btnMap.setOnClickListener(v -> {
                try {
                    Intent i = new Intent(MainActivity.getInstance(), MapActivity.class);
                    startActivity(i);
                } catch (Throwable e) {
                    logger.WriteError(e);
                }
            });

            btnMensajeria.setOnClickListener(v -> {
                try {
                    Intent i = new Intent(MainActivity.getInstance(), MensajeriaActivity.class);
                    startActivity(i);
                } catch (Throwable e) {
                    logger.WriteError(e);
                }
            });

            btnPersonal.setOnClickListener(v -> {
                try {
                    Intent i = new Intent(MainActivity.getInstance(), PersonalActivity.class);
                    i.putExtra("desdeMenu", true);
                    startActivity(i);
                } catch (Throwable e) {
                    logger.WriteError(e);
                }
            });

            btnAjustes.setOnClickListener(v -> {
                try {
                    Intent i = new Intent(MainActivity.getInstance(), AjustesActivity.class);
                    MainActivity.getInstance().startActivity(i);
                } catch (Throwable e) {
                    logger.WriteError(e);
                }
            });

            boolean isCallingLibInitialized = RutasApplication.getInstance().isCallingLibInitialized();
            layoutBtnContacts.setVisibility(isCallingLibInitialized ? View.VISIBLE : View.GONE);
            btnContacts.setEnabled(isCallingLibInitialized);
            btnContacts.setOnClickListener(v -> {
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) return;
                try {
                    Intent i = new Intent(MainActivity.getInstance(), MyContactsActivity.class);
                    MainActivity.getInstance().startActivity(i);
                } catch (Throwable e) {
                    logger.WriteError(e);
                }
            });

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();

        try {
            //        assert getSupportActionBar()!=null;
            //        getSupportActionBar().invalidateOptionsMenu();
            //invalidateOptionsMenu();
            //

            // Si no se ha seleccionado el código de móvil intento asignarlo ahora
            if (app.getCodigoMovil() == 0)
                app.asignaCodigoMovil();

            if (app.getCodigoMovil() != 0) {
                btnRutas.setEnabled(true);
                btnMap.setEnabled(true);
                btnMensajeria.setEnabled(true);
                btnAjustes.setEnabled(true);
                btnPersonal.setEnabled(true);
            }

            if (app.isSincronizando())
                onEventMainThread(new OnSincronizacionEnCurso());

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
    }

    @Override
    public void onStop() {
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        try {
            super.onDestroy();
            if (ActivacionActivity.checkLicense(this)) {
                app.finalize();

            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {

        try {
            boolean res = super.onPrepareOptionsMenu(menu);
            menu.findItem(R.id.btnfinalizar).setVisible(true);
            menu.findItem(R.id.btnReiniciar).setVisible(true);
            if (app.getCodigoMovil() == 0) {
                menu.findItem(R.id.btnMensajeria).setVisible(false);
                menu.findItem(R.id.btnMapa).setVisible(false);
            }
            return res;
        } catch (Throwable e) {
            logger.WriteError(e);
            return false;
        }

    }

    @Override
    public void onBackPressed() {
        try {
//            DialogoSalir();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void onEventMainThread(OnSincronizacionEnCurso event) {
        try {
            super.showProgressBar(true);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void onEventMainThread(OnSincronizacionFinalizada event) {
        try {
            super.showProgressBar(false);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void onEventMainThread(OnNotifyAfterSincronizacionMoviles event) {
        try {
            if (app.getCodigoMovil() == 0)
                app.asignaCodigoMovil();

            if (app.getCodigoMovil() != 0) {
                btnRutas.setEnabled(true);
                btnMap.setEnabled(true);
                btnMensajeria.setEnabled(true);
                btnAjustes.setEnabled(true);
                btnPersonal.setEnabled(true);
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    private BroadcastReceiver onNotice = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            // String pack = intent.getStringExtra("package");
            String title = intent.getStringExtra("title");
            String text = intent.getStringExtra("text");
            //int id = intent.getIntExtra("icon",0);
            AdminUtils.setKioskModeEnabled(MainActivity.getInstance(), false);

        }
    };

}
