package com.movisat.activities.screenSlide;

import android.content.Context;
import android.view.animation.Interpolator;
import android.widget.Scroller;

/**
 * Created by sroca on 16/12/2015.
 */
public class VelocidadScroller extends Scroller {

    private int mDuration = 3000;

    public VelocidadScroller(Context context) {
        super(context);
    }

    public VelocidadScroller(Context context, Interpolator interpolator) {

            super(context, interpolator);

    }

    public VelocidadScroller(Context context, Interpolator interpolator, boolean flywheel) {
        super(context, interpolator, flywheel);
    }


    @Override
    public void startScroll(int startX, int startY, int dx, int dy, int duration) {
        // Ignora la duración recibida, utiliza una fija en su lugar
        super.startScroll(startX, startY, dx, dy, mDuration);
    }

    @Override
    public void startScroll(int startX, int startY, int dx, int dy) {
        // Ignora la duración recibida, utiliza una fija en su lugar
        super.startScroll(startX, startY, dx, dy, mDuration);
    }
}