package com.movisat.activities.screenSlide.vistas;

/**
 * Created by sroca on 14/12/2015.
 */

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.movisat.activities.R;


public class DescargadoFragment extends Fragment{

    int fragVal;

    public static DescargadoFragment init(int val) {
        DescargadoFragment ajustesFrag = new DescargadoFragment();
        // Supply val input as an argument.
        Bundle args = new Bundle();
        args.putInt("val", val);
        ajustesFrag.setArguments(args);
        return ajustesFrag;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        fragVal = getArguments() != null ? getArguments().getInt("val") : 1;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View layoutView = inflater.inflate(R.layout.fragment_rutas_descargando_pag11, container,
                false);
//        View tv = layoutView.findViewById(R.id.titulo_seccion);
//        ((TextView) tv).setText("Truiton Fragment #" + fragVal);
        return layoutView;
    }
}
