package com.movisat.activities.screenSlide;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

/**
 * Created by sroca on 16/12/2015.
 */
public class ViewPagerCustomizada extends androidx.viewpager.widget.ViewPager {



    public ViewPagerCustomizada(Context context) {
        super(context);

    }

    public ViewPagerCustomizada(Context context, AttributeSet atributo) {
        super(context, atributo);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent event) {
        // no permite deslizar para cambiar entre páginas
        return false;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        // no permite deslizar para cambiar entre páginas
        return false;
    }




}
