/*
 * Copyright 2012 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.movisat.activities.screenSlide;

import android.content.DialogInterface;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.ViewPager;
import androidx.appcompat.app.AlertDialog;
import android.text.InputType;
import android.widget.EditText;
import android.widget.Toast;

import com.movisat.activities.BaseActivity;
import com.movisat.activities.R;
import com.movisat.activities.MainActivity;
import com.movisat.activities.screenSlide.vistas.DescargadoFragment;
import com.movisat.activities.screenSlide.vistas.MapaFragment;
import com.movisat.activities.screenSlide.vistas.MensajeriaFragment;
import com.movisat.activities.screenSlide.vistas.RutaFragment;
import com.movisat.activities.splashScreen.SplashScreenActivity;
import com.movisat.application.RutasApplication;
import com.movisat.events.sincronización.OnNotifyFirstSincronizacion;
import com.movisat.managers.DracoManager;
import com.movisat.managers.log.LoggerManager;
import com.movisat.activities.screenSlide.vistas.AccionesFragment;
import com.movisat.activities.screenSlide.vistas.AjustesFragment;
import com.movisat.activities.screenSlide.vistas.IncidenciasFragment;
import com.movisat.activities.screenSlide.vistas.InformacionFragment;
import com.movisat.activities.screenSlide.vistas.PersonalDescansoFragment;
import com.movisat.activities.screenSlide.vistas.PersonalFragment;
import com.movisat.activities.screenSlide.vistas.PersonalIdentificacionFragment;
import com.movisat.activities.screenSlide.vistas.ProcesarFragment;
import com.movisat.activities.screenSlide.vistas.VehiculoFragment;
import com.movisat.utilities.AdminUtils;

import java.lang.reflect.Field;

import de.greenrobot.event.EventBus;

public class ScreenSlideActivity extends FragmentActivity {
    public static final int TIME = 1000 * 30;
    //int ITEM_ACTUAL = 0;
    int ITEM_ACTUAL = 1;
    private int NUMERO_PAGES = 13;
    Adapter adapter;
    ViewPager pager;
    private boolean isCargaFinished;
    public static final LoggerManager logger = LoggerManager.getInstance();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.fragment_pager);
        adapter = new Adapter(getSupportFragmentManager());
        pager = (ViewPager) findViewById(R.id.pager);
        pager.setAdapter(adapter);
        try {
            Field mScroller;
            mScroller = ViewPager.class.getDeclaredField("mScroller");
            mScroller.setAccessible(true);
            VelocidadScroller scroller = new VelocidadScroller(pager.getContext());
            mScroller.set(pager, scroller);
        } catch (NoSuchFieldException e) {
            LoggerManager.getInstance().WriteError(e);
        } catch (IllegalArgumentException e) {
            LoggerManager.getInstance().WriteError(e);
        } catch (IllegalAccessException e) {
            LoggerManager.getInstance().WriteError(e);
        }

        isCargaFinished = false;

        Thread timer = new Thread() {
            public void run() {
                try {
                    while (!isCargaFinished) {
                        sleep(TIME);
                        handler.sendMessage(new Message());

                    }

                } catch (InterruptedException e) {
                    LoggerManager.getInstance().WriteError(e);
                }

            }
        };
        timer.start();
    }

    public void onEventMainThread(OnNotifyFirstSincronizacion event) {
        try {
            if (!RutasApplication.getInstance().isPrimeraSincronizacion()) {
                finish();
            }
        } catch (Throwable e) {
            LoggerManager.getInstance().WriteError(e);
        }
    }

    @Override
    protected void onStart() {
        try {
            super.onStart();
            EventBus.getDefault().register(this);
        } catch (Throwable e) {
            LoggerManager.getInstance().WriteError(e);
        }
    }

    @Override
    protected void onStop() {
        try {
            super.onStop();
            EventBus.getDefault().unregister(this);
        } catch (Throwable e) {
            LoggerManager.getInstance().WriteError(e);
        }
    }

    @Override
    public void onBackPressed() {
        try {
            DialogoSalir();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void DialogoSalir() {

        try {
            // Se crea un cuadro de diálogo para la introducción de texto. Si el texto
            // introducido coincide con la contraseña de salida, se puede salir de la aplicación.
            AlertDialog.Builder builder = new AlertDialog.Builder(this);
            builder.setTitle("Introduzca el código de salida");

            // Se crea el EditText para introducir texto y se incluye en el diálogo
            final EditText input = new EditText(this);
            input.setInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_VARIATION_PASSWORD);
            builder.setView(input);

            // Set up the buttons
            builder.setPositiveButton("OK", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    String texto;
                    texto = input.getText().toString();
                    if (texto.equals(BaseActivity.EXIT_PASSWORD)) {
                        // Se sale del modo kiosko y de la aplicación
                        AdminUtils.setKioskModeEnabled(SplashScreenActivity.getInstance(), false);

                        // Aviso que el usuario a salido de la aplicación usando la contraseña
                        // para que el Watchdog no la vuelva a arrancar
                        DracoManager.setConfigBy("appStoppedByUser", "1");

                        SplashScreenActivity.getInstance().finish();
                        MainActivity.getInstance().finish();
                        finish();
                    } else if (texto.equals(BaseActivity.RESET_DATA_AND_RESTART_PASSWORD)
                            || texto.equals(BaseActivity.getResetCode() + "")) {
                        AdminUtils.setKioskModeEnabled(SplashScreenActivity.getInstance(), false);
                        AdminUtils.setHomeButtonEnabled(true);
                        // Elimino todos los datos de la aplicación y la reinicio
                        AdminUtils.resetData();
                        
                    } else {
                        Toast.makeText(getApplicationContext(), "Código incorrecto", Toast.LENGTH_SHORT).show();
                    }
                }
            });
            builder.setNegativeButton("Cancelar", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.cancel();
                }
            });

            builder.show();

        } catch (Throwable e) {

            e.printStackTrace();
        }
    }


    public class Adapter extends FragmentStatePagerAdapter {
        public Adapter(FragmentManager fragmentManager) {
            super(fragmentManager);
        }

        @Override
        public int getCount() {
            return NUMERO_PAGES;
        }

        @Override
        public Fragment getItem(int position) {

            try {
                switch (position) {
                    case 0:
//                        return VehiculoFragment.init(position);
                    case 1:
                        return AjustesFragment.init(position);
                    case 2:
                        return PersonalFragment.init(position);
                    case 3:
                        return PersonalIdentificacionFragment.init(position);
                    case 4:
                        return PersonalDescansoFragment.init(position);
                    case 5:
                        return MapaFragment.init(position);
                    case 6:
                        return MensajeriaFragment.init(position);
                    case 7:
                        return RutaFragment.init(position);
                    case 8:
                        return ProcesarFragment.init(position);
                    case 9:
                        return InformacionFragment.init(position);
                    case 10:
                        return DescargadoFragment.init(position);
                    case 11:
                        return IncidenciasFragment.init(position);
                    case 12:
                        return AccionesFragment.init(position);
                    default:
                        return VehiculoFragment.init(position);
                }
            } catch (Throwable e) {
                LoggerManager.getInstance().WriteError(e);
                return null;
            }
        }
    }

    private final Handler handler = new Handler() {

        @Override
        public void handleMessage(Message msg) {
            try {
                if (ITEM_ACTUAL < NUMERO_PAGES) {
                    pager.setCurrentItem(++ITEM_ACTUAL);
                } else {

                    //ITEM_ACTUAL = 0;
                    ITEM_ACTUAL = 1;
                    pager.setCurrentItem(ITEM_ACTUAL);
                }
            } catch (Throwable e) {
                LoggerManager.getInstance().WriteError(e);
            }

        }
    };

}
