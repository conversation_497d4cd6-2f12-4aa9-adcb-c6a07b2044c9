package com.movisat.activities;

import android.content.Context;
import android.service.notification.NotificationListenerService;
import android.service.notification.StatusBarNotification;
import android.text.TextUtils;

import com.movisat.managers.log.LoggerManager;
import com.movisat.utilities.AdminUtils;

public class NotificationListener extends NotificationListenerService {

    Context context;

    @Override
    public void onCreate() {

        super.onCreate();
        context = getApplicationContext();

    }
    @Override
    public void onNotificationPosted(StatusBarNotification sbn) {
        final String packageName = sbn.getPackageName();
        if (!TextUtils.isEmpty(packageName)) {
            // Do something
            LoggerManager.getInstance().WriteInfo(packageName);
            AdminUtils.setKioskModeEnabled(MainActivity.getInstance(), false);
        }
    }

    @Override
    public void onNotificationRemoved(StatusBarNotification sbn) {
        // Nothing to do
    }

}
