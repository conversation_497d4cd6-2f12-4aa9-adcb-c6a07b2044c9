package com.movisat.activities.incidencias;

import com.movisat.activities.MainActivity;
import com.movisat.application.RutasApplication;
import com.movisat.jobs.incidencias.InsertarIncidenciaHJob;
import com.movisat.listeners.gps.MyLocationListener;
import com.movisat.managers.RutasManager;
import com.movisat.managers.log.LoggerManager;
import com.movisat.models.rutas.RutasModel;
import com.movisat.utilities.ToastMensaje;
import com.movisat.viewmodels.incidencias.IncidenciaHViewModel;
import com.movisat.viewmodels.mapa.PosicionGPSViewModel;
import com.jobqueue.JobManager;

import java.util.ArrayList;
import java.util.Date;

public class AutoPhoto {
    public static int modeloInci;
    public static int tipoInci;
    public static int motivoInci;
    public static volatile long timerAutoSend = 0;
    public static volatile Thread threadSend = null;
    private static ArrayList<byte[]> listImagenes;

    public static void clearImages() {
        listImagenes = new ArrayList<>();
    }

    public static void addImage(byte[] image) {
        listImagenes.add(image);
    }

    public synchronized static void sendIncidence() {
        try {
            RutasModel rutaSelecionada = RutasManager.getInstance().getRutaIniciada();

            if (rutaSelecionada != null) {
                IncidenciaHViewModel incidenciaH = new IncidenciaHViewModel();

                incidenciaH.IdEmpresa = RutasApplication.getInstance().getEmpresa();
                incidenciaH.Incidencia = modeloInci;
                incidenciaH.Tipo = tipoInci;
                incidenciaH.Motivo = motivoInci;
                incidenciaH.Movil = RutasApplication.getInstance().getCodigoMovil();
                incidenciaH.Ruta = rutaSelecionada.Codigo;
                incidenciaH.Elemento = 0;
                incidenciaH.TipoElem = 0;
                incidenciaH.Fecha = new Date();

                PosicionGPSViewModel pos = MyLocationListener.getInstance().getUltimaPosicionGPS();
                if (pos != null) {
                    incidenciaH.Lat = pos.latitud;
                    incidenciaH.Lng = pos.longitud;
                }

                incidenciaH.Imagenes = listImagenes;

                LoggerManager.getInstance().WriteInfo("Incidencia desde AutoPhoto");

                RutasManager.getInstance().setInicioDeRuta(rutaSelecionada.Codigo, RutasApplication.getInstance().getEmpresa(), new Date());
                JobManager jobManager = RutasApplication.getInstance().getJobManager();
                jobManager.addJobInBackground(new InsertarIncidenciaHJob(incidenciaH));

                new ToastMensaje(MainActivity.getInstance()).show("Incidencia Introducida");

                modeloInci = 0;
                motivoInci = 0;
                tipoInci = 0;
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }
}
