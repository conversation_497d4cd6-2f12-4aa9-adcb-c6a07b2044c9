package com.movisat.activities.incidencias;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;

import com.google.android.gms.maps.model.LatLng;
import com.movisat.activities.R;
import com.movisat.application.RutasApplication;
import com.movisat.events.incidencias.OnNotifyBeforeSincronizacionIncidenciasTipo;
import com.movisat.activities.BaseActivity;
import com.movisat.adapters.incidencias.ListAdapterTiposIncidencias;
import com.movisat.dao.incidencias.IncidenciasTipoDAO;
import com.movisat.events.incidencias.OnNotifyAfterSincronizacionIncidenciasTipo;
import com.movisat.models.incidencias.IncidenciasTipoModel;

import java.util.ArrayList;

import de.greenrobot.event.EventBus;


public class IncidenciaTiposActivity extends BaseActivity {

    private static IncidenciaTiposActivity instance = null;

    //Constantes
    public final String ID_RUTA = "idRuta";
    public final String ID_ELEMENTO = "idElemento";
    public final String TIPO_ELEMENTO = "tipoElemento";
    public final String POSICION = "posicion";
    public static final String ACTIVITY_INCIDENCIA_ELEMENTO = "activityIncidenciaElemento";

    //Variables Genericas
    private int idRuta;
    private int idElemento;
    private int tipoElemento;
    private LatLng posicion;
    private int activityIncidenciaElemento = 0;

    private ListView listTiposIncidencias;
    private int modo;

    public static IncidenciaTiposActivity getInstance() {
        return instance;
    }


    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        try {
            // Inflate the menu; this adds items to the action bar if it is present.
            //TODO: Hasta que no se implemente la funcionalidad de buscar se deja el menu normal
            //getMenuInflater().inflate(R.menu.menu_buscar, menu);
            getMenuInflater().inflate(R.menu.menu_main, menu);
            return true;
        }catch (Throwable e){
            logger.WriteError(e);
            return false;
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            instance = this;
            setTituloActionBar(R.string.title_ecosat_incidencias_ruta);
            fillListTiposIncidencias();
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    @Override
    protected void onResume() {
        try {
            super.onResume();

            if (app.isSincronizando(RutasApplication.GROUP_JOB_INCIDENCIAS_TIPOS)) {
                EventBus.getDefault().post(new OnNotifyAfterSincronizacionIncidenciasTipo());
            }
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    @Override
    protected void setContentView() {setContentView(R.layout.activity_incid_rutas);}

    @Override
    protected void init()
    {
        try {
            TextView textViewTitulo = findViewById(R.id.textView_titulo);
            listTiposIncidencias = findViewById(R.id.ListView_listado_incidencias_rutas);

            //Pueden ser nulos idElemento y tipoElemento e idRuta
            Bundle bundle = getIntent().getExtras();
            idRuta = bundle.getInt(ID_RUTA);
            idElemento = bundle.getInt(ID_ELEMENTO);
            tipoElemento = bundle.getInt(TIPO_ELEMENTO);
            posicion = (LatLng) bundle.get(POSICION);
            activityIncidenciaElemento = bundle.getInt(ACTIVITY_INCIDENCIA_ELEMENTO);
            modo = (idElemento > 0) ? IncidenciasTipoModel.MODO_ELEMENTO : IncidenciasTipoModel.MODO_RUTA;
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    @Override
    protected void setControls()
    {
        try {
            listTiposIncidencias.setOnItemClickListener(new AdapterView.OnItemClickListener() {
                @Override
                public void onItemClick(AdapterView<?> pariente, View view, int posicion, long id) {
                    clickTipoIncidencia(view, posicion);
                }
            });
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    /**
     * Click sobre un elemento de la lista
     * @param view
     * @param posicion
     */
    private void clickTipoIncidencia(View view, int posicion) {
        try {
            IncidenciasTipoModel tipoIncidenciaSeleccionada = (IncidenciasTipoModel) listTiposIncidencias.getItemAtPosition(posicion);
            //Cambiamos el color del vehiculo seleccionado
            for (int j = 0; j < listTiposIncidencias.getChildCount(); j++) {
                listTiposIncidencias.getChildAt(j).setBackgroundColor(getResources().getColor(
                        R.color.background_material_light));

            }
            view.setBackgroundColor(getResources().getColor(
                    R.color.verde));

            Intent i = new Intent(IncidenciaTiposActivity.getInstance(), IncidenciaModelosActivity.class);
            i.putExtra(getString(R.string.incidencias_tipo_incidencia), tipoIncidenciaSeleccionada);
            i.putExtra(ID_RUTA, idRuta);
            i.putExtra(ID_ELEMENTO, idElemento);
            i.putExtra(TIPO_ELEMENTO, tipoElemento);
            i.putExtra(POSICION, this.posicion);
            startActivityForResult(i, ACTIVITY_MODELOS_INCIDENCIAS);
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    /**
     * Construye la lista de Tipos de Incidencias
     */
    private void fillListTiposIncidencias()
    {
        try {
            IncidenciasTipoDAO incidenciasTipoDAO = new IncidenciasTipoDAO();
            //ArrayList<IncidenciasTipoModel> tiposIncidencias = (ArrayList<IncidenciasTipoModel>) incidenciasTipoDAO.getAllIncidenciasTipoBy(app.getCodigoMovil());

            ArrayList<IncidenciasTipoModel> tiposIncidencias = (ArrayList<IncidenciasTipoModel>) incidenciasTipoDAO.getAllIncidenciasTipoByModo(app.getCodigoMovil(), modo);

            if (tiposIncidencias != null) {
                ListAdapter listAdapter = new ListAdapterTiposIncidencias(this, R.layout.entrada_1text, tiposIncidencias);
                listTiposIncidencias.setAdapter(listAdapter);
            }
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        try {
            super.onActivityResult(requestCode, resultCode, data);
            switch (requestCode) {
                case ACTIVITY_MODELOS_INCIDENCIAS:
                    if (resultCode == RESULT_OK) {
                        setResult(RESULT_OK);
                        finish();
                    }
                    break;
            }
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }
    public void onEventMainThread(OnNotifyAfterSincronizacionIncidenciasTipo event) {
        try {
            fillListTiposIncidencias();
            super.showProgressBar(false);
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    public void onEventMainThread(OnNotifyBeforeSincronizacionIncidenciasTipo event) {
        try {
            super.showProgressBar(true);
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    /**
     * Sobreescribimos este metodo para cuando se encuentre unicamente un elemento.
     * Y al insertar incidencia por elemento mas cercano no vuelva a la lista con ese
     * elemento mas cercano sino que vuelva a la lista de elementos del fragment
     * @param activity
     */
    @Override
    protected void onBotonAtrasClick(Activity activity) {
        try {
            if (activityIncidenciaElemento == 1) {
                setResult(RESULT_OK);
                finish();
            } else {
                activity.onBackPressed();
            }
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }
}


