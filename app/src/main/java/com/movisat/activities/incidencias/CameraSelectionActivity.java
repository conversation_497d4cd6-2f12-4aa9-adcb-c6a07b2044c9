package com.movisat.activities.incidencias;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.Menu;
import android.view.TextureView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.camera.core.AspectRatio;
import androidx.camera.core.CameraSelector;
import androidx.camera.core.Preview;
import androidx.camera.lifecycle.ProcessCameraProvider;
import androidx.camera.view.PreviewView;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.google.common.util.concurrent.ListenableFuture;
import com.movisat.activities.R;
import com.movisat.managers.ConfiguracionManager;
import com.movisat.managers.WifiApManager;
import com.movisat.models.configuracion.ConfiguracionModel;
import com.movisat.utilities.ToastMensaje;
import com.movisat.activities.wificamera.WifiCameraActivity;
import com.movisat.wificamera.CameraManager;
import com.movisat.wificamera.VideoSurfaceManager;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.concurrent.ExecutionException;

public class CameraSelectionActivity extends IncidenciaBaseActivity {
    private static final int REQUEST_CAMERA_PERMISSION = 100;

    private PreviewView internalPreviewView;
    private TextureView textureFrontCameraPreview;
    private TextureView textureSideCameraPreview;
    private ImageButton btnUpdateImage;

    private ConfiguracionModel confCamID;
    private ConfiguracionModel confCamID2;

    private ArrayList<CameraManager> cameraInstances = new ArrayList<>();
    private ArrayList<VideoSurfaceManager> videoSurfaceManagers = new ArrayList<>();

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.CAMERA}, REQUEST_CAMERA_PERMISSION);
        }
    }

    public boolean onCreateOptionsMenu(Menu menu) {
        try {
            getMenuInflater().inflate(R.menu.menu_main, menu);
            return true;
        } catch (Throwable e) {
            logger.WriteError(e);
            return false;
        }
    }

    @Override
    protected void setContentView() {
        setContentView(R.layout.activity_camera_selection);
    }

    @Override
    protected void init() {
        try {
            super.init();

            textureFrontCameraPreview = findViewById(R.id.textureFrontCameraPreview);
            textureSideCameraPreview = findViewById(R.id.textureSideCameraPreview);
            internalPreviewView = findViewById(R.id.textureInternalCameraPreview);
            btnUpdateImage = findViewById(R.id.btnActualizar);

            confCamID = ConfiguracionManager.getConfBy("dracoSensor.camID", "");
            confCamID2 = ConfiguracionManager.getConfBy("dracoSensor.camID2", "");
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void setControls() {
        textureFrontCameraPreview.setOnClickListener(v -> {
            if (confCamID == null || confCamID.valor.isEmpty()) {
                new ToastMensaje(this).show(getString(R.string.cam_not_configured));
                return;
            }
            // Iniciar cámara frontal WiFi
            openWifiCamera(1);
        });

        textureSideCameraPreview.setOnClickListener(v -> {
            if (confCamID2 == null || confCamID2.valor.isEmpty()) {
                new ToastMensaje(this).show(getString(R.string.cam_not_configured));
                return;
            }
            // Iniciar cámara trasera WiFi
            openWifiCamera(2);
        });

        btnUpdateImage.setOnClickListener(v -> {
            restartWifiAp();
            startWifiCameras();
        });
    }

    private void restartWifiAp() {
        try {
            WifiApManager wifiApManager = new WifiApManager(this);
            wifiApManager.restartWifiAp();
        } catch (InterruptedException e) {
            Log.e("CameraSelectionActivity", "Error al reiniciar la Zona Wi-Fi", e);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        resetTextureViews();
        startInternalCamera();
        startWifiCameras();
    }

    private void startWifiCameras() {
        captureWifiCameraPreview(confCamID.valor, textureFrontCameraPreview);
        captureWifiCameraPreview(confCamID2.valor, textureSideCameraPreview);
    }

    private void stopCameras() {
        try {
            for (CameraManager cameraInstance : cameraInstances) {
                cameraInstance.disconnect();
            }
            cameraInstances.clear();
            for (VideoSurfaceManager videoSurfaceManager : videoSurfaceManagers) {
                videoSurfaceManager.stop();
            }
            videoSurfaceManagers.clear();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    private void openWifiCamera(int cam) {
        Intent intent = new Intent(getApplicationContext(), WifiCameraActivity.class);
        intent.putExtra("camara", cam);
        intent.putExtra("auto", false);
        startActivityForResult(intent, IncidenciaFotoActivity.REQUEST_WIFI_CAMERA);
    }

    private synchronized void captureWifiCameraPreview(String cameraUid, TextureView texture) {
        if (cameraUid.isEmpty()) return;

        Log.i("CameraSelectionActivity", "Obteniendo imagen de la cámara WiFi: " + cameraUid);

        CameraManager cameraInstance = new CameraManager();
        cameraInstance.setUID(cameraUid);

        if (!cameraInstance.startClient()) {
            texture.setVisibility(View.GONE);
            new ToastMensaje(this).show(getString(R.string.cam_disconnected));
            return;
        }
        texture.setVisibility(View.VISIBLE);

        VideoSurfaceManager mVideoSurfaceManager = new VideoSurfaceManager(texture, cameraInstance.getFrameProvider(), 640, 320);

        videoSurfaceManagers.add(mVideoSurfaceManager);

        cameraInstance.startStreaming();

        cameraInstances.add(cameraInstance);

        Log.i("CameraSelectionActivity", "Imagen de la cámara WiFi reproduciéndose: " + cameraUid);
    }

    @Override
    protected void onPause() {
        super.onPause();
        stopCameras();
    }

    private void resetTextureViews() {
        ViewGroup frontParent = (ViewGroup) textureFrontCameraPreview.getParent();
        int frontIndex = frontParent.indexOfChild(textureFrontCameraPreview);
        ViewGroup sideParent = (ViewGroup) textureSideCameraPreview.getParent();
        int sideIndex = sideParent.indexOfChild(textureSideCameraPreview);

        // Elimina vistas antiguas
        frontParent.removeView(textureFrontCameraPreview);
        sideParent.removeView(textureSideCameraPreview);

        // Crea nuevas vistas
        textureFrontCameraPreview = new TextureView(this);
        textureFrontCameraPreview.setLayoutParams(new ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT));
        textureFrontCameraPreview.setId(R.id.textureFrontCameraPreview);

        textureSideCameraPreview = new TextureView(this);
        textureSideCameraPreview.setLayoutParams(new ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT));
        textureSideCameraPreview.setId(R.id.textureSideCameraPreview);

        // Vuelve a añadir al layout
        frontParent.addView(textureFrontCameraPreview, frontIndex);
        sideParent.addView(textureSideCameraPreview, sideIndex);

        // Reinicia controles
        setControls();
    }

    private void startInternalCamera() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED)
            return;

        internalPreviewView.setOnClickListener(v -> {
            Intent intent = new Intent(this, CamaraInternaActivity.class);
            startActivityForResult(intent, IncidenciaFotoActivity.REQUEST_IMAGE_CAPTURE);
        });

        ListenableFuture<ProcessCameraProvider> cameraProviderFuture = ProcessCameraProvider.getInstance(this);
        cameraProviderFuture.addListener(() -> {
            try {
                ProcessCameraProvider cameraProvider = cameraProviderFuture.get();
                Preview preview = new Preview.Builder()
                      .setTargetAspectRatio(AspectRatio.RATIO_16_9)
                      .build();
                preview.setSurfaceProvider(internalPreviewView.getSurfaceProvider());

                cameraProvider.unbindAll();
                cameraProvider.bindToLifecycle(
                      this,
                      CameraSelector.DEFAULT_BACK_CAMERA,
                      preview
                );
                internalPreviewView.setVisibility(PreviewView.VISIBLE);
            } catch (ExecutionException | InterruptedException e) {
                internalPreviewView.setVisibility(PreviewView.GONE);
                e.printStackTrace();
            }
        }, ContextCompat.getMainExecutor(this));
    }

    private void sendImageToIncidenciaFotoActivity(byte[] byteArray) {
        Intent resultIntent = new Intent();
        resultIntent.putExtra("capture", byteArray);
        setResult(RESULT_OK, resultIntent);
        finish();
    }

    // Podría estar en Util
    private byte[] compressBitmapToMaxSize(Bitmap bitmap, int maxFileSizeKB) {
        int quality = 100;
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, quality, stream);

        while (stream.toByteArray().length / 1024 > maxFileSizeKB && quality > 10) {
            stream.reset();
            quality -= 5;
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, stream);
        }

        return stream.toByteArray();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_CAMERA_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                startInternalCamera();
            } else {
                Toast.makeText(this, "Permiso de cámara denegado", Toast.LENGTH_SHORT).show();
            }
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        try {
            switch (requestCode) {
                case IncidenciaFotoActivity.REQUEST_IMAGE_CAPTURE:
                    if (resultCode == RESULT_OK) {
                        if (data != null) {
                            Uri imageUri = data.getData();
                            if (imageUri != null) {
                                try {
                                    InputStream inputStream = getContentResolver().openInputStream(imageUri);
                                    Bitmap imageBitmap = BitmapFactory.decodeStream(inputStream);
                                    byte[] byteArray = compressBitmapToMaxSize(imageBitmap, 400);

                                    File file = new File(imageUri.getPath());
                                    if (file.exists() && file.delete()) {
                                        Log.d("CameraSelectionActivity", "Archivo borrado exitosamente.");
                                    } else {
                                        Log.d("CameraSelectionActivity", "No se pudo borrar el archivo.");
                                    }
                                    sendImageToIncidenciaFotoActivity(byteArray);
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                            }
                        }
                    }
                    break;
                case IncidenciaFotoActivity.REQUEST_WIFI_CAMERA:
                    if (resultCode == RESULT_OK) {
                        byte[] captureBytes = data.getByteArrayExtra("capture");
                        sendImageToIncidenciaFotoActivity(captureBytes);
                    }
                    break;
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }
}
