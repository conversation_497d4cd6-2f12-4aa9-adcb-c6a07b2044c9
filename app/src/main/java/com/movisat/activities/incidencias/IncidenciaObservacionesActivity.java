package com.movisat.activities.incidencias;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;

import androidx.annotation.NonNull;

import com.google.android.gms.maps.model.LatLng;
import com.movisat.activities.R;
import com.movisat.models.incidencias.IncidenciasMotivoModel;
import com.movisat.utilities.SoftKeyboard;
import com.movisat.viewmodels.incidencias.IncidenciaHViewModel;
import com.movisat.viewmodels.mapa.PosicionGPSViewModel;


public class IncidenciaObservacionesActivity extends IncidenciaBaseActivity {

    private static final IncidenciaObservacionesActivity instance = null;

    private SoftKeyboard softKeyboard;

    private Button btnEnviar;
    private Button btnFoto;
    private Button btnHideKeyboard;

    //Observaciones
    private EditText editTextObservaciones;
    private String sObservaciones;

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        try {
            getMenuInflater().inflate(R.menu.menu_main, menu);
            menu.findItem(R.id.btnMapa).setVisible(false);
            return true;
        }catch (Throwable e){
            logger.WriteError(e);
            return false;
        }
    }

    public static IncidenciaObservacionesActivity getInstance() {
        return instance;
    }

    protected void setContentView()
    {
        setContentView(R.layout.activity_observaciones_rutas);
    }

    protected void init()
    {
        try {
            super.init();
            btnEnviar = findViewById(R.id.btnEnviar);
            btnFoto = findViewById(R.id.btnFoto);
            btnHideKeyboard = findViewById(R.id.btnHideKeyboard);
            editTextObservaciones = findViewById(R.id.editText_observaciones);

            Intent i = getIntent();
            setMotivoIncidenciaSeleccionado((IncidenciasMotivoModel) i.getSerializableExtra(getString(R.string.incidencias_motivo_incidencia)));
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }


    @Override
    protected void setControls()
    {
        try {
            //Boton Foto
            btnFoto.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    try {
                        Intent i = new Intent(IncidenciaMotivosActivity.getInstance(), IncidenciaFotoActivity.class);
                        i.putExtra(getString(R.string.incidencias_tipo_incidencia), getTipoIncidenciaSeleccionado());
                        i.putExtra(getString(R.string.incidencias_modelo_incidencia), getModeloIncidenciaSeleccionado());
                        i.putExtra(getString(R.string.incidencias_motivo_incidencia), getMotivoIncidenciaSeleccionado());
                        sObservaciones = editTextObservaciones.getText().toString();
                        i.putExtra(getString(R.string.incidencias_observaciones_incidencia), sObservaciones);
                        i.putExtra(ID_RUTA, getIdRuta());
                        i.putExtra(ID_ELEMENTO, getIdElemento());
                        i.putExtra(TIPO_ELEMENTO, getTipoElemento());
                        LatLng clickedPos = (LatLng) IncidenciaMotivosActivity.getInstance().getIntent().getExtras().get(POSICION);
                        i.putExtra(POSICION, clickedPos);
                        startActivityForResult(i, ACTIVITY_FOTO_INCIDENCIAS);
                    } catch (Throwable e) {
                        logger.WriteError(e);
                    }
                }
            });

            btnEnviar.setOnClickListener(new OnClickListenerEnviarIncidencia());

            // Se crea el objeto para manejar el SoftKeyboard y saber cuándo se muestra / oculta
            softKeyboard = new SoftKeyboard(this);
            softKeyboard.setOnKeyboardVisibilityChangeListener(new SoftKeyboard.OnKeyboardVisibilityChangeListener() {
                @Override
                public void onVisibilityChange(final boolean keyboardVisible) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                btnHideKeyboard.setVisibility(keyboardVisible ? View.VISIBLE : View.INVISIBLE);
                            } catch (Throwable e) {
                                logger.WriteError(e);
                            }
                        }
                    });
                }
            });

            // Listener para ocultar el teclado al pulsar el botón de ocultar
            btnHideKeyboard.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    try {
                        softKeyboard.hide();
                    } catch (Throwable e) {
                        logger.WriteError(e);
                    }
                }
            });

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }


    @Override
    public void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }


    /**
     * Construye la incidenciaH
     * @param posicionGPS
     * @return
     */
    @NonNull
    @Override
    protected IncidenciaHViewModel buildIncidenciaH(PosicionGPSViewModel posicionGPS) {
        try {
            super.buildIncidenciaH(posicionGPS);

            sObservaciones = editTextObservaciones.getText().toString();
            incidenciaH.Observaciones = sObservaciones;
        }catch (Throwable e){
            logger.WriteError(e);
        }

        return incidenciaH;
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
    }


    @Override
    public void finish() {
        if (softKeyboard != null)
            softKeyboard.detach();
        super.finish();
    }
}
