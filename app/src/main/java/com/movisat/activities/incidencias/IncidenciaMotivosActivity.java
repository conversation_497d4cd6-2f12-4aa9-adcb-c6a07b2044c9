package com.movisat.activities.incidencias;


import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ListAdapter;
import android.widget.ListView;

import com.google.android.gms.maps.model.LatLng;
import com.movisat.activities.R;
import com.movisat.adapters.genericos.ImageItem;
import com.movisat.adapters.incidencias.ListAdapterMotivosIncidencia;
import com.movisat.dao.incidencias.IncidenciasMotivoDAO;
import com.movisat.models.incidencias.IncidenciasMotivoModel;

import java.util.ArrayList;

import cn.pedant.SweetAlert.SweetAlertDialog;

public class IncidenciaMotivosActivity extends IncidenciaBaseActivity {

    private static IncidenciaMotivosActivity instance = null;

    private ListView listViewMotivosIncidencia;

    private Button btnObservaciones;
    private Button btnEnviar;
    private Button btnFoto;

    private Boolean aviso = false;

    public static IncidenciaMotivosActivity getInstance() {
        return instance;
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        try {
            getMenuInflater().inflate(R.menu.menu_main, menu);
            return true;
        } catch (Throwable e) {
            logger.WriteError(e);
            return false;
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            instance = this;

            setTituloActionBar(R.string.title_ecosat_rutas_incidencias_motivos);
            fillListMotivosIncidencia();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void onStart() {
        super.onStart();
    }

    public void onStop() {
        super.onStop();
    }

    protected void setContentView() {
        setContentView(R.layout.activity_incidencia_motivo_rutas);
    }

    protected void init() {
        try {
            super.init();

            btnObservaciones = findViewById(R.id.btnObservaciones);
            btnEnviar = findViewById(R.id.btnEnviar);
            btnFoto = findViewById(R.id.btnFoto);

            disableButtons();
            listViewMotivosIncidencia = findViewById(R.id.ListView_listado_motivos);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    protected void setControls() {
        //Elemento de la lista de motivos
        listViewMotivosIncidencia.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> pariente, View view, int posicion, long id) {
                clickMotivoIncidencias(view, posicion);
            }
        });

        //Boton Observaciones
        btnObservaciones.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    Intent i = createIntentForActivity(IncidenciaObservacionesActivity.class);
                    startActivityForResult(i, ACTIVITY_OBSERVACIONES_INCIDENCIAS);

                } catch (Throwable e) {
                    logger.WriteError(e);
                }
            }
        });

        //Boton Foto
        btnFoto.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    Intent i = createIntentForActivity(IncidenciaFotoActivity.class);
                    startActivityForResult(i, ACTIVITY_FOTO_INCIDENCIAS);
                } catch (Throwable e) {
                    logger.WriteError(e);
                }
            }
        });


        //Boton Enviar
        btnEnviar.setOnClickListener(new OnClickListenerEnviarIncidencia());
    }

    private Intent createIntentForActivity(Class<?> activityClass) {
        aviso = true;
        IncidenciaMotivosActivity currentActivity = IncidenciaMotivosActivity.getInstance();
        Intent i = new Intent(currentActivity, activityClass);
        i.putExtra(getString(R.string.incidencias_tipo_incidencia), getTipoIncidenciaSeleccionado());
        i.putExtra(getString(R.string.incidencias_modelo_incidencia), getModeloIncidenciaSeleccionado());
        i.putExtra(getString(R.string.incidencias_motivo_incidencia), getMotivoIncidenciaSeleccionado());
        i.putExtra(ID_RUTA, getIdRuta());
        i.putExtra(ID_ELEMENTO, getIdElemento());
        i.putExtra(TIPO_ELEMENTO, getTipoElemento());
        LatLng clickedPos = (LatLng) currentActivity.getIntent().getExtras().get(POSICION);
        i.putExtra(POSICION, clickedPos);
        return i;
    }

    @Override
    protected void onBotonAtrasClick(Activity activity) {
        try {
            if (aviso) {
                final SweetAlertDialog dlg = new SweetAlertDialog(instance, SweetAlertDialog.WARNING_TYPE)
                        .setTitleText(getString(R.string.atencion))
                        .setContentText(getString(R.string.incidencias_aviso_foto))
                        .setCancelText(getString(R.string.no))
                        .setConfirmText(getString(R.string.si));

                dlg.setCancelClickListener(new SweetAlertDialog.OnSweetClickListener() {
                    @Override
                    public void onClick(SweetAlertDialog sweetAlertDialog) {
                        dlg.dismiss();
                    }
                });

                dlg.setConfirmClickListener(new SweetAlertDialog.OnSweetClickListener() {
                    @Override
                    public void onClick(SweetAlertDialog sweetAlertDialog) {
                        dlg.dismiss();
                        finish();
                    }
                }).show();
            } else {
                finish();
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Click sobre un motivo de incidencia de la lista
     *
     * @param view
     * @param posicion
     */
    private void clickMotivoIncidencias(View view, int posicion) {
        try {
            //Seleccionamos el motivo de la incidencia
            activeButtons();
            setMotivoIncidenciaSeleccionado((IncidenciasMotivoModel) listViewMotivosIncidencia.getItemAtPosition(posicion));

            //Cambiamos el color del motivo seleccionado
            for (int j = 0; j < listViewMotivosIncidencia.getChildCount(); j++) {
                listViewMotivosIncidencia.getChildAt(j).setBackgroundColor(getResources().getColor(
                        R.color.background_material_light));

            }
            view.setBackgroundColor(getResources().getColor(
                    R.color.verde));
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }


    /**
     * Construye la lista de motivos de incidencia
     */
    private void fillListMotivosIncidencia() {
        try {
            IncidenciasMotivoDAO incidenciasMotivoDAO = new IncidenciasMotivoDAO();
            ArrayList<IncidenciasMotivoModel> motivosIncidencias = (ArrayList<IncidenciasMotivoModel>) incidenciasMotivoDAO.getIncidenciasMotivoBy
                    (app.getEmpresa(), getModeloIncidenciaSeleccionado().Codigo);

            if (motivosIncidencias != null) {
                ListAdapter listAdapter = new ListAdapterMotivosIncidencia(this, R.layout.entrada_1text, motivosIncidencias);
                listViewMotivosIncidencia.setAdapter(listAdapter);
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }


    /**
     * Activa los botones Enviar, Observaciones y Foto
     */
    private void activeButtons() {
        try {
            btnObservaciones.setEnabled(true);
            btnEnviar.setEnabled(true);
            btnFoto.setEnabled(true);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }


    /**
     * Deshabilita los botones Enviar, Observaciones y Foto
     */
    private void disableButtons() {
        btnFoto.setEnabled(false);
        btnObservaciones.setEnabled(false);
        btnEnviar.setEnabled(false);
    }

    protected void onDestroy() {
        super.onDestroy();
        fotos = new ArrayList<ImageItem>();
    }
}
