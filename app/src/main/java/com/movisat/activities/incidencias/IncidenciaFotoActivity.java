package com.movisat.activities.incidencias;

import android.content.Intent;
import android.content.res.AssetFileDescriptor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;

import androidx.annotation.NonNull;

import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;
import android.view.Menu;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.google.android.gms.maps.model.LatLng;
import com.movisat.activities.R;
import com.movisat.managers.ConfiguracionManager;
import com.movisat.models.configuracion.ConfiguracionModel;
import com.movisat.models.incidencias.IncidenciasMotivoModel;
import com.movisat.adapters.genericos.GridViewAdapter;
import com.movisat.adapters.genericos.ImageItem;
import com.movisat.utilities.Photo;
import com.movisat.utilities.ToastMensaje;
import com.movisat.utilities.Utils;
import com.movisat.viewmodels.incidencias.IncidenciaHViewModel;
import com.movisat.viewmodels.mapa.PosicionGPSViewModel;
import com.movisat.wificamera.StreamClient;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;

public class IncidenciaFotoActivity extends IncidenciaBaseActivity {

    private static IncidenciaFotoActivity instance = null;
    private final int cameraId = 1;

    //Observaciones
    private String sObservaciones = "";

    private Button btnEnviar;
    private Button btnGaleria;
    private Button btnCamara;
    private Button btnEliminar;

    //Galeria y array de fotos
    private GridView gridView;
    private GridViewAdapter gridAdapter;
    private static final int RESULT_GALLERY = 0;
    public static final int REQUEST_IMAGE_CAPTURE = 1;
    public static final int REQUEST_WIFI_CAMERA = 10;
    private static final int REQUEST_CAMERA_SELECTION = 100;
    private static final int NUM_FOTOS = 5;

    private ConfiguracionModel confCamID;
    private ConfiguracionModel confCamID2;
    String currentPhotoPath;

    private final StreamClient.OnConnectionChangedListener mOnConnectionChangedListener = null;

    public static IncidenciaFotoActivity getInstance() {
        return instance;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            instance = this;
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public boolean onCreateOptionsMenu(Menu menu) {
        try {
            //TODO: Hasta que no se implemente la funcionalidad de buscar se deja el menu normal
            //getMenuInflater().inflate(R.menu.menu_buscar, menu);
            getMenuInflater().inflate(R.menu.menu_main, menu);
            return true;
        } catch (Throwable e) {
            logger.WriteError(e);
            return false;
        }
    }

    @Override
    protected void setContentView() {
        setContentView(R.layout.activity_incidencias_foto);
    }

    @Override
    protected void init() {
        try {
            super.init();

            btnCamara = findViewById(R.id.btnCamara);
            btnGaleria = findViewById(R.id.btnGaleria);
            btnEliminar = findViewById(R.id.btnEliminar);
            btnEnviar = findViewById(R.id.btnEnviar);

            gridView = findViewById(R.id.gridView);
            gridAdapter = new GridViewAdapter(this, R.layout.grid_item_incidencias_foto, fotos);
            gridView.setAdapter(gridAdapter);

            Intent i = getIntent();
            setMotivoIncidenciaSeleccionado((IncidenciasMotivoModel) i.getSerializableExtra(getString(R.string.incidencias_motivo_incidencia)));
            sObservaciones = (String) i.getSerializableExtra(getString(R.string.incidencias_observaciones_incidencia));

            confCamID = ConfiguracionManager.getConfBy("dracoSensor.camID", "");
            confCamID2 = ConfiguracionManager.getConfBy("dracoSensor.camID2", "");

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void setControls() {

        // Selecciona la imagen
        gridView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            public void onItemClick(AdapterView<?> parent, View v, int position, long id) {
                try {
                    synchronized (this) {
                        LinearLayout layout = (LinearLayout) v;
                        ImageView view;
                        int count = layout.getChildCount();
                        for (int i = 0; i < count; i++) {
                            view = (ImageView) layout.getChildAt(i);

                            if (view.isSelected()) {
                                view.clearColorFilter();
                                view.invalidate();
                                view.setSelected(false);

                            } else {
                                view.setColorFilter(0xAA26FFFF);
                                view.invalidate();
                                view.setSelected(true);
                            }
                        }
                    }
                } catch (Throwable e) {
                    logger.WriteError(e);
                }
            }
        });

        //Boton Camara
        btnCamara.setOnClickListener(v -> {
            if (confCamID.valor.isEmpty() && confCamID2.valor.isEmpty()) {
                dispatchTakePictureIntent();
            } else {
                // Se lanza la activity CameraSelectionActivity para seleccionar la cámara
                Intent i = new Intent(getApplicationContext(), CameraSelectionActivity.class);
                i.putExtra(getString(R.string.incidencias_tipo_incidencia), getTipoIncidenciaSeleccionado());
                i.putExtra(getString(R.string.incidencias_modelo_incidencia), getModeloIncidenciaSeleccionado());
                i.putExtra(getString(R.string.incidencias_motivo_incidencia), getMotivoIncidenciaSeleccionado());
                i.putExtra(ID_RUTA, getIdRuta());
                i.putExtra(ID_ELEMENTO, getIdElemento());
                i.putExtra(TIPO_ELEMENTO, getTipoElemento());
                LatLng clickedPos = (LatLng) IncidenciaFotoActivity.getInstance().getIntent().getExtras().get(POSICION);
                i.putExtra(POSICION, clickedPos);

                startActivityForResult(i, REQUEST_CAMERA_SELECTION);
            }
        });

        //Boton Galeria
        btnGaleria.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                openGallery();
            }
        });

        //Boton Enviar
        btnEnviar.setOnClickListener(new OnClickListenerEnviarIncidencia());

        //Boton Eliminar
        btnEliminar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                eliminarFotos();
            }
        });
    }

    /**
     * Eliminar Fotos
     */
    private void eliminarFotos() {

        try {
            int count = gridView.getChildCount();

            if (count != 0) {
                ImageView img;

                for (int i = count - 1; i >= 0; i--) {
                    LinearLayout linearLayout = (LinearLayout) gridView.getChildAt(i);
                    img = (ImageView) linearLayout.getChildAt(0);
                    if (img != null && img.isSelected()) {
                        img.setSelected(false);
                        img.clearColorFilter();
                        fotos.remove(i);
                    }
                }
                gridAdapter.update(fotos);
            } else {
                String mensaje = getString(R.string.incidencias_foto_error_seleccion_imagen);
                new ToastMensaje(instance).show(mensaje, R.mipmap.camara);
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Abre la galeria del dispositivo
     */
    private void openGallery() {
        try {
            Intent galleryIntent = new Intent(Intent.ACTION_PICK, android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
            startActivityForResult(galleryIntent, RESULT_GALLERY);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Construye la incidenciaH
     *
     * @param posicionGPS
     * @return
     */
    @NonNull
    @Override
    protected IncidenciaHViewModel buildIncidenciaH(PosicionGPSViewModel posicionGPS) {
        try {
            super.buildIncidenciaH(posicionGPS);
            incidenciaH.Observaciones = sObservaciones;
        } catch (Throwable e) {
            logger.WriteError(e);
        }

        return incidenciaH;
    }

    private File createImageFile() throws IOException {
        // Create an image file name
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
        String imageFileName = "JPEG_" + timeStamp + "_";
        File storageDir = getExternalFilesDir(Environment.DIRECTORY_PICTURES);
        File image = File.createTempFile(
              imageFileName,  /* prefix */
              ".jpg",         /* suffix */
              storageDir      /* directory */
        );

        // Save a file: path for use with ACTION_VIEW intents
        currentPhotoPath = image.getAbsolutePath();
        return image;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        try {
            switch (requestCode) {
                // Insertamos foto desde la camara
                case REQUEST_CAMERA_SELECTION:

                    if (resultCode == RESULT_OK) {
                        byte[] captureBytes = data.getByteArrayExtra("capture");
                        Bitmap foto = BitmapFactory.decodeByteArray(captureBytes, 0, captureBytes.length);
                        Bitmap imageResize = Utils.ResizeImage(foto, 1280, 720);
                        fotos.add(new ImageItem(imageResize));
                        gridAdapter.update(fotos);
                    }
                    break;

                case REQUEST_WIFI_CAMERA:

                    if (resultCode == RESULT_OK) {
                        byte[] captureBytes = data.getByteArrayExtra("capture");
                        Bitmap foto = BitmapFactory.decodeByteArray(captureBytes, 0, captureBytes.length);
                        Bitmap imageResize = Utils.ResizeImage(foto, 800, 450);
                        fotos.add(new ImageItem(imageResize));
                        gridAdapter.update(fotos);
                    }
                    break;

                // Insertamos foto desde la galeria
                case Photo.REQUEST_IMAGE_CAPTURE:
                    if (data != null) {
                        if (resultCode == RESULT_OK) {
                            Uri imageUri = data.getData();
                            if (imageUri != null) {
                                try {
                                    InputStream inputStream = getContentResolver().openInputStream(imageUri);
                                    Bitmap imageBitmap = BitmapFactory.decodeStream(inputStream);
                                    byte[] byteArray = compressBitmapToMaxSize(imageBitmap, 500);

                                    File file = new File(imageUri.getPath());
                                    if (file.exists() && file.delete()) {
                                        Log.d("CameraSelectionActivity", "Archivo borrado exitosamente.");
                                    } else {
                                        Log.d("CameraSelectionActivity", "No se pudo borrar el archivo.");
                                    }
                                    Bitmap foto = BitmapFactory.decodeByteArray(byteArray, 0, byteArray.length);
                                    Bitmap imageResize = Utils.ResizeImage(foto, 1280, 720);
                                    fotos.add(new ImageItem(imageResize));
                                    gridAdapter.update(fotos);
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                            }
                        }
                    }
                    break;

                case RESULT_GALLERY: {
                    if (null != data) {
                        if (fotos.size() < NUM_FOTOS) {
                            Uri imageUri = data.getData();
                            if (imageUri != null) {
                                try {
                                    BitmapFactory.Options options = new BitmapFactory.Options();
                                    options.inSampleSize = 2;
                                    AssetFileDescriptor fileDescriptor = null;

                                    fileDescriptor = this.getContentResolver().openAssetFileDescriptor(imageUri, "r");

                                    Bitmap foto = BitmapFactory.decodeFileDescriptor(
                                          fileDescriptor.getFileDescriptor(), null, options);

                                    fotos.add(new ImageItem(foto));
                                    gridAdapter.update(fotos);

                                } catch (Throwable e) {
                                    logger.WriteError(e);
                                    e.printStackTrace();
                                }
                            }
                        } else {
                            new ToastMensaje(IncidenciaFotoActivity.this).show(getString(R.string.incidencias_sobrepasado_num_fotos));
                        }

                    }
                }
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    // Podría estar en Util
    private byte[] compressBitmapToMaxSize(Bitmap bitmap, int maxFileSizeKB) {
        int quality = 100;
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, quality, stream);

        while (stream.toByteArray().length / 1024 > maxFileSizeKB && quality > 10) {
            stream.reset();
            quality -= 5;
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, stream);
        }

        return stream.toByteArray();
    }

    private void dispatchTakePictureIntent() {
//        Intent takePictureIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
//        if (takePictureIntent.resolveActivity(getPackageManager()) != null) {
//            startActivityForResult(takePictureIntent, REQUEST_IMAGE_CAPTURE);
//        }
        Intent intent = new Intent(this, CamaraInternaActivity.class);
        startActivityForResult(intent, REQUEST_IMAGE_CAPTURE);
    }

    @Override
    public void finish() {
        super.finish();
    }
}