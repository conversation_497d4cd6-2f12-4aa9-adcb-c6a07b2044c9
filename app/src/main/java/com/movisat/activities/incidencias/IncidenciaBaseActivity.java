package com.movisat.activities.incidencias;

import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;

import com.google.android.gms.maps.model.LatLng;
import com.jobqueue.JobManager;
import com.movisat.activities.BaseActivity;
import com.movisat.activities.R;
import com.movisat.adapters.genericos.ImageItem;
import com.movisat.application.RutasApplication;
import com.movisat.dao.elementos.ElementosDAOProxy;
import com.movisat.dao.rutas.RutasElementosDAO;
import com.movisat.dao.rutas.RutasElementosDAOProxy;
import com.movisat.events.incidencias.OnChangeActiveIncidencia;
import com.movisat.factories.actions.ClickIncidenciaAction;
import com.movisat.jobs.incidencias.InsertarIncidenciaHJob;
import com.movisat.listeners.gps.MyLocationListener;
import com.movisat.managers.RutasManager;
import com.movisat.managers.log.LoggerManager;
import com.movisat.models.elementos.ElementosModel;
import com.movisat.models.incidencias.IncidenciasModel;
import com.movisat.models.incidencias.IncidenciasModeloModel;
import com.movisat.models.incidencias.IncidenciasMotivoModel;
import com.movisat.models.incidencias.IncidenciasTipoModel;
import com.movisat.utilities.ToastMensaje;
import com.movisat.viewmodels.incidencias.IncidenciaHViewModel;
import com.movisat.viewmodels.mapa.PosicionGPSViewModel;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Date;

import cn.pedant.SweetAlert.SweetAlertDialog;
import de.greenrobot.event.EventBus;


public class IncidenciaBaseActivity extends BaseActivity {

    private static IncidenciaBaseActivity instance = null;

    public final String ID_RUTA = "idRuta";
    public final String ID_ELEMENTO = "idElemento";
    public final String TIPO_ELEMENTO = "tipoElemento";
    public final String POSICION = "posicion";

    private int idRuta;
    private int idElemento;
    private int tipoElemento;
    private LatLng posicion = null;

    private IncidenciasMotivoModel motivoIncidenciaSeleccionado;
    private IncidenciasTipoModel tipoIncidenciaSeleccionado;
    private IncidenciasModeloModel modeloIncidenciaSeleccionado;

    PosicionGPSViewModel posicionGPS = null;

    IncidenciaHViewModel incidenciaH = null;
    protected static ArrayList<ImageItem> fotos = new ArrayList<ImageItem>();


    public static IncidenciaBaseActivity getInstance() {
        return instance;
    }

    protected void setContentView() {
    }

    protected void setControls() {
    }

    public void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            instance = this;
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    protected void init() {
        try {
            Intent i = getIntent();
            tipoIncidenciaSeleccionado = (IncidenciasTipoModel) i.getSerializableExtra(getString(R.string.incidencias_tipo_incidencia));
            modeloIncidenciaSeleccionado = (IncidenciasModeloModel) i.getSerializableExtra(getString(R.string.incidencias_modelo_incidencia));

            //Pueden ser nulos idElemento, tipoElemento e idRuta
            Bundle bundle = getIntent().getExtras();
            idRuta = bundle.getInt(ID_RUTA);
            idElemento = bundle.getInt(ID_ELEMENTO);
            tipoElemento = bundle.getInt(TIPO_ELEMENTO);
            posicion = (LatLng) bundle.get(POSICION);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }


    /**
     * Clase que implementa el listener del boton Enviar
     */
    public class OnClickListenerEnviarIncidencia implements View.OnClickListener {

        @Override
        public void onClick(View v) {
            try {
                // 05/9/2017 AO: Lo quito a petición de soporte (mantis: 3350)
                //posicionGPS = MyLocationListener.getInstance().getUltimaPosicionGPS();

                // Si se ha pinchado en el mapa cojo las coordenadas del punto sobre el que se ha pinchado
                // de lo contrario cojo la última posición posición GPS.
                // Cuando se trata de una incidnecia sobre elemento también cojo la posición sobre la
                // que se ha pinchado
                if (posicion != null || idElemento > 0) {
                    if (posicion != null)
                        // Siempre se coge la posición sobe la que se ha pulsado sobre el mapa
                        posicionGPS = new PosicionGPSViewModel(null, posicion.latitude, posicion.longitude, 0, 0, 0, 0, new Date());
                    else {
                        // Ponemos la posición del elemento
                        ElementosDAOProxy elementosDAO = new ElementosDAOProxy();
                        ElementosModel elem = elementosDAO.getElemento(idElemento, app.getEmpresa());
                        if (elem != null)
                            posicionGPS = new PosicionGPSViewModel(null, elem.Lat, elem.Lng, 0, 0, 0, 0, new Date());
                    }
                } else
                    posicionGPS = MyLocationListener.getInstance().getUltimaPosicionGPS();

                if (motivoIncidenciaSeleccionado != null) {

                    if (posicionGPS != null) {

                        //Se construye la incidencia
                        incidenciaH = buildIncidenciaH(posicionGPS);

                        //Se construye el mensaje
                        String mensaje = "";
                        mensaje = buildMensaje();

                        final SweetAlertDialog dlg = new SweetAlertDialog(IncidenciaBaseActivity.this, SweetAlertDialog.WARNING_TYPE)
                                .setTitleText(getString(R.string.atencion))
                                .setContentText(mensaje)
                                .setConfirmText(getString(R.string.si))
                                .setCancelText(getString(R.string.no));

                        dlg.setConfirmClickListener(new SweetAlertDialog.OnSweetClickListener() {
                            @Override
                            public void onClick(SweetAlertDialog sweetAlertDialog) {
                                dlg.dismiss();

                                // Si la posición es de hace más de 1 minuto aviso
                                if (new Date().getTime() - posicionGPS.fecha.getTime() > 60 * 1000) {
                                    new ToastMensaje(IncidenciaBaseActivity.this).
                                            show(getString(R.string.rutas_posicion_gps_antigua_params, 1), R.mipmap.gps);
                                }

                                insertIncidenciaH(posicionGPS);

                                setResult(RESULT_OK);

                                new ElementosDAOProxy().marcarIncidencia(app.getEmpresa(), idElemento);

                                finish();
                            }
                        }).show();
                    } else {
                        new ToastMensaje(IncidenciaBaseActivity.this).show(getString(R.string.rutas_no_hay_posicio_gps_valida), R.drawable.gps_85dp);
                    }
                }

            } catch (Throwable e) {
                logger.WriteError(e);
            }
        }
    }

    /**
     * Construye el mensaje para el dialogo
     *
     * @return
     */
    private String buildMensaje() {
        String mensaje;
        try {
            if (incidenciaH.Elemento > 0) {
                //Recuperamos la información del elemento
                ElementosDAOProxy elementosDAO = new ElementosDAOProxy();
                ElementosModel elemento = elementosDAO.getElemento(incidenciaH.Elemento, incidenciaH.IdEmpresa);

                //Campos que se muestran
                String nombre = elemento.Nombre != null ? elemento.Nombre : "";
                String matricula = elemento.Matricula != null ? elemento.Matricula : "";
                String calle = elemento.Calle != null ? elemento.Calle : "";

                if (incidenciaH.Imagenes.size() > 0)
                    mensaje = getString(R.string.incidencias_incidencia_desea_insertar_con_fotos, nombre, calle, matricula);
                else
                    mensaje = getString(R.string.incidencias_incidencia_desea_insertar, nombre, calle, matricula);
            } else {
                if (incidenciaH.Imagenes.size() > 0)
                    mensaje = getString(R.string.rutas_insertar_incidencia_con_fotos);
                else
                    mensaje = getString(R.string.rutas_insertar_incidencia);
            }
            return mensaje;
        } catch (Throwable e) {
            logger.WriteError(e);
            return null;
        }
    }


    /**
     * Construye y envia la incidencia al Job
     *
     * @param posicionGPS
     */
    private void insertIncidenciaH(PosicionGPSViewModel posicionGPS) {

        try {
            // Siempre se llama al manager de rutas por si fuese necesario iniciar ruta
            RutasManager.getInstance().setInicioDeRuta(idRuta, app.getEmpresa(), incidenciaH.Fecha);

            logger.WriteInfo("Incidencia desde IncidenciaBaseActivity");

            JobManager jobManager = RutasApplication.getInstance().getJobManager();
            jobManager.addJobInBackground(new InsertarIncidenciaHJob(incidenciaH));

            // Se crea el objeto de incidencia a partir del modelo para insertar en la DB
            IncidenciasModel incidenciaModel = new IncidenciasModel(0, incidenciaH.IdEmpresa,
                    incidenciaH.Movil, incidenciaH.Tipo, incidenciaH.Incidencia, incidenciaH.Motivo,
                    0, incidenciaH.Fecha, 0, incidenciaH.Lat, incidenciaH.Lng,
                    incidenciaH.Observaciones);

            // Si se trata de una incidencia sobre un elemento marco el registro
            // del elemento en la ruta
            if (incidenciaH.Elemento > 0) {
                RutasElementosDAO rutasElementosDAO = new RutasElementosDAOProxy();
                rutasElementosDAO.setIncidenciaRutaElemento(incidenciaH.Ruta, incidenciaH.IdEmpresa,
                        incidenciaH.Elemento, incidenciaH.Incidencia);

                // Se completa el objeto con el id del elemento
                incidenciaModel.IdElemento = incidenciaH.Elemento;
            }

            logger.WriteInfo(getString(R.string.incidencias_incidencia_a_insertar_detalle,
                    incidenciaH.Tipo, incidenciaH.Incidencia, incidenciaH.Fecha));

            // Se guarda la incidencia localmente
            incidenciaModel.save();

            logger.WriteInfo(getString(R.string.incidencias_incidencia_insertada_detalle,
                    incidenciaH.Tipo, incidenciaH.Incidencia, incidenciaH.Fecha));

            //Volvemos a resetear la accion de insertar incidencias sobre el mapa
            ClickIncidenciaAction.isActiveIncidencia = false;
            EventBus.getDefault().post(new OnChangeActiveIncidencia());
        } catch (Throwable e) {
            logger.WriteError(e);
        }

    }


    /**
     * Construye la incidenciaH
     *
     * @param posicionGPS
     * @return
     */
    @NonNull
    protected IncidenciaHViewModel buildIncidenciaH(PosicionGPSViewModel posicionGPS) {
        try {

            incidenciaH = new IncidenciaHViewModel();

            incidenciaH.IdEmpresa = app.getEmpresa();
            incidenciaH.Incidencia = modeloIncidenciaSeleccionado.Codigo;
            incidenciaH.Motivo = motivoIncidenciaSeleccionado.Codigo;
            incidenciaH.Tipo = modeloIncidenciaSeleccionado.Tipo;
            incidenciaH.Movil = app.getCodigoMovil();
            incidenciaH.Ruta = idRuta;
            incidenciaH.Elemento = idElemento;
            incidenciaH.TipoElem = tipoElemento;
            incidenciaH.Fecha = new Date();

            // Ponemos la posición del elemento
            ElementosDAOProxy elementosDAO = new ElementosDAOProxy();
            ElementosModel elem = elementosDAO.getElemento(idElemento, app.getEmpresa());
            if (elem != null) {
                incidenciaH.Lat = elem.Lat;
                incidenciaH.Lng = elem.Lng;
            } else if (posicionGPS != null) {
                incidenciaH.Lat = posicionGPS.latitud;
                incidenciaH.Lng = posicionGPS.longitud;
            }

            if (fotos != null)
                incidenciaH.Imagenes = buildImagenesIncidenciaH();


        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return incidenciaH;
    }


    /**
     * Construye el array de imagenes para insertarlas con la incidencia
     *
     * @return
     */
    private ArrayList<byte[]> buildImagenesIncidenciaH() {

        ArrayList<byte[]> listImagenes = new ArrayList<byte[]>();
        try {

            //Creamos primer paquete
            for (ImageItem foto : fotos) {
                ByteArrayOutputStream stream = new ByteArrayOutputStream();
                foto.getImage().compress(
                        Bitmap.CompressFormat.JPEG, 80,
                        stream);
                byte[] byteArray = stream.toByteArray();

                listImagenes.add(byteArray);
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return listImagenes;
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        try {
            super.onActivityResult(requestCode, resultCode, data);
            switch (requestCode) {
                case ACTIVITY_OBSERVACIONES_INCIDENCIAS:
                    if (resultCode == RESULT_OK) {
                        setResult(RESULT_OK);
                        finish();
                    }
                    break;
                case ACTIVITY_FOTO_INCIDENCIAS:
                    if (resultCode == RESULT_OK) {
                        setResult(RESULT_OK);
                        finish();
                    }
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    // Get y Set de los parametros
    public int getIdRuta() {
        return idRuta;
    }

    public void setIdRuta(int idRuta) {
        this.idRuta = idRuta;
    }

    public int getIdElemento() {
        return idElemento;
    }

    public void setIdElemento(int idElemento) {
        this.idElemento = idElemento;
    }

    public int getTipoElemento() {
        return tipoElemento;
    }

    public void setTipoElemento(int tipoElemento) {
        this.tipoElemento = tipoElemento;
    }

    public IncidenciasMotivoModel getMotivoIncidenciaSeleccionado() {
        return motivoIncidenciaSeleccionado;
    }

    public void setMotivoIncidenciaSeleccionado(IncidenciasMotivoModel motivoIncidenciaSeleccionado) {
        this.motivoIncidenciaSeleccionado = motivoIncidenciaSeleccionado;
    }

    public IncidenciasTipoModel getTipoIncidenciaSeleccionado() {
        return tipoIncidenciaSeleccionado;
    }

    public void setTipoIncidenciaSeleccionado(IncidenciasTipoModel tipoIncidenciaSeleccionado) {
        this.tipoIncidenciaSeleccionado = tipoIncidenciaSeleccionado;
    }

    public IncidenciasModeloModel getModeloIncidenciaSeleccionado() {
        return modeloIncidenciaSeleccionado;
    }

    public void setModeloIncidenciaSeleccionado(IncidenciasModeloModel modeloIncidenciaSeleccionado) {
        this.modeloIncidenciaSeleccionado = modeloIncidenciaSeleccionado;
    }
}
