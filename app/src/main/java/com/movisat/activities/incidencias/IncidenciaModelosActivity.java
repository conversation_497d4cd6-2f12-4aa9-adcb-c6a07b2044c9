package com.movisat.activities.incidencias;


import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ListView;
import android.widget.TextView;

import com.google.android.gms.maps.model.LatLng;
import com.movisat.activities.R;
import com.movisat.adapters.genericos.ListaAdapter;
import com.movisat.application.RutasApplication;
import com.movisat.dao.incidencias.IncidenciasModeloDAO;
import com.movisat.models.incidencias.IncidenciasModeloModel;
import com.jobqueue.JobManager;
import com.movisat.activities.BaseActivity;
import com.movisat.adapters.incidencias.ListAdapterModelosIncidencias;
import com.movisat.events.incidencias.OnNotifyAfterSincronizacionIncidenciasModelo;
import com.movisat.events.incidencias.OnNotifyBeforeSincronizacionIncidenciasModelo;
import com.movisat.models.incidencias.IncidenciasTipoModel;

import java.util.ArrayList;

import de.greenrobot.event.EventBus;

public class IncidenciaModelosActivity extends BaseActivity {

    //Job Manager
    private JobManager jobManager;

    private static IncidenciaModelosActivity instance = null;

    private ListView listViewModelosIncidencias;
    private IncidenciasTipoModel tipoIncidenciaSeleccionado;

    public final String ID_RUTA = "idRuta";
    public final String ID_ELEMENTO = "idElemento";
    public final String TIPO_ELEMENTO = "tipoElemento";
    public final String POSICION = "posicion";

    private int idRuta;
    private int idElemento;
    private int tipoElemento;
    private LatLng posicion;

    public static IncidenciaModelosActivity getInstance() {
        return instance;
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        try {
            // Inflate the menu; this adds items to the action bar if it is present.
            //TODO: Hasta que no se implemente la funcionalidad de buscar se deja el menu normal
            //getMenuInflater().inflate(R.menu.menu_buscar, menu);
            getMenuInflater().inflate(R.menu.menu_main, menu);
            return true;
        }catch(Throwable e){
            logger.WriteError(e);
            return false;
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            instance = this;
            setTituloActionBar(R.string.title_ecosat_rutas_incidencias_modelos);
            fillListViewModelosIncidencias();
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    @Override
    protected void onResume() {
        try {
            super.onResume();
            if (app.isSincronizando(RutasApplication.GROUP_JOB_INCIDENCIAS_TIPOS)) {
                EventBus.getDefault().post(new OnNotifyAfterSincronizacionIncidenciasModelo());
            }
        }catch(Throwable e){
            logger.WriteError(e);
        }
    }

    protected void setContentView() { setContentView(R.layout.activity_incidencia_modelo_rutas); }

    protected void init()
    {
        try {
            listViewModelosIncidencias = findViewById(R.id.ListView_listado_modelos);
            TextView textViewTitulo = findViewById(R.id.textView_titulo);
            Intent i = getIntent();
            tipoIncidenciaSeleccionado = (IncidenciasTipoModel) i.getSerializableExtra(getString(R.string.incidencias_tipo_incidencia));

            //Pueden ser nulos idElemento y tipoElemento
            Bundle bundle = getIntent().getExtras();
            idRuta = bundle.getInt(ID_RUTA);
            idElemento = bundle.getInt(ID_ELEMENTO);
            tipoElemento = bundle.getInt(TIPO_ELEMENTO);
            posicion = (LatLng) bundle.get(POSICION);
        }catch(Throwable e){
            logger.WriteError(e);
        }
    }

    protected void setControls()
    {
        listViewModelosIncidencias.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> pariente, View view, int posicion, long id) {
                clickModeloIncidencias(view, posicion);
            }
        });
    }

    /**
     * Evento al pulsar sobre un elemento de la lista de modelos
     * @param view
     * @param posicion
     */
    private void clickModeloIncidencias(View view, int posicion) {
        try{
            IncidenciasModeloModel modeloIncidenciaSeleccionado = (IncidenciasModeloModel) listViewModelosIncidencias.getItemAtPosition(posicion);
            //Cambiamos el color del vehiculo seleccionado
            for (int j = 0; j < listViewModelosIncidencias.getChildCount(); j++) {
                listViewModelosIncidencias.getChildAt(j).setBackgroundColor(getResources().getColor(
                        R.color.background_material_light));

            }
            view.setBackgroundColor(getResources().getColor(
                    R.color.verde));

            Intent i = new Intent(IncidenciaModelosActivity.getInstance(), IncidenciaMotivosActivity.class);
            i.putExtra(getString(R.string.incidencias_tipo_incidencia), tipoIncidenciaSeleccionado);
            i.putExtra(getString(R.string.incidencias_modelo_incidencia), modeloIncidenciaSeleccionado);
            i.putExtra(ID_RUTA, idRuta);
            i.putExtra(ID_ELEMENTO, idElemento);
            i.putExtra(TIPO_ELEMENTO, tipoElemento);
            i.putExtra(POSICION, this.posicion);

            startActivityForResult(i, ACTIVITY_MOTIVOS_INCIDENCIAS);

        }catch (Throwable e){
            logger.WriteError(e);
        }


    }


    /**
     * Construye la lista de modelos de incidencias
     */
    private void fillListViewModelosIncidencias()
    {
        try {
            IncidenciasModeloDAO incidenciasModeloDAO = new IncidenciasModeloDAO();
            ArrayList<IncidenciasModeloModel> modelosIncidencias = (ArrayList<IncidenciasModeloModel>) incidenciasModeloDAO.getAllIncidenciasModelBy(app.getEmpresa(), tipoIncidenciaSeleccionado.Id);

            if (modelosIncidencias != null) {
                ListaAdapter listaAdapter = new ListAdapterModelosIncidencias(this, R.layout.entrada_1text, modelosIncidencias);
                listViewModelosIncidencias.setAdapter(listaAdapter);
            }
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        try {
            super.onActivityResult(requestCode, resultCode, data);
            switch (requestCode) {
                case ACTIVITY_MOTIVOS_INCIDENCIAS:
                    if (resultCode == RESULT_OK) {
                        setResult(RESULT_OK);
                        finish();
                    }
                    break;
            }
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    public void onEventMainThread(OnNotifyAfterSincronizacionIncidenciasModelo event) {
        try {
            fillListViewModelosIncidencias();
            super.showProgressBar(false);
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    public void onEventMainThread(OnNotifyBeforeSincronizacionIncidenciasModelo event) {
        try {
            super.showProgressBar(true);
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

}
