package com.movisat.activities.incidencias;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.view.View;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.camera.core.AspectRatio;
import androidx.camera.core.CameraSelector;
import androidx.camera.core.ImageCapture;
import androidx.camera.core.ImageCaptureException;
import androidx.camera.core.Preview;
import androidx.camera.lifecycle.ProcessCameraProvider;
import androidx.camera.view.PreviewView;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.google.common.util.concurrent.ListenableFuture;
import com.movisat.activities.R;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.ExecutionException;

public class CamaraInternaActivity extends AppCompatActivity {

    private static final int REQUEST_CAMERA_PERMISSION = 100;
    private PreviewView previewView;
    private ImageCapture imageCapture;
    private ImageView imageView;
    private Uri photoUri;
    private ImageButton buttonAccept;
    private ImageButton buttonCancel;
    private ImageButton captureButton;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().getDecorView().setSystemUiVisibility(
              View.SYSTEM_UI_FLAG_IMMERSIVE
                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_FULLSCREEN
        );
        setContentView(R.layout.activity_camara_interna);

        previewView = findViewById(R.id.previewView);
        imageView = findViewById(R.id.imageView);
        captureButton = findViewById(R.id.captureButton);
        buttonAccept = findViewById(R.id.buttonAccept);
        buttonCancel = findViewById(R.id.buttonCancel);

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.CAMERA}, REQUEST_CAMERA_PERMISSION);
        } else {
            startCamera();
        }

        captureButton.setOnClickListener(v -> takePhoto());
        buttonAccept.setOnClickListener(v -> savePhoto());
        buttonCancel.setOnClickListener(v -> {
            if (photoUri == null) finish();
            resetPreview();
        });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_CAMERA_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                startCamera();
            } else {
                Toast.makeText(this, "Permiso de cámara denegado", Toast.LENGTH_SHORT).show();
                finish();
            }
        }
    }

    private void startCamera() {
        ListenableFuture<ProcessCameraProvider> cameraProviderFuture = ProcessCameraProvider.getInstance(this);
        cameraProviderFuture.addListener(() -> {
            try {
                ProcessCameraProvider cameraProvider = cameraProviderFuture.get();
                Preview preview = new Preview.Builder()
                      .setTargetAspectRatio(AspectRatio.RATIO_16_9)
                      .build();
                preview.setSurfaceProvider(previewView.getSurfaceProvider());

                imageCapture = new ImageCapture.Builder()
                      .setTargetAspectRatio(AspectRatio.RATIO_16_9)
                      .setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY)
                      .build();

                CameraSelector cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA;
                cameraProvider.unbindAll();
                cameraProvider.bindToLifecycle(
                      this,
                      cameraSelector,
                      preview,
                      imageCapture
                );
            } catch (ExecutionException | InterruptedException e) {
                e.printStackTrace();
            }
        }, ContextCompat.getMainExecutor(this));
    }

    private void takePhoto() {
        File photoFile = new File(getExternalFilesDir(Environment.DIRECTORY_PICTURES),
              new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date()) + ".jpg");

        ImageCapture.OutputFileOptions outputOptions = new ImageCapture.OutputFileOptions.Builder(photoFile).build();

        imageCapture.takePicture(outputOptions, ContextCompat.getMainExecutor(this), new ImageCapture.OnImageSavedCallback() {
            @Override
            public void onImageSaved(@NonNull ImageCapture.OutputFileResults outputFileResults) {
                photoUri = Uri.fromFile(photoFile);
                imageView.setImageURI(photoUri);
                imageView.setVisibility(View.VISIBLE);
                buttonAccept.setVisibility(View.VISIBLE);
                buttonCancel.setVisibility(View.VISIBLE);
                captureButton.setVisibility(View.GONE);
            }

            @Override
            public void onError(@NonNull ImageCaptureException exception) {
                exception.printStackTrace();
                Toast.makeText(CamaraInternaActivity.this, "Error al capturar la imagen", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void savePhoto() {
        Intent resultIntent = new Intent();
        resultIntent.setData(photoUri);
        setResult(RESULT_OK, resultIntent);
        finish();
    }

    private void resetPreview() {
        imageView.setVisibility(View.GONE);
        buttonAccept.setVisibility(View.GONE);
        buttonCancel.setVisibility(View.VISIBLE);
        captureButton.setVisibility(View.VISIBLE);
    }
}