package com.movisat.activities.incidencias;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ListView;

import com.google.android.gms.maps.model.LatLng;
import com.movisat.activities.R;
import com.movisat.adapters.genericos.ListaAdapter;
import com.jobqueue.JobManager;
import com.movisat.activities.BaseActivity;
import com.movisat.adapters.rutas.ListaAdapterElementosCercanos;
import com.movisat.dao.rutas.RutasDAO;
import com.movisat.dao.rutas.RutasDAOProxy;
import com.movisat.dao.rutas.RutasElementosDAO;
import com.movisat.dao.rutas.RutasElementosDAOProxy;
import com.movisat.fragments.RutasElementosFragment;
import com.movisat.listeners.gps.MyLocationListener;
import com.movisat.managers.ConfiguracionManager;
import com.movisat.managers.DracoManager;
import com.movisat.models.configuracion.ConfiguracionModel;
import com.movisat.models.rutas.RutasModel;
import com.movisat.utilities.ToastMensaje;
import com.movisat.viewmodels.elementos.ElementosCercanosViewModel;
import com.movisat.viewmodels.mapa.PosicionGPSViewModel;

import java.util.ArrayList;

/**
 * Actividad de elementos a procesar.
 */
public class IncidenciaElementoActivity extends BaseActivity {
    private static final int TIEMPO_MAXIMO_MINUTOS = 1;

    //region Variables
    private static IncidenciaElementoActivity instance = null;
    public static final String ID_RUTA = "idRuta";
    public static final String ID_ELEMENTO = "idElemento";
    public static final String TIPO_ELEMENTO = "tipoElemento";
    public static final String ACTIVITY_INCIDENCIA_ELEMENTO = "activityIncidenciaElemento";

    private Button btnIncidencia;
    private ListView listViewElementosCercanos = null;
    private ElementosCercanosViewModel elementoSeleccionado = null;
    private ArrayList<ElementosCercanosViewModel> listaElementosCercanos = null;
    private int idRutaActual;
    private PosicionGPSViewModel posicionGps;
    private RutasElementosDAO rutasElementosDAO;
    private int radioElementos = ElementosCercanosViewModel.RADIO_ELEMENTOS_DEFECTO;

    //endregion

    public static IncidenciaElementoActivity getInstance() {
        return instance;
    }

    //region Metodos heredados de la actividad maestra.
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        //TODO: Hasta que no se implemente la funcionalidad de buscar se deja el menu normal
        //getMenuInflater().inflate(R.menu.menu_buscar, menu);
        getMenuInflater().inflate(R.menu.menu_main, menu);
        return true;
    }

    @Override
    protected void init() {
        try {

            // Inicializo botones
            btnIncidencia = findViewById(R.id.btnIncidenciasElemento);
            listaElementosCercanos = new ArrayList<ElementosCercanosViewModel>();
            listViewElementosCercanos = findViewById(R.id.ListView_listado_elementos);
            JobManager jobManager = app.getJobManager();

            // Obtengo los datos de la ruta actual
            Bundle bundle = getIntent().getExtras();
            idRutaActual = bundle.getInt(RutasElementosFragment.ID_RUTA);

            RutasDAO rutasDAO = new RutasDAOProxy();
            RutasModel rutaActual = rutasDAO.getRutaBy(idRutaActual, app.getEmpresa(), true);

            // Obtengo el radio para procesar elementos
            ConfiguracionModel configuracionModel =
                    DracoManager.getConfigBy(ElementosCercanosViewModel.CONF_RADIO_ELEMENTOS, "30");
            if(configuracionModel != null && !configuracionModel.valor.isEmpty())
                radioElementos = Integer.parseInt(configuracionModel.valor);

            // Obtengo la última posición GPS
            posicionGps = MyLocationListener.getInstance().getUltimaPosicionGPS();

            if (posicionGps.fakePosition)
                radioElementos = 10000000; // Si se trata de una posición simulada pongo un radio de diez mil kilómetros (para demos)

            // Obtengo los elementos de ruta dentro del radio
            if (posicionGps != null && getElementosCercanos(posicionGps)) {

                // Seleeciono siempre por defecto el primer elemento de la lista
                // que es el más cerano
                elementoSeleccionado = listaElementosCercanos.get(0);

            } else {
                new ToastMensaje(this).show(getString(R.string.rutas_no_hay_elementos_cercano_radio,
                        radioElementos), R.mipmap.procesar);
                finish();
            }

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void setControls() {
        try {
            setTituloActionBar(R.string.title_ecosat_incidencia_elemento);

            // Botón incidencia elemento
            btnIncidencia.setOnClickListener(new IncidenciasOnClickListener());

            if (listaElementosCercanos != null) {
                ListaAdapter listaAdapter =
                        new ListaAdapterElementosCercanos(this, R.layout.entrada_imagen_2text_2textderecha,
                                listaElementosCercanos, elementoSeleccionado);
                listViewElementosCercanos.setAdapter(listaAdapter);
            }

            // Sólo se muestra la lista si hay más de un elemento sin procesar
            // demtro del radio
            if (listaElementosCercanos != null && listaElementosCercanos.size() == 1) {
                btnIncidencia.performClick();
            }

            // Elemento seleccionado de la lista
            listViewElementosCercanos.setOnItemClickListener(new AdapterView.OnItemClickListener() {
                @Override
                public void onItemClick(AdapterView<?> pariente, View view, int posicion, long id) {
                    elementoSeleccionado = (ElementosCercanosViewModel) listViewElementosCercanos.getItemAtPosition(posicion);
                    for (int j = 0; j < listViewElementosCercanos.getChildCount(); j++) {
                        listViewElementosCercanos.getChildAt(j).setBackgroundColor(getResources().getColor(
                                R.color.bright_foreground_material_dark));
                    }
                    view.setBackgroundColor(getResources().getColor(R.color.verde));
                }
            });

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void setContentView() {
        setContentView(R.layout.activity_elemento_incidencias);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        instance = this;

        try {
            if (posicionGps != null) {
                if (!isValidTiempoPosicion(posicionGps)) {
                    new ToastMensaje(this).show(getString(R.string.rutas_posicion_gps_antigua_params,
                            TIEMPO_MAXIMO_MINUTOS), R.mipmap.gps);
                }
            } else {
                new ToastMensaje(this).show(getString(R.string.rutas_no_hay_posicio_gps_valida), R.mipmap.gps);
                finish();
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    //endregion

    //region Metodos propios de la clase

    private boolean getElementosCercanos(PosicionGPSViewModel posicion) {
        boolean res = false;
        listaElementosCercanos = null;

        try {

            if (posicion != null) {
                // Obtengo la lista de elementos dentro del radio
                rutasElementosDAO = new RutasElementosDAOProxy();
                listaElementosCercanos = rutasElementosDAO.getElementosCercanos(
                        app.getEmpresa(),
                        idRutaActual, new LatLng(posicion.latitud, posicion.longitud), radioElementos, false);
            }

        } catch(Throwable e) {
            logger.WriteError(e);
        }

        return listaElementosCercanos != null;
    }

    /**
     * Comprobar si desde la ultima posición a la fecha actual ha pasado X tiempo.
     *
     * @param posicion
     * @return
     */
    private boolean isValidTiempoPosicion(PosicionGPSViewModel posicion) {
        boolean isValid = posicion != null && System.currentTimeMillis() -
                posicion.fecha.getTime() <= 60 * 1000 * TIEMPO_MAXIMO_MINUTOS;
        // Si la posición es de hace menos de 1 minuto la damos por buena
        return isValid;
    }

    //endregion

    public class IncidenciasOnClickListener implements View.OnClickListener {

        @Override
        public void onClick(View v) {
            Intent i = new Intent(IncidenciaElementoActivity.this, IncidenciaTiposActivity.class);
            i.putExtra(ID_RUTA, idRutaActual);
            i.putExtra(ID_ELEMENTO, elementoSeleccionado.getElemento().Codigo);
            i.putExtra(TIPO_ELEMENTO, elementoSeleccionado.getElemento().CodModelo);
            i.putExtra(ACTIVITY_INCIDENCIA_ELEMENTO, 1);
            startActivityForResult(i, ACTIVITY_TIPOS_INCIDENCIAS);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case ACTIVITY_TIPOS_INCIDENCIAS:
                if(resultCode == RESULT_OK) {
                    finish();
                }
                break;
        }
    }

}


