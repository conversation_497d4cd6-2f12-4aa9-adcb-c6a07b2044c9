package com.movisat.activities.rutas;

import android.content.Intent;
import android.nfc.Tag;
import android.os.Bundle;
import android.util.Log;
import android.view.Menu;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ListView;

import com.google.android.gms.maps.model.LatLng;
import com.movisat.activities.BaseActivity;
import com.movisat.activities.R;
import com.movisat.adapters.rutas.ListaAdapterElementosCercanos;
import com.movisat.dao.elementos.ElementosDAOProxy;
import com.movisat.dao.incidencias.IncidenciasRutaDAO;
import com.movisat.dao.rutas.ProcesadoPorActividadDAO;
import com.movisat.dao.rutas.RutasElementosDAO;
import com.movisat.dao.rutas.RutasElementosDAOProxy;
import com.movisat.events.rutas.OnTagProcesadoEvent;
import com.movisat.factories.actions.IProcesadoElementos;
import com.movisat.listeners.gps.MyLocationListener;
import com.movisat.log.Logg;
import com.movisat.managers.ConfiguracionManager;
import com.movisat.managers.DracoManager;
import com.movisat.managers.RutasManager;
import com.movisat.managers.SensoresManager;
import com.movisat.managers.TagManager;
import com.movisat.models.configuracion.ConfiguracionModel;
import com.movisat.models.elementos.ElementosModel;
import com.movisat.models.incidencias.IncidenciasRutaModel;
import com.movisat.models.rutas.ProcesadoPorActividadModel;
import com.movisat.models.rutas.RutasModel;
import com.movisat.models.rutas.SensoresModel;
import com.movisat.usecases.rutas.ProcesaElementosPorEntradaDigitalUseCase;
import com.movisat.utilities.ToastMensaje;
import com.movisat.viewmodels.elementos.ElementosCercanosViewModel;
import com.movisat.viewmodels.mapa.PosicionGPSViewModel;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import de.greenrobot.event.EventBus;

/**
 * Actividad de elementos a procesar.
 */
public class ElementoProcesarBaseActivity extends BaseActivity implements IProcesadoElementos {
    private final String TAG = getSubtag();

    public static ElementoProcesarBaseActivity instance = null;
    private static final int TIEMPO_MAXIMO_MINUTOS = 1;

    //region Variables
    private Button btnProcesar;
    private Button btnProcesarNElementos;
    private ListView listViewElementosCercanos = null;
    public ElementosCercanosViewModel elementoCercanoSeleccionado = null;
    public boolean procesadoPorTAG = false, fueraDeRuta = false, procesadoManual = false;
    public Date fechaProcesar = null;
    public ArrayList<ElementosCercanosViewModel> listaElementosCercanos = null;
    public int idRutaActual;
    public RutasModel rutaActual;
    private PosicionGPSViewModel posicionGps;
    private RutasElementosDAO rutasElementosDAO;
    private IncidenciasRutaDAO incidenciasRutaDAO;
    public int radioElementos = ElementosCercanosViewModel.RADIO_ELEMENTOS_DEFECTO;
    public int elementosJuntos = ElementosCercanosViewModel.ELEMENTOS_JUNTOS_DEFECTO;
    public static final String NUM_ELEMENTOS_PROCESAR = "elementosAProcesar";
    public static final String MAX_ELEMENTOS_PROCESAR = "maxElementos";
    public static final String ID_ELEMENTO_PROCESAR = "idElemento";
    public static final String ID_ELEMENTO_PROCESADO_X_TAG = "procesarPorTag";
    public static final String ID_ELEMENTO_PROCESADO_MANUAL = "procesarManual";
    public static final String ID_ELEMENTO_FUERA_DE_RUTA = "elementoFueraDeRuta";
    public static final String ID_FECHA_PROCESAR = "fechaProcesar";
    public static final String ID_NEAR_ELEMENTS_OR_INCIDENCES_COUNT = "nearElementsOrIncidencesCount";
    public static final String ID_ENTRADA_DIGITAL = "entradaDigital";
    private int entradaDigital = 0;
    public int numElementosAProcesar = 1;
    private ListaAdapterElementosCercanos listaAdapter = null;
    private boolean resetElementosSeleccionados = true;
    private boolean estaYaProcesado;

    //region Metodos heredados de la actividad maestra.
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        try {
            //TODO: Hasta que no se implemente la funcionalidad de buscar se deja el menu normal
            //getMenuInflater().inflate(R.menu.menu_buscar, menu);
            getMenuInflater().inflate(R.menu.menu_main, menu);
            return true;
        } catch (Throwable e) {
            Logg.error(TAG, "[onCreateOptionsMenu] " + e.getMessage());
            return false;
        }
    }

    @Override
    protected void onResume() {
        try {
            super.onResume();
            //Se resetean el numero de elementos seleccionados a procesar, exceptuando cuando se seleccionan varios.
            if (resetElementosSeleccionados) {
                ConfiguracionManager.save(NUM_ELEMENTOS_PROCESAR, "1");
            }
            resetElementosSeleccionados = true;
        } catch (Throwable e) {
            Logg.error(TAG, "[onResume] " + e.getMessage());
        }
    }

    @Override
    protected void init() {
        try {
            instance = this;
            estaYaProcesado = false;

            // Obtengo los datos de la ruta actual
            rutaActual = RutasManager.getInstance().getRutaIniciada();
            idRutaActual = rutaActual.Codigo;

            // Compruebo si se ha pasado el elemento a procesar
            Bundle bundle = getIntent().getExtras();

            if (bundle != null) {
                int idElemento = bundle.getInt(ID_ELEMENTO_PROCESAR, 0);
                int nearElementsOrIncidencesCount = bundle.getInt(ID_NEAR_ELEMENTS_OR_INCIDENCES_COUNT, 0);
                entradaDigital = bundle.getInt(ID_ENTRADA_DIGITAL, 0);
                procesadoPorTAG = bundle.getBoolean(ID_ELEMENTO_PROCESADO_X_TAG, false);
                procesadoManual = bundle.getBoolean(ID_ELEMENTO_PROCESADO_MANUAL, false);
                long time = bundle.getLong(ID_FECHA_PROCESAR, 0);
                if (time > 0) {
                    fechaProcesar = new Date(time);
                } else {
                    Logg.warning(TAG, "No se ha pasado la fecha de procesar");
                }
                fueraDeRuta = bundle.getBoolean(ID_ELEMENTO_FUERA_DE_RUTA, false);

                if (rutaActual != null && rutaActual.esRutaDeIncidencias()) {
                    IncidenciasRutaDAO incidenciasDao = new IncidenciasRutaDAO();
                    IncidenciasRutaModel incidenciaAProcesar = incidenciasDao.getIncidencia(idElemento, app.getEmpresa());
                    elementoCercanoSeleccionado = ElementosCercanosViewModel.deIncidencia(incidenciaAProcesar, 0);
                } else {

                    // Si se trata de un elemento en concreto o de un elemento fuera de ruta
                    // que se ha leido por TAG directamente lo proceso o saco el formulario
                    // que corresponda según el tipo de procesado
                    if (idElemento > 0 || fueraDeRuta) {
                        elementoCercanoSeleccionado = null;
                        // Si es un elemento fuera de ruta el Id del elemento será 0
                        if (!fueraDeRuta || procesadoPorTAG) {
                            ElementosDAOProxy elementosDAO = new ElementosDAOProxy();
                            ElementosModel elementoAProcesar = elementosDAO.getElemento(idElemento, app.getEmpresa());
                            elementoCercanoSeleccionado = ElementosCercanosViewModel.deElemento(null, elementoAProcesar, 0);
                        }
                        numElementosAProcesar = 1;
                        ConfiguracionManager.save(NUM_ELEMENTOS_PROCESAR, "" + numElementosAProcesar);
                        listaElementosCercanos = null;

                        // Cuando hay más de 1 elemento, no mostramos la pantalla de nivel de llenado.
                        // Ya que primero debe seleccionar el elemento que quiere procesar.
                        if (nearElementsOrIncidencesCount <= 1) {
                            onProcesar();
                            // Mantis 6380: Información de % llenado doble
                            // En setControls() se volvía a llamar a onProcesar(), se elimina y se finaliza aquí
                            estaYaProcesado = true;
                            finish();
                            return;
                        }
                    }
                }
            }

            // Inicializo botones
            btnProcesar = findViewById(R.id.btnProcesar);
            btnProcesarNElementos = findViewById(R.id.btnProcesarNElementos);
            listaElementosCercanos = new ArrayList<ElementosCercanosViewModel>();
            listViewElementosCercanos = findViewById(R.id.ListView_listado_procesar);

            // Obtengo la última posición GPS
            posicionGps = MyLocationListener.getInstance().getUltimaPosicionGPS();

            if (posicionGps != null) {
                if (!isValidTiempoPosicion(posicionGps)) {
                    new ToastMensaje(this).show(getString(R.string.rutas_posicion_gps_antigua_params,
                                    TIEMPO_MAXIMO_MINUTOS),
                            R.mipmap.gps);
                }
            } else {
                new ToastMensaje(this).show(getString(R.string.rutas_no_hay_posicio_gps_valida), R.drawable.gps_85dp);
                finish();
                return;
            }

            // Obtengo el radio para procesar elementos
            ConfiguracionModel configuracionModel =
                    DracoManager.getConfigBy(ElementosCercanosViewModel.CONF_RADIO_ELEMENTOS, "30");
            if (configuracionModel != null && !configuracionModel.valor.isEmpty())
                radioElementos = Integer.parseInt(configuracionModel.valor);

            if (posicionGps.fakePosition)
                radioElementos = 10000000; // Si se trata de una posición simulada pongo un radio de diez mil kilómetros (para demos)

            configuracionModel = ConfiguracionManager.getConfBy(ElementosCercanosViewModel.CONF_ELEMENTOS_JUNTOS);
            if (configuracionModel != null && !configuracionModel.valor.isEmpty()) {
                elementosJuntos = Integer.parseInt(configuracionModel.valor);
            }

            // Obtengo los elementos sin procesar dentro del radio
            if (posicionGps != null && getElementosCercanos(posicionGps, rutaActual)) {
                // Seleeciono siempre por defecto el primer elemento de la lista
                // que es el más cercano
                Logg.info(TAG, "[init] Elementos cercanos encontrados despues de ejecutar (getElementosCercanos): " + listaElementosCercanos.size());
                if (!procesadoPorTAG)
                    elementoCercanoSeleccionado = listaElementosCercanos.get(0);
            } else {
                // Mantis 0005740. Evitamos que se muestre el mensaje.
                if (!procesadoPorTAG)
                    new ToastMensaje(this).show(getString(R.string.rutas_no_hay_elementos_radio_params,
                            radioElementos), R.mipmap.procesar);
                finish();
            }

        } catch (Throwable e) {
            Logg.error(TAG, "[init] " + e.getMessage());
        }
    }

    @Override
    protected void setControls() {
        try {
            if (estaYaProcesado) {
                finish();
                return;
            }

            // Si se ha configurado que se procese automáticamente el elemento más cercano del radio
            if (DracoManager.getConfigBy("elemProcAuto", "0").valor.equals("1")) {
                // Se detecta en Sentry: [2024-06-19 08:32:39] [ERROR] [ElementoProcesarBaseActivity] [procesaElementos] Attempt to invoke virtual method 'boolean com.movisat.viewmodels.elementos.ElementosCercanosViewModel.esDeIncidencia()' on a null object reference
                // En Emalu - 86641905543338200
                if (elementoCercanoSeleccionado == null) {
                    Logg.error(TAG, "Al procesar automáticamente el elemento más cercano, elementoCercanoSeleccionado es nulo.");
                    if (listaElementosCercanos.size() > 0) {
                        elementoCercanoSeleccionado = listaElementosCercanos.get(0);
                    } else {
                        Logg.error(TAG, "No hay elementos cercanos para procesar.");
                        new ToastMensaje(this).show(getString(R.string.rutas_no_hay_elementos_radio_params,
                                radioElementos), R.mipmap.procesar);
                        finish();
                        return;
                    }
                }
                onProcesar();
                finish();
                return;
            }

            setTituloActionBar(R.string.title_ecosat_procesar_elemento);

            // Botón procesar
            btnProcesar.setOnClickListener(new OnClickListenerProcesarElementos());
            btnProcesarNElementos.setOnClickListener(new OnClickListenerNumeroElementosProcesar());

            ProcesadoPorActividadDAO procesadoPorActividadDAO = new ProcesadoPorActividadDAO();
            int tipoActividad = procesadoPorActividadDAO.
                    getTipoProcesadoBy(rutaActual.ActividadId);

            // Si la actividad de la ruta es de reparación no se pueden procesar
            // varios elementos juntos
            if (tipoActividad == ProcesadoPorActividadModel.REPARACION) {
                btnProcesarNElementos.setVisibility(View.GONE);
            } else {
                if (elementosJuntos < 1 || (listaElementosCercanos != null &&
                        listaElementosCercanos.size() < elementosJuntos)) {
                    btnProcesarNElementos.setVisibility(View.GONE);
                }
            }

            // Mantis 6380: Información de % llenado doble
            // En init() ya se llama a onProcesar(), es posible que se deba eleminar o directamente sea código muerto
            // Sólo se muestra la lista si hay más de un elemento sin procesar dentro del radio
            if (listaElementosCercanos != null && listaElementosCercanos.size() == 1) {
                onProcesar();
                finish();
            }

            if (listaElementosCercanos != null) {
                Logg.info(TAG, "[setControls] Elementos cercanos encontrados: " + listaElementosCercanos.size());
                listaAdapter = new ListaAdapterElementosCercanos(this,
                        R.layout.entrada_imagen_2text_2textderecha, listaElementosCercanos, elementoCercanoSeleccionado);
                listViewElementosCercanos.setAdapter(listaAdapter);

                // Elemento seleccionado de la lista
                listViewElementosCercanos.setOnItemClickListener(new AdapterView.OnItemClickListener() {
                    @Override
                    public void onItemClick(AdapterView<?> pariente, View view, int posicion, long id) {
                        elementoCercanoSeleccionado = (ElementosCercanosViewModel)
                                listViewElementosCercanos.getItemAtPosition(posicion);
                        listaAdapter.setElementoSeleccionado(elementoCercanoSeleccionado);

                        for (int j = 0; j < listViewElementosCercanos.getChildCount(); j++) {
                            listViewElementosCercanos.getChildAt(j).setBackgroundColor(getResources().getColor(
                                    R.color.bright_foreground_material_dark));
                        }
                        view.setBackgroundColor(getResources().getColor(R.color.verde));
                    }
                });
            }

        } catch (Throwable e) {
            Logg.error(TAG, "[setControls] " + e.getMessage());
        }
    }

    @Override
    protected void setContentView() {
        setContentView(R.layout.activity_elemento_procesar);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    //endregion

    //region Metodos propios de la clase

    private boolean getElementosCercanos(PosicionGPSViewModel posicion, RutasModel ruta) {
        boolean res = false;
        listaElementosCercanos = null;

        try {

            if (posicion != null && ruta != null) {
                if (ruta.esRutaDeIncidencias()) {
                    // Obtengo la lista de elementos sin procesar dentro del radio
                    incidenciasRutaDAO = new IncidenciasRutaDAO();
                    listaElementosCercanos = incidenciasRutaDAO.getIncidenciasCercanas(
                            app.getEmpresa(), idRutaActual, new LatLng(posicion.latitud,
                                    posicion.longitud), radioElementos, true);
                } else {
                    ProcesaElementosPorEntradaDigitalUseCase useCase = new ProcesaElementosPorEntradaDigitalUseCase();
                    ArrayList<ElementosCercanosViewModel> elementosParaProcesar = useCase.execute(
                            app.getEmpresa(),
                            idRutaActual,
                            new LatLng(posicion.latitud, posicion.longitud),
                            radioElementos,
                            true,
                            entradaDigital,
                            procesadoManual
                    );
                    if (listaElementosCercanos != null) {
                        listaElementosCercanos.clear();
                        listaElementosCercanos.addAll(elementosParaProcesar);
                    } else {
                        listaElementosCercanos = new ArrayList<>(elementosParaProcesar);
                    }
                    if (listaAdapter != null) {
                        listaAdapter.updateList(listaElementosCercanos);
                    }
                    Logg.info(TAG, "[getElementosCercanos] Elementos disponibles para procesar: " + elementosParaProcesar.size());
                }

            }

        } catch (Throwable e) {
            Logg.error(TAG, "[getElementosCercanos] " + e.getMessage());
        }

        return listaElementosCercanos != null && !listaElementosCercanos.isEmpty();
    }

    /**
     * Comprobar si desde la ultima posición a la fecha actual ha pasado X tiempo.
     *
     * @param posicion
     * @return
     */
    private boolean isValidTiempoPosicion(PosicionGPSViewModel posicion) {
        boolean isValid = false;
        try {
            // Si la posición es de hace menos de 1 minuto la damos por buena
            if (posicion != null && System.currentTimeMillis() -
                    posicion.fecha.getTime() <= 60 * 1000 * TIEMPO_MAXIMO_MINUTOS) {
                isValid = true;
            }
        } catch (Throwable e) {
            Logg.error(TAG, "[isValidTiempoPosicion] " + e.getMessage());
        }
        return isValid;
    }

    //endregion

    public class OnClickListenerProcesarElementos implements View.OnClickListener {

        @Override
        public void onClick(View v) {
            try {
                onProcesar();
            } catch (Throwable e) {
                Logg.error(TAG, "[OnClickListenerProcesarElementos] " + e.getMessage());
            }
        }
    }

    public class OnClickListenerNumeroElementosProcesar implements View.OnClickListener {
        @Override
        public void onClick(View v) {
            try {
                if (ElementoProcesarBaseActivity.instance.listaElementosCercanos != null) {
                    Intent i = new Intent(ElementoProcesarBaseActivity.this, NumeroElementosProcesarActivity.class);
                    i.putExtra(MAX_ELEMENTOS_PROCESAR, ElementoProcesarBaseActivity.instance.listaElementosCercanos.size());
                    startActivityForResult(i, BaseActivity.ACTIVITY_NUM_ELEMENTOS_PROCESAR);
                }
            } catch (Throwable e) {
                Logg.error(TAG, "[OnClickListenerNumeroElementosProcesar] " + e.getMessage());
            }
        }
    }

    public void procesaElementos(Date fecha, boolean quitar) {
        try {

            // Si el elemento se ha procesado por lectura de TAG la fecha
            // de procesado es la de la lectura del TAG
            if (procesadoPorTAG)
                fecha = fechaProcesar;

            if (procesadoManual) {
                Logg.info(TAG, "[procesaElementos] Procesado manual, se asigna la fecha actual");
                fecha = new Date();
            }

            // Si se ha configurado que se pueden procesar más de un elemento junto
            // proceso la cantidad que haya seleccionado el usuario por cercanía
            if (elementosJuntos > 0 && listaElementosCercanos != null &&
                    listaElementosCercanos.size() > 1 && numElementosAProcesar > 1) {
                Logg.info(TAG, "[procesaElementos] Procesando varios");
                if (listaElementosCercanos.get(0).esDeIncidencia())
                    procesarMultiplesIncidencias(fecha, quitar);
                else if (listaElementosCercanos.get(0).esDeElemento())
                    procesarMultiplesElementos(fecha, quitar);
            } else {
                if (elementoCercanoSeleccionado.esDeIncidencia()) {
                    Logg.info(TAG, "[procesaElementos] Procesando incidencia: " + elementoCercanoSeleccionado.getIncidencia().Id);

                    RutasManager.getInstance().setIncidenciaProcesada(rutaActual.Codigo,
                            rutaActual.Empresa, elementoCercanoSeleccionado.getIncidencia().Id, fecha,
                            quitar, true);
                } else if (elementoCercanoSeleccionado.esDeElemento()) {
                    Logg.info(TAG, "[procesaElementos] Procesando elemento: " + elementoCercanoSeleccionado.getElemento().Codigo);

                    RutasManager.getInstance().setElementoProcesado(rutaActual.Codigo,
                            rutaActual.Empresa, elementoCercanoSeleccionado.getElemento().Codigo, fecha,
                            quitar, !fueraDeRuta);

                    if (procesadoManual) {
                        // Enviamos el sensor 80
                        ArrayList<SensoresModel> sensores = new ArrayList<>();
                        SensoresModel model = new SensoresModel(rutaActual.CodMovil, SensoresModel.SENSOR_PROCESADO_MANUAL, "", elementoCercanoSeleccionado.getElemento().Codigo + "", "", fecha, "");
                        sensores.add(model);
                        SensoresManager.getInstance().insertSensor(sensores);
                        Log.i(TAG.substring(0, Math.min(26, TAG.length())), "Enviamos el sensor 80");
                    }
                }
                Logg.info(TAG, "[procesaElementos] Procesado enviado");

            }

        } catch (Throwable e) {
            Logg.error(TAG, "[procesaElementos] " + e.getMessage());
            TagManager.getInstance().skipTag(elementoCercanoSeleccionado.getElemento().Tag.Tag);
        }
    }

    private void procesarMultiplesElementos(Date fecha, boolean quitar) {
        LinkedList<ElementosModel> elementos = new LinkedList<ElementosModel>();
        Logg.info(TAG, "[procesarMultiplesElementos] Procesando multiples elementos: " + listaElementosCercanos.size());
        for (ElementosCercanosViewModel elementoSelec : listaElementosCercanos) {
            elementos.add(new ElementosModel().copy(elementoSelec.getElemento()));
            if (numElementosAProcesar > 1)
                numElementosAProcesar--;
            else
                break;
        }

        RutasManager.getInstance().setElementosProcesados(rutaActual.Codigo,
                rutaActual.Empresa, elementos, fecha, quitar, false);
    }

    private void procesarMultiplesIncidencias(Date fecha, boolean quitar) {
        LinkedList<IncidenciasRutaModel> incidencias = new LinkedList<IncidenciasRutaModel>();
        Logg.info(TAG, "[procesarMultiplesIncidencias] Procesando multiples incidencias: " + listaElementosCercanos.size());
        for (ElementosCercanosViewModel elementoSelec : listaElementosCercanos) {
            incidencias.add(new IncidenciasRutaModel().copy(elementoSelec.getIncidencia()));
            if (numElementosAProcesar > 1)
                numElementosAProcesar--;
            else
                break;
        }

        RutasManager.getInstance().setIncidenciasProcesadas(rutaActual.Codigo,
                rutaActual.Empresa, incidencias, fecha, quitar, false);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        try {
            switch (requestCode) {
                case ACTIVITY_NUM_ELEMENTOS_PROCESAR:
                    if (resultCode > 0) {
                        numElementosAProcesar = resultCode;
                        ConfiguracionManager.save(NUM_ELEMENTOS_PROCESAR, "" + numElementosAProcesar);
                        resetElementosSeleccionados = false;
                        onProcesar();
                    }
                    break;
            }
        } catch (Throwable e) {
            Logg.error(TAG, "[onActivityResult] " + e.getMessage());
        }
    }

    @Override
    public void onProcesar() {

    }

}

