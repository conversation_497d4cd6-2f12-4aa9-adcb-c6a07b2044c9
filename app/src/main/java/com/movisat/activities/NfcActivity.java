package com.movisat.activities;

import android.app.Activity;
import android.nfc.NfcAdapter;
import android.nfc.Tag;
import android.os.Bundle;

import com.movisat.events.nfc.OnNfcReaded;
import com.movisat.log.Logg;

import com.movisat.utilities.Utils;
import com.movisat.utils.Timer;

import de.greenrobot.event.EventBus;

/**
 * Esta Activity se inicia cuando un tag NFC es leído por el dispositivo. Cuando
 * esto ocurre, realiza la lectura del ID, la transmite vía EventBus e inmediatamente se
 * cierra.
 * <p>
 * (Mantis 5848)
 * Cuando el método enableReaderMode() de NfcAdapter (en BaseActivity) no funciona.
 */
public class NfcActivity extends Activity {
    static final String TAG = "NfcActivity";
    static final Timer timer = new Timer(1);

    public static void sendTagNfc(Tag tag) {
        try {
            if (!timer.isOver()) return;

            Logg.info(TAG, "Tag NFC leído con ID = " + Utils.arrayToHex(tag.getId(), 0, tag.getId().length));

            EventBus.getDefault().post(new OnNfcReaded(tag));
        } catch (Throwable e) {
            Logg.info(TAG, "ERROR: " + e.getMessage());
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Tag tag = getIntent().getParcelableExtra(NfcAdapter.EXTRA_TAG);
        if (tag != null) sendTagNfc(tag);
        finish();
    }

}
