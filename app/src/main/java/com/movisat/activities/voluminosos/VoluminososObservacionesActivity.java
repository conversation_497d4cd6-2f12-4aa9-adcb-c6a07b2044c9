package com.movisat.activities.voluminosos;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;

import com.movisat.activities.R;
import com.movisat.activities.incidencias.IncidenciaBaseActivity;
import com.movisat.activities.incidencias.IncidenciaFotoActivity;
import com.movisat.activities.incidencias.IncidenciaModelosActivity;
import com.movisat.activities.incidencias.IncidenciaMotivosActivity;
import com.movisat.adapters.voluminosos.ListAdapterModelosVoluminosos;
import com.movisat.dao.voluminosos.VoluminososModelDAO;
import com.movisat.events.incidencias.OnNotifyAfterSincronizacionIncidenciasTipo;
import com.movisat.events.incidencias.OnNotifyBeforeSincronizacionIncidenciasTipo;
import com.movisat.models.voluminosos.VoluminososModel;
import com.movisat.utilities.SoftKeyboard;

import java.util.ArrayList;


public class VoluminososObservacionesActivity extends VoluminososBaseActivity {


    private Button btnEnviar;
    private Button btnHideKeyboard;
    private EditText editTextObservaciones;
    private SoftKeyboard softKeyboard;

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        try {
            getMenuInflater().inflate(R.menu.menu_main, menu);
            return true;
        } catch (Throwable e) {
            logger.WriteError(e);
            return false;
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            setTituloActionBar(R.string.title_ecosat_incidencias_ruta);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void onResume() {
        try {
            super.onResume();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void setContentView() {
        // Reutilizo la activity.
        setContentView(R.layout.activity_observaciones);
    }

    @Override
    protected void init() {
        try {
            Bundle bundle = getIntent().getExtras();
            btnEnviar = (Button) findViewById(R.id.btnEnviar);
            btnHideKeyboard = (Button) findViewById(R.id.btnHideKeyboard);
            editTextObservaciones = (EditText) findViewById(R.id.editText_observaciones);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void setControls() {
        try {
            btnEnviar.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    try {
                        observaciones = editTextObservaciones.getText().toString();
                        enviar();
                    } catch (Throwable e) {
                        logger.WriteError(e);
                    }
                }
            });

            // Se crea el objeto para manejar el SoftKeyboard y saber cuándo se muestra / oculta
            softKeyboard = new SoftKeyboard(this);
            softKeyboard.setOnKeyboardVisibilityChangeListener(new SoftKeyboard.OnKeyboardVisibilityChangeListener() {
                @Override
                public void onVisibilityChange(final boolean keyboardVisible) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                btnHideKeyboard.setVisibility(keyboardVisible ? View.VISIBLE : View.INVISIBLE);
                            } catch (Throwable e) {
                                logger.WriteError(e);
                            }
                        }
                    });
                }
            });

            // Listener para ocultar el teclado al pulsar el botón de ocultar
            btnHideKeyboard.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    try {
                        softKeyboard.hide();
                    } catch (Throwable e) {
                        logger.WriteError(e);
                    }
                }
            });

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
    }


    @Override
    public void finish() {
        if (softKeyboard != null)
            softKeyboard.detach();
        super.finish();
    }
}


