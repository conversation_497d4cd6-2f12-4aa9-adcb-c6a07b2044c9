package com.movisat.activities.voluminosos;

import com.google.android.gms.maps.model.LatLng;
import com.jobqueue.JobManager;
import com.movisat.activities.BaseActivity;
import com.movisat.activities.R;
import com.movisat.application.RutasApplication;
import com.movisat.events.incidencias.OnChangeActiveIncidencia;
import com.movisat.jobs.voluminosos.InsertarRecogidaVoluminosoJob;
import com.movisat.listeners.gps.MyLocationListener;
import com.movisat.managers.RutasManager;
import com.movisat.models.voluminosos.VoluminososModel;
import com.movisat.models.voluminosos.VoluminososTipoModel;
import com.movisat.utilities.ToastMensaje;
import com.movisat.viewmodels.mapa.PosicionGPSViewModel;
import com.movisat.viewmodels.voluminosos.VoluminosoRecogidaViewModel;

import java.util.Date;

import cn.pedant.SweetAlert.SweetAlertDialog;
import de.greenrobot.event.EventBus;


public class VoluminososBaseActivity extends BaseActivity {

    // Parámetro recibidos en la primera activity.
    static protected int idRuta = -1;
    static protected LatLng posicion = null;

    // Parámetros asignados en cada activity al seleccionar el elemento de la lista.
    static protected VoluminososTipoModel tipoVoluminosoSeleccionado = null;
    static protected VoluminososModel modeloVoluminosoSeleccionado = null;
    static protected String observaciones = null;

    static private PosicionGPSViewModel posicionGPS = null;

    protected void dispose() {
        idRuta = -1;
        posicion = null;
        tipoVoluminosoSeleccionado = null;
        modeloVoluminosoSeleccionado = null;
        observaciones = null;
        posicionGPS = null;
    }

    protected void enviar() {
        try {
            if (posicion != null)
                posicionGPS = new PosicionGPSViewModel(null, posicion.latitude, posicion.longitude, 0, 0, 0, 0, new Date());
            else
                posicionGPS = MyLocationListener.getInstance().getUltimaPosicionGPS();

            if (posicionGPS == null) {
                new ToastMensaje(this).show(getString(R.string.rutas_no_hay_posicio_gps_valida), R.drawable.gps_85dp);
                return;
            }

            if (tipoVoluminosoSeleccionado == null) {
                new ToastMensaje(this).show(getString(R.string.no_seleccionado_tipo_voluminoso), R.drawable.gps_85dp);
                return;
            }

            if (modeloVoluminosoSeleccionado == null) {
                new ToastMensaje(this).show(getString(R.string.no_seleccionado_modelo_voluminoso), R.drawable.gps_85dp);
                return;
            }


            final SweetAlertDialog dlg = new SweetAlertDialog(this, SweetAlertDialog.WARNING_TYPE)
                    .setTitleText(getString(R.string.atencion))
                    .setContentText(getString(R.string.desea_registrar_recogida_voluminoso))
                    .setConfirmText(getString(R.string.si))
                    .setCancelText(getString(R.string.no));

            dlg.setConfirmClickListener(new SweetAlertDialog.OnSweetClickListener() {
                @Override
                public void onClick(SweetAlertDialog sweetAlertDialog) {
                    dlg.dismiss();

                    // Si la posición es de hace más de 1 minuto aviso
                    if (new Date().getTime() - posicionGPS.fecha.getTime() > 60 * 1000) {
                        new ToastMensaje(VoluminososBaseActivity.getInstance()).
                                show(getString(R.string.rutas_posicion_gps_antigua_params, 1), R.mipmap.gps);
                    }

                    insertRecogidaVoluminoso(posicionGPS);

                    setResult(RESULT_OK);

                    finish();
                }
            }).show();


        } catch (
                Throwable e) {
            logger.WriteError(e);
        }
    }

    private void insertRecogidaVoluminoso(PosicionGPSViewModel posicionGPS) {

        try {
            // Siempre se llama al manager de rutas por si fuese necesario iniciar ruta
            RutasManager.getInstance().setInicioDeRuta(idRuta, app.getEmpresa(), posicionGPS.fecha);

            JobManager jobManager = RutasApplication.getInstance().getJobManager();
            jobManager.addJobInBackground(new InsertarRecogidaVoluminosoJob(
                    VoluminosoRecogidaViewModel.desdeModelo(modeloVoluminosoSeleccionado, app.getCodigoMovil(), posicionGPS.latitud, posicionGPS.longitud, observaciones)));

            //Volvemos a resetear la accion de insertar incidencias sobre el mapa
            EventBus.getDefault().post(new OnChangeActiveIncidencia());
        } catch (Throwable e) {
            logger.WriteError(e);
        }

    }
}
