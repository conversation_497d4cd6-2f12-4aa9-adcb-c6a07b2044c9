package com.movisat.activities.voluminosos;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;

import com.google.android.gms.maps.model.LatLng;
import com.movisat.activities.R;
import com.movisat.activities.incidencias.IncidenciaModelosActivity;
import com.movisat.adapters.voluminosos.ListAdapterTiposVoluminosos;
import com.movisat.dao.voluminosos.VoluminososTipoDAO;
import com.movisat.events.incidencias.OnNotifyAfterSincronizacionIncidenciasTipo;
import com.movisat.events.incidencias.OnNotifyBeforeSincronizacionIncidenciasTipo;
import com.movisat.models.voluminosos.VoluminososTipoModel;

import java.util.ArrayList;


public class VoluminososTiposActivity extends VoluminososBaseActivity {

    //Constantes
    public final String ID_RUTA = "idRuta";
    public final String POSICION = "posicion";
    private ListView listVoluminosos;

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        try {
            getMenuInflater().inflate(R.menu.menu_main, menu);
            return true;
        } catch (Throwable e) {
            logger.WriteError(e);
            return false;
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            setTituloActionBar(R.string.title_ecosat_incidencias_ruta);
            fillListTiposIncidencias();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void onResume() {
        try {
            super.onResume();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void setContentView() {
        setContentView(R.layout.activity_voluminosos_tipos);
    }

    @Override
    protected void init() {
        try {
            // Reiniciamos los valores estáticos.
            dispose();

            TextView textViewTitulo = (TextView) findViewById(R.id.textView_titulo);
            listVoluminosos = (ListView) findViewById(R.id.ListView_listado_incidencias_rutas);

            //Pueden ser nulos idElemento y tipoElemento e idRuta
            Bundle bundle = getIntent().getExtras();
            idRuta = bundle.getInt(ID_RUTA);
            posicion = (LatLng) bundle.get(POSICION);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void setControls() {
        try {
            listVoluminosos.setOnItemClickListener(new AdapterView.OnItemClickListener() {
                @Override
                public void onItemClick(AdapterView<?> pariente, View view, int posicion, long id) {
                    clickVoluminoso(view, posicion);
                }
            });
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Click sobre un elemento de la lista
     *
     * @param view
     * @param posicion
     */
    private void clickVoluminoso(View view, int posicion) {
        try {
            tipoVoluminosoSeleccionado = (VoluminososTipoModel) listVoluminosos.getItemAtPosition(posicion);
            //Cambiamos el color del seleccionado
            for (int j = 0; j < listVoluminosos.getChildCount(); j++) {
                listVoluminosos.getChildAt(j).setBackgroundColor(getResources().getColor(
                        R.color.background_material_light));

            }
            view.setBackgroundColor(getResources().getColor(
                    R.color.verde));

            Intent i = new Intent(VoluminososTiposActivity.getInstance(), VoluminososModelosActivity.class);
            // Le pasamos un ID al abrir la nueva página para que notifique cuando se cierra. Esto lo hará llamando al método onActivityResult de esta misma clase.
            startActivityForResult(i, ACTIVITY_VOLUMINOSOS_MODELOS);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Construye la lista de Tipos de Incidencias
     */
    private void fillListTiposIncidencias() {
        try {
            VoluminososTipoDAO voluminososTipoDAO = new VoluminososTipoDAO();
            ArrayList<VoluminososTipoModel> tiposVoluminosos = (ArrayList<VoluminososTipoModel>) voluminososTipoDAO.getTodoPor(app.getEmpresa());

            if (tiposVoluminosos != null) {
                ListAdapter listAdapter = new ListAdapterTiposVoluminosos(this, R.layout.entrada_1text, tiposVoluminosos);
                listVoluminosos.setAdapter(listAdapter);
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }


    public void onEventMainThread(OnNotifyAfterSincronizacionIncidenciasTipo event) {
        try {
            fillListTiposIncidencias();
            super.showProgressBar(false);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void onEventMainThread(OnNotifyBeforeSincronizacionIncidenciasTipo event) {
        try {
            super.showProgressBar(true);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        try {
            super.onActivityResult(requestCode, resultCode, data);
            switch (requestCode) {
                case ACTIVITY_VOLUMINOSOS_MODELOS:
                    if (resultCode == RESULT_OK) {
                        setResult(RESULT_OK);
                        dispose();
                        finish();
                    }
                    break;
            }
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    @Override
    protected void onBotonAtrasClick(Activity activity) {
        try {
            dispose();
            activity.onBackPressed();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }
}


