package com.movisat.activities.voluminosos;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;

import com.google.android.gms.maps.model.LatLng;
import com.movisat.activities.BaseActivity;
import com.movisat.activities.R;
import com.movisat.activities.incidencias.IncidenciaModelosActivity;
import com.movisat.adapters.voluminosos.ListAdapterModelosVoluminosos;
import com.movisat.adapters.voluminosos.ListAdapterTiposVoluminosos;
import com.movisat.dao.voluminosos.VoluminososModelDAO;
import com.movisat.events.incidencias.OnNotifyAfterSincronizacionIncidenciasTipo;
import com.movisat.events.incidencias.OnNotifyBeforeSincronizacionIncidenciasTipo;
import com.movisat.models.incidencias.IncidenciasTipoModel;
import com.movisat.models.voluminosos.VoluminososModel;

import java.util.ArrayList;


public class VoluminososModelosActivity extends VoluminososBaseActivity {

    private ListView listVoluminosos;

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        try {
            getMenuInflater().inflate(R.menu.menu_main, menu);
            return true;
        } catch (Throwable e) {
            logger.WriteError(e);
            return false;
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            setTituloActionBar(R.string.title_ecosat_incidencias_ruta);
            fillListTiposIncidencias();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void onResume() {
        try {
            super.onResume();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void setContentView() {
        // Reutilizo la activity.
        setContentView(R.layout.activity_voluminosos_tipos);
    }

    @Override
    protected void init() {
        try {
            TextView textViewTitulo = (TextView) findViewById(R.id.textView_titulo);
            textViewTitulo.setText(R.string.voluminosos_modelos_titulo);
            listVoluminosos = (ListView) findViewById(R.id.ListView_listado_incidencias_rutas);

            Bundle bundle = getIntent().getExtras();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void setControls() {
        try {
            listVoluminosos.setOnItemClickListener(new AdapterView.OnItemClickListener() {
                @Override
                public void onItemClick(AdapterView<?> pariente, View view, int posicion, long id) {
                    clickVoluminoso(view, posicion);
                }
            });
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Click sobre un elemento de la lista
     *
     * @param view
     * @param posicion
     */
    private void clickVoluminoso(View view, int posicion) {
        try {
            modeloVoluminosoSeleccionado = (VoluminososModel) listVoluminosos.getItemAtPosition(posicion);
            //Cambiamos el color del seleccionado
            for (int j = 0; j < listVoluminosos.getChildCount(); j++) {
                listVoluminosos.getChildAt(j).setBackgroundColor(getResources().getColor(
                        R.color.background_material_light));

            }
            view.setBackgroundColor(getResources().getColor(
                    R.color.verde));

            Intent i = new Intent(VoluminososModelosActivity.getInstance(), VoluminososObservacionesActivity.class);
            startActivityForResult(i,ACTIVITY_VOLUMINOSOS_OBSERVACIONES);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Construye la lista de Tipos de Incidencias
     */
    private void fillListTiposIncidencias() {
        try {
            VoluminososModelDAO voluminososModelDAO = new VoluminososModelDAO();
            ArrayList<VoluminososModel> modelosVoluminosos = (ArrayList<VoluminososModel>) voluminososModelDAO.getTodoPor(tipoVoluminosoSeleccionado.Id, app.getEmpresa());

            if (modelosVoluminosos != null) {
                ListAdapter listAdapter = new ListAdapterModelosVoluminosos(this, R.layout.entrada_1text, modelosVoluminosos);
                listVoluminosos.setAdapter(listAdapter);
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        try {
            super.onActivityResult(requestCode, resultCode, data);
            switch (requestCode) {
                case ACTIVITY_VOLUMINOSOS_OBSERVACIONES:
                    if (resultCode == RESULT_OK) {
                        setResult(RESULT_OK);
                        finish();
                    }
                    break;
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void onEventMainThread(OnNotifyAfterSincronizacionIncidenciasTipo event) {
        try {
            fillListTiposIncidencias();
            super.showProgressBar(false);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void onEventMainThread(OnNotifyBeforeSincronizacionIncidenciasTipo event) {
        try {
            super.showProgressBar(true);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

}


