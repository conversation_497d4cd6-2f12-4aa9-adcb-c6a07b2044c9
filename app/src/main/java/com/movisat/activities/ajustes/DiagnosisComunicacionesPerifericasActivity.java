package com.movisat.activities.ajustes;

import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.Switch;

import com.movisat.activities.R;
import com.movisat.activities.activacion.ActivacionActivity;
import com.movisat.application.RutasApplication;
import com.movisat.dao.moviles.MovilesDAO;
import com.movisat.jobs.sincronizacion.SincronizacionJob;
import com.movisat.managers.ConfiguracionManager;
import com.movisat.managers.DracoManager;
import com.movisat.models.configuracion.ConfiguracionModel;
import com.movisat.models.moviles.MovilesModel;
import com.movisat.utilities.Utils;

public class DiagnosisComunicacionesPerifericasActivity extends BaseInformacion {

    /*
        Entrada1
        Entrada2
        Entrada3
        Entrada4
        Entrada5
        Entrada6

        Salida1
        Salida2

        CANBUSbaudios
        CANBUScanal
        CANBUSvelocidad
        CANBUSrpm
        CANBUSkilometraje

        CleanOpencontenedores
        CleanOpentag
        CleanOpenCepillosActivos
        CleanOpenConductorEnAsiento
        CleanOpenAspiradorActivo
        CleanOpenKilometraje

        RS232baudios

        BluetoothActivado
        BluetoothMAC
        BluetoothTipo

        OBDestaConectado
        OBDbastidor
        OBDvelocidad
        OBDrpm
        OBDkilometraje

        RFIDpuerto
        RFIDtipo
        RFIDultimoTag
        RFIDtrama

        PesajeTara
        PesajeBruto
        PesajeNeto

     */


    private static DiagnosisComunicacionesPerifericasActivity instance = null;

    private volatile boolean lock = false;
    private volatile boolean active = true;

    private Switch swEntradaDigital1;
    private Switch swEntradaDigital2;
    private Switch swEntradaDigital3;
    private Switch swEntradaDigital4;
    private Switch swEntradaDigital5;
    private Switch swEntradaDigital6;
    private Switch swSalidaDigital1;
    private Switch swSalidaDigital2;

    private EditText editCANBUSbaudios;
    private EditText editCANBUScanal;
    private EditText editCANBUSvelocidad;
    private EditText editCANBUSrpm;
    private EditText editCANBUSkilometraje;

    private EditText editCleanOpencontenedores;
    private EditText editCleanOpentag;
    private Switch swCepillosActivos;
    private Switch swAspiradorActivo;
    private Switch swConductorEnAsiento;
    private EditText editCleanOpenKilometraje;

    private EditText editRS232baudios;

    private Switch swBluetoothActivo;
    private EditText editBluetoothMAC;
    private EditText editBluetoothTipo;

    private Switch swOBDEstaConectado;
    private EditText editOBDbastidor;
    private EditText editOBDvelocidad;
    private EditText editOBDrpm;
    private EditText editOBDkilometraje;

    private EditText editRFIDpuerto;
    private EditText editRFIDtipo;
    private EditText editRFIDultimoTag;
    private EditText editRFIDtrama;

    private EditText editPesajeTara;
    private EditText editPesajeBruto;
    private EditText editPesajeNeto;

    protected void setContentView(int layautId, LayoutInflater inflater, ViewGroup container) {
        try {
            super.setContentView(layautId, inflater, container);
        } catch (Throwable e) {
            logger.WriteError(e);
        }

    }


    public static DiagnosisComunicacionesPerifericasActivity getInstance() {
        return instance;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {

            setContentView(R.layout.activity_diagnosis_comunicacionesperifericas, inflater, container);
            init();
            setControls();

        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return rootView;
    }


    @Override
    public void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            instance = this;
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        active = false;
    }

    protected void init() {
        try {
            super.init();

            swEntradaDigital1 = rootView.findViewById(R.id.swEntradaDigital1);
            swEntradaDigital2 = rootView.findViewById(R.id.swEntradaDigital2);
            swEntradaDigital3 = rootView.findViewById(R.id.swEntradaDigital3);
            swEntradaDigital4 = rootView.findViewById(R.id.swEntradaDigital4);
            swEntradaDigital5 = rootView.findViewById(R.id.swEntradaDigital5);
            swEntradaDigital6 = rootView.findViewById(R.id.swEntradaDigital6);

            swSalidaDigital1 = rootView.findViewById(R.id.swSalidaDigital1);
            swSalidaDigital2 = rootView.findViewById(R.id.swSalidaDigital2);

            editCANBUSbaudios = rootView.findViewById(R.id.CANBUSbaudios);
            editCANBUScanal = rootView.findViewById(R.id.CANBUScanal);
            editCANBUSvelocidad = rootView.findViewById(R.id.CANBUSvelocidad);
            editCANBUSrpm = rootView.findViewById(R.id.CANBUSrpm);
            editCANBUSkilometraje = rootView.findViewById(R.id.CANBUSkilometraje);

            editCleanOpencontenedores = rootView.findViewById(R.id.CleanOpencontenedores);
            editCleanOpentag = rootView.findViewById(R.id.CleanOpentag);

            swCepillosActivos = rootView.findViewById(R.id.swCepillosActivos);
            swAspiradorActivo = rootView.findViewById(R.id.swAspiradorActivo);
            swConductorEnAsiento = rootView.findViewById(R.id.swConductorEnAsiento);
            editCleanOpenKilometraje = rootView.findViewById(R.id.CleanOpenKilometraje);

            editRS232baudios = rootView.findViewById(R.id.RS232baudios);

            swBluetoothActivo = rootView.findViewById(R.id.swBluetoothActivo);
            editBluetoothMAC = rootView.findViewById(R.id.BluetoothMAC);
            editBluetoothTipo = rootView.findViewById(R.id.BluetoothTipo);

            swOBDEstaConectado = rootView.findViewById(R.id.swOBDConectado);
            editOBDbastidor = rootView.findViewById(R.id.OBDbastidor);
            editOBDvelocidad = rootView.findViewById(R.id.OBDvelocidad);
            editOBDrpm = rootView.findViewById(R.id.OBDrpm);
            editOBDkilometraje = rootView.findViewById(R.id.OBDkilometraje);

            editRFIDpuerto = rootView.findViewById(R.id.RFIDpuerto);
            editRFIDtipo = rootView.findViewById(R.id.RFIDtipo);
            editRFIDultimoTag = rootView.findViewById(R.id.RFIDultimoTag);
            editRFIDtrama = rootView.findViewById(R.id.RFIDtrama);

            editPesajeTara = rootView.findViewById(R.id.PesajeTara);
            editPesajeBruto = rootView.findViewById(R.id.PesajeBruto);
            editPesajeNeto = rootView.findViewById(R.id.PesajeNeto);

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }


    protected void setControls() {
        try {
            super.setControls();

            Thread thread = new Thread() {
                @Override
                public void run() {
                    try {
                        while (active) {
                            lock = true;
                            diagComunicacionesPerofericasHandler.post(null);
                            while (lock)
                                sleep(1000);
                        }
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            };

            thread.start();

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    Handler diagComunicacionesPerofericasHandler = new Handler() {

        public void handleMessage(Message msg) {

            try {

                swEntradaDigital1.setChecked(DracoManager.getDiagnosisBy("diagnosisEntradas.ED1", "0").valor.equals("1"));
                swEntradaDigital2.setChecked(DracoManager.getDiagnosisBy("diagnosisEntradas.ED2", "0").valor.equals("1"));
                swEntradaDigital3.setChecked(DracoManager.getDiagnosisBy("diagnosisEntradas.ED3", "0").valor.equals("1"));
                swEntradaDigital4.setChecked(DracoManager.getDiagnosisBy("diagnosisEntradas.ED4", "0").valor.equals("1"));
                swEntradaDigital5.setChecked(DracoManager.getDiagnosisBy("diagnosisEntradas.ED5", "0").valor.equals("1"));
                swEntradaDigital6.setChecked(DracoManager.getDiagnosisBy("diagnosisEntradas.ED6", "0").valor.equals("1"));

                swSalidaDigital1.setChecked(DracoManager.getDiagnosisBy("diagnosisSalidas.SD1", "0").valor.equals("1"));
                swSalidaDigital2.setChecked(DracoManager.getDiagnosisBy("diagnosisSalidas.SD2", "0").valor.equals("1"));

                editCANBUSbaudios.setText(DracoManager.getDiagnosisBy("diagnosisCANBUS.baudios", "0").valor + " bps");
                editCANBUScanal.setText(DracoManager.getDiagnosisBy("diagnosisCANBUS.canal", "0").valor);
                editCANBUSvelocidad.setText(DracoManager.getDiagnosisBy("diagnosisCANBUS.velocidad", "0").valor + " Km/h");
                editCANBUSrpm.setText(DracoManager.getDiagnosisBy("diagnosisCANBUS.rpm", "0").valor);
                editCANBUSkilometraje.setText(DracoManager.getDiagnosisBy("diagnosisCANBUS.kilometraje", "0").valor + " Km");

                editCleanOpencontenedores.setText(DracoManager.getDiagnosisBy("diagnosisCleanOpen.contenedores", "0").valor);
                editCleanOpentag.setText(DracoManager.getDiagnosisBy("diagnosisCleanOpen.tag", "").valor);
                swCepillosActivos.setChecked(DracoManager.getDiagnosisBy("diagnosisCleanOpen.cepillosActivos", "0").valor.equals("1"));
                swAspiradorActivo.setChecked(DracoManager.getDiagnosisBy("diagnosisCleanOpen.aspiradorActivo", "0").valor.equals("1"));
                swConductorEnAsiento.setChecked(DracoManager.getDiagnosisBy("diagnosisCleanOpen.conductorEnAsiento", "0").valor.equals("1"));
                editCleanOpenKilometraje.setText(DracoManager.getDiagnosisBy("diagnosisCleanOpen.kilometraje", "0").valor + " Km");

                swBluetoothActivo.setChecked(DracoManager.getDiagnosisBy("diagnosisBluetooth.estaActivado", "0").valor.equals("1"));
                editBluetoothMAC.setText(DracoManager.getDiagnosisBy("diagnosisBluetooth.MAC", "").valor);
                editBluetoothTipo.setText(DracoManager.getDiagnosisBy("diagnosisBluetooth.tipo", "").valor);

                editRS232baudios.setText(DracoManager.getDiagnosisBy("diagnosisRS232.baudios", "0").valor + " bps");

                swOBDEstaConectado.setChecked(DracoManager.getDiagnosisBy("diagnosisOBD.estaConectado", "0").valor.equals("1"));
                editOBDbastidor.setText(DracoManager.getDiagnosisBy("diagnosisOBD.bastidor", "").valor);
                editOBDvelocidad.setText(DracoManager.getDiagnosisBy("diagnosisOBD.velocidad", "0").valor + " Km/h");
                editOBDrpm.setText(DracoManager.getDiagnosisBy("diagnosisOBD.rpm", "0").valor);
                editOBDkilometraje.setText(DracoManager.getDiagnosisBy("diagnosisOBD.kilometraje", "0").valor + " Km");

                editRFIDpuerto.setText(DracoManager.getDiagnosisBy("diagnosisRFID.puerto", "0").valor);
                editRFIDtipo.setText(DracoManager.getDiagnosisBy("diagnosisRFID.tipo", "").valor);
                editRFIDultimoTag.setText(DracoManager.getDiagnosisBy("diagnosisRFID.ultimoTag", "").valor);
                editRFIDtrama.setText(DracoManager.getDiagnosisBy("diagnosisRFID.trama", "").valor);

                editPesajeBruto.setText(DracoManager.getDiagnosisBy("diagnosisPesaje.bruto", "0").valor + " Kg");
                editPesajeTara.setText(DracoManager.getDiagnosisBy("diagnosisPesaje.tara", "0").valor + " Kg");
                editPesajeNeto.setText(DracoManager.getDiagnosisBy("diagnosisPesaje.neto", "0").valor + " Kg");

            } catch (Throwable e) {
            } finally {
                lock = false;
            }
        }

    };


}
