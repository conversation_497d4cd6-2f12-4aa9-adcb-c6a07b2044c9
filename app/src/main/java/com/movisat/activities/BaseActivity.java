package com.movisat.activities;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Configuration;
import android.nfc.NfcAdapter;
import android.os.AsyncTask;
import android.os.Bundle;
import android.text.InputType;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import com.movisat.activities.activacion.ActivacionActivity;
import com.movisat.activities.ajustes.AjustesActivity;
import com.movisat.activities.ajustes.DiagnosisActivity;
import com.movisat.activities.ayuda.HelpActivity;
import com.movisat.activities.incidencias.AutoPhoto;
import com.movisat.activities.map.MapActivity;
import com.movisat.activities.mensajeria.MensajeriaActivity;
import com.movisat.activities.mensajeria.MensajeriaRecibidosActivity;
import com.movisat.activities.moviles.MovilesActivity;
import com.movisat.activities.personal.PersonalActivity;
import com.movisat.activities.rutas.RutasActivity;
import com.movisat.activities.splashScreen.SplashScreenActivity;
import com.movisat.activities.wificamera.WifiCameraActivity;
import com.movisat.application.RutasApplication;
import com.movisat.callinglib.utils.PhoneUtils;
import com.movisat.dao.elementos.ElementosDAOProxy;
import com.movisat.dao.incidencias.IncidenciasRutaDAO;
import com.movisat.dao.mensajes.MensajesDAO;
import com.movisat.dao.moviles.MovilesDAO;
import com.movisat.managers.PDFManager;
import com.movisat.dao.rutas.ProcesadoPorActividadDAO;
import com.movisat.dao.rutas.RutasDAOProxy;
import com.movisat.dao.rutas.RutasElementosDAO;
import com.movisat.dao.rutas.RutasElementosDAOProxy;
import com.movisat.events.RFid.OnNotifyRFidEvent;
import com.movisat.events.incidencias.OnErrorIncidenciaHEvent;
import com.movisat.events.incidencias.OnSavedIncidenciaHEvent;
import com.movisat.events.intranet.OnErrorConfiguracionEvent;
import com.movisat.events.map.OnRefrescarIncidenciasMapa;
import com.movisat.events.mensajeria.OnNotifyMensajesRecibidosEvent;
import com.movisat.events.rutas.OnElementoProcesadoEvent;
import com.movisat.events.sensores.OnNotifyDinEvent;
import com.movisat.factories.FactoryProcesado;
import com.movisat.factories.actions.ClickProcesadoNivelLlenadoAction;
import com.movisat.factories.actions.IProcesadoAction;
import com.movisat.log.Logg;
import com.movisat.managers.DracoManager;
import com.movisat.managers.RutasManager;
import com.movisat.managers.StatusManager;
import com.movisat.managers.TTSManager;
import com.movisat.managers.TagManager;
import com.movisat.managers.TrabajadoresManager;
import com.movisat.managers.log.LoggerManager;
import com.movisat.models.configuracion.ConfiguracionModel;
import com.movisat.models.elementos.ElementosModel;
import com.movisat.models.incidencias.IncidenciasRutaModel;
import com.movisat.models.mensajes.MensajesModel;
import com.movisat.models.moviles.MovilesModel;
import com.movisat.models.rutas.ProcesadoPorActividadModel;
import com.movisat.models.rutas.RutasElementosModel;
import com.movisat.models.rutas.RutasModel;
import com.movisat.usecases.rutas.ProcesaElementosPorEntradaDigitalUseCase;
import com.movisat.utilities.AdminUtils;
import com.movisat.utilities.ToastMensaje;
import com.movisat.utils.Stream;
import com.movisat.utils.Utilss;
import com.movisat.viewmodels.elementos.ElementosCercanosViewModel;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.TimeZone;

import cn.pedant.SweetAlert.SweetAlertDialog;
import de.greenrobot.event.EventBus;
import me.zhanghai.android.materialprogressbar.IndeterminateHorizontalProgressDrawable;

/**
 * Created by faroca on 15/09/2015.
 */
public abstract class BaseActivity extends AppCompatActivity {
    private static final String TAG = BaseActivity.class.getSimpleName();
    private static BaseActivity instance = null;
    protected RutasApplication app = null;
    public static final LoggerManager logger = LoggerManager.getInstance();
    private MovilesModel vehiculoSeleccionado = null;
    private String lastTAG = "";
    protected String filtroBusqueda = "";
    protected androidx.appcompat.widget.SearchView searchView = null;
    //    public static final String EXIT_PASSWORD = "85213";
    public static final String EXIT_PASSWORD = "15379";
    public static final String RESET_DATA_AND_RESTART_PASSWORD = "66666";
    public boolean capture2 = false;

    private Menu mMenu = null;

    // Códigos asignados a las distintas Activitys
    public final static int ACTIVITY_TIPOS_INCIDENCIAS = 1;
    public final static int ACTIVITY_MODELOS_INCIDENCIAS = 2;
    public final static int ACTIVITY_MOTIVOS_INCIDENCIAS = 3;
    public final static int ACTIVITY_OBSERVACIONES_INCIDENCIAS = 4;
    public final static int ACTIVITY_SENSORES = 5;
    public final static int ACTIVITY_PERSONAL = 6;
    public final static int ACTIVITY_FOTO_INCIDENCIAS = 7;
    public final static int ACTIVITY_ACTIVACION = 8;
    public final static int ACTIVITY_NUM_ELEMENTOS_PROCESAR = 9;
    public final static int ACTIVITY_NIVEL_DE_LLENADO = 10;
    public final static int ACTIVITY_REPARACIONES = 11;
    public final static int ACTIVITY_REPARACIONES_OBSERVACIONES = 12;
    public final static int ACTIVITY_REVISIONES = 13;
    public final static int ACTIVITY_SCREEN_SLIDE = 14;
    public final static int REQUEST_WIFI_CAMERA1_AUTO = 16;
    public final static int REQUEST_WIFI_CAMERA2_AUTO = 17;
    public final static int ACTIVITY_VOLUMINOSOS_MODELOS = 18;
    public final static int ACTIVITY_VOLUMINOSOS_OBSERVACIONES = 19;

    public static final Stream<OnElementoProcesadoEvent> onProcessedElement = new Stream<>(null);

    public static BaseActivity getInstance() {
        return instance;
    }

    //region Métodos sobreescritos

    // Esto es para que no se llame al OnCreate al rotar la pantalla
    @Override
    public void onConfigurationChanged(Configuration nuevaconfig) {
        super.onConfigurationChanged(nuevaconfig);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        try {
            //            if (this == MainActivity.getInstance()) {
            //
            //            }
            switch (item.getItemId()) {
                case R.id.btnVehiculo:
                    Intent i = new Intent(this, MovilesActivity.class);
                    startActivity(i);
                    return true;
                case R.id.btnAjustes:
                    i = new Intent(this, AjustesActivity.class);
                    startActivity(i);
                    return true;
                case R.id.btnMensajeria:
                    i = new Intent(this, MensajeriaActivity.class);
                    startActivity(i);
                    return true;
                /*case R.id.btnPersonal:
                    i = new Intent(this, PersonalActivity.class);
                    i.putExtra("desdeMenu", true);
                    startActivity(i);
                    return true;*/
                case R.id.btnRutas:
                    i = new Intent(this, RutasActivity.class);
                    startActivity(i);
                    return true;
                case R.id.btnMapa:
                    i = new Intent(this, MapActivity.class);
                    startActivity(i);
                    return true;
                case R.id.btnSincro:
                    app.synchronizeAll();
                    return true;
                case R.id.btnHelp:
                    i = new Intent(this, HelpActivity.class);
                    startActivity(i);
                    return true;
                case R.id.btnPdf:
                    if (PDFManager.getInstance().mostrarPDF()) {
                        PDFManager.getInstance().mostrarPopupPdfs(this);
                    }
                    return true;
                case R.id.btnfinalizar:
                    DialogoSalir();
                    return true;
                case R.id.btnReiniciar:
                    onRestartPressed();
                    return true;
                default:
                    return super.onOptionsItemSelected(item);
            }
        } catch (Throwable e) {
            logger.WriteError(e);
            return false;

        }
    }

    public void DialogoSalir() {

        try {
            // Se crea un cuadro de diálogo para la introducción de texto. Si el texto
            // introducido coincide con la contraseña de salida, se puede salir de la aplicación.
            AlertDialog.Builder builder = new AlertDialog.Builder(this);
            builder.setTitle("Introduzca el código de salida");

            // Se crea el EditText para introducir texto y se incluye en el diálogo
            final EditText input = new EditText(this);
            input.setInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_VARIATION_PASSWORD);
            input.setImeOptions(EditorInfo.IME_FLAG_NO_EXTRACT_UI);
            builder.setView(input);

            // Set up the buttons
            builder.setPositiveButton("OK", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    String texto;
                    texto = input.getText().toString();
                    if (texto.equals(EXIT_PASSWORD)) {
                        // Se sale del modo kiosko y de la aplicación
                        AdminUtils.setKioskModeEnabled(SplashScreenActivity.getInstance(), false);
                        AdminUtils.setHomeButtonEnabled(true);
                        //                        SplashScreenActivity.getInstance().finish();
                        //                        MainActivity.getInstance().finish();
                        //                        RutasApplication.getInstance().finalize();
                        //                        finishAffinity();
                        //                        finishAndRemoveTask();
                        ExitActivity.exitApplication(getApplicationContext());

                        // Aviso que el usuario a salido de la aplicación usando la contraseña
                        // para que el Watchdog no la vuelva a arrancar
                        DracoManager.setConfigBy("appStoppedByUser", "1");
                    } else if (texto.equals(RESET_DATA_AND_RESTART_PASSWORD) || texto.equals(getResetCode() + "")) {
                        AdminUtils.setKioskModeEnabled(SplashScreenActivity.getInstance(), false);
                        AdminUtils.setHomeButtonEnabled(true);
                        // Elimino todos los datos de la aplicación y la reinicia
                        AdminUtils.resetData();
                        AdminUtils.restartApp(getApplicationContext(), SplashScreenActivity.class);
                    } else {
                        Toast.makeText(getApplicationContext(), "Código incorrecto", Toast.LENGTH_SHORT).show();
                    }
                }
            });
            builder.setNegativeButton("Cancelar", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.cancel();
                }
            });

            builder.show();

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    private void onRestartPressed() {
        final SweetAlertDialog dlg = new SweetAlertDialog(this, SweetAlertDialog.NORMAL_TYPE)
              .setTitleText(this.getString(R.string.atencion))
              .setContentText("¿Quiere reiniciar la aplicación?")
              .setConfirmText(this.getString(R.string.si))
              .setCancelText(this.getString(R.string.no));

        dlg.setConfirmClickListener(new SweetAlertDialog.OnSweetClickListener() {
            @Override
            public void onClick(SweetAlertDialog sweetAlertDialog) {
                try {
                    dlg.dismiss();
                    AdminUtils.restartApp(
                          getApplicationContext(),
                          MainActivity.class);
                } catch (Throwable e) {
                }
            }
        }).show();
    }

    @Override
    protected void onResume() {

        try {
            //TODO: Activar o desactivar botones del action bar.
            super.onResume();

            setCustomViewActionBar();
            if (app == null) {
                app = RutasApplication.getInstance();
            }

            showNotificationMensajes(app.getNumeroMensajesNoLeidos());
            setIdentificacionVehiculo();
            setIdentificacionRuta();

            // Se establece el menú en el StatusManager para mostrar los iconos de estado
            if (mMenu != null) {
                StatusManager statusManager = StatusManager.getInstance();
                statusManager.setMenu(mMenu);
                statusManager.loadIcons();  // Para mostrar los iconos al instante
            }

            //TODO: Ocultamos la barra del sistema y se pone la app a pantalla completa
            //hideSystemUI();

        } catch (Throwable e) {
            Logg.error(getSubtag(), "[onResume] " + e.getMessage());
        }
    }

    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {

        mMenu = menu;
        MenuItem item;

        try {
            // Se establece el menú en el StatusManager para mostrar los iconos de estado
            StatusManager statusManager = StatusManager.getInstance();
            statusManager.setMenu(menu);
            statusManager.loadIcons();  // Para mostrar los iconos al instante
            if (statusManager.getStatus() != AsyncTask.Status.RUNNING)
                statusManager.execute();

            // Control de visibilidad del botón PDF
            MenuItem pdfItem = menu.findItem(R.id.btnPdf);
            if (pdfItem != null) {
                boolean mostrarPdf = PDFManager.getInstance().mostrarPDF();
                pdfItem.setVisible(mostrarPdf);
            }

            for (int i = 0; i < menu.size(); i++) {
                item = menu.getItem(i);

                if (item != null &&
                      item != menu.findItem(R.id.btnVehiculo) &&
                      item != menu.findItem(R.id.btnAjustes) &&
                      item != menu.findItem(R.id.btnSincro) &&
                      item != menu.findItem(R.id.statusCam) &&
                      item != menu.findItem(R.id.statusDownload))
                    //item.setVisible(app.getCodigoMovil() != 0);
                    item.setVisible(true);

                //Ponemos invisible el boton de busqueda y asociamos evento.
                if (item == menu.findItem(R.id.action_search)) {
                    searchView = (androidx.appcompat.widget.SearchView) menu.findItem(R.id.action_search).getActionView();
                    searchView.setOnQueryTextListener(new OnFindText());
                    //Para que se ajuste al ancho maximo permitido.
                    searchView.setMaxWidth(Integer.MAX_VALUE);
                    item.setVisible(false);
                }
            }
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[onPrepareOptionsMenu] " + e.getMessage());
        }

        return super.onPrepareOptionsMenu(menu);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Logg.info(getSubtag(), "[onCreate] start");

        try {
            instance = this;

            app = (RutasApplication) getApplication();

            setContentView();

            setCustomViewActionBar();
            setClickMensajesNoLeidos();
            setClickIdentificarVehiculo();
            setClickIdentificarRuta();

            init();
            setControls();
            initBotonAtras(this, R.id.btnAtras);
            initBotonRefresh(this, R.id.btnRefresh);
            startReadingNfc();
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[onCreate] " + e.getMessage());
        }
    }

    @Override
    protected void onStart() {
        try {
            super.onStart();
            EventBus.getDefault().register(this);
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[onStart] " + e.getMessage());
        }
    }

    @Override
    protected void onStop() {
        try {
            EventBus.getDefault().unregister(this);
            super.onStop();
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[onStop] " + e.getMessage());
        }
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
    }

    //endregion

    private void startReadingNfc() {
        // Copy paste de EcoSAT Móvil (Por si...)
        // (Mantis 5848) En las versiones más recientes de Android tras bloquear y desbloquear
        // esto deja de funcionar, se lee a través del intent que se obtiene en NfcActivity
        try {
            NfcAdapter nfcAdapter = NfcAdapter.getDefaultAdapter(this);
            if (nfcAdapter != null && nfcAdapter.isEnabled()) {
                Bundle options = new Bundle();
                options.putInt(NfcAdapter.EXTRA_READER_PRESENCE_CHECK_DELAY, 1000);
                nfcAdapter.enableReaderMode(this, NfcActivity::sendTagNfc,
                      NfcAdapter.FLAG_READER_NFC_A | NfcAdapter.FLAG_READER_NFC_B
                            | NfcAdapter.FLAG_READER_NFC_F | NfcAdapter.FLAG_READER_NFC_V
                            | NfcAdapter.FLAG_READER_SKIP_NDEF_CHECK, options);
            }
        } catch (Exception ignore) { }
    }

    //region Métodos protegidos, abstractos.
    //TODO: Hacer este método abstracto.

    /**
     * Metodo muy importante para establecer en la actividad hija el layout contenedor.
     */
    protected void setContentView() {

    }

    /**
     * Inicializar todos los controles del activity en este metodo.
     * OJO solo instanciarlos nada de set.
     */
    protected void init() {

    }

    /**
     * Establecemos cada control o variable de la actividad.
     */
    protected void setControls() {
    }

    /**
     * Muestra u oculta la barra de progreso.
     *
     * @param visible
     */
    protected void showProgressBar(boolean visible) {

        try {
            ProgressBar progress = getSupportActionBar().
                  getCustomView().findViewById(R.id.indeterminate_horizontal_progress);
            if (progress != null) {
                progress.setVisibility(visible ? View.VISIBLE : View.GONE);
            }
        } catch (Exception e) {
            Logg.error(getSubtag(), "[showProgressBar] " + e.getMessage());
        }
    }

    protected void setTituloActionBar(int idResource) {

    }
    //endregion

    //region Métodos privados

    /**
     * Inicializa el custom view del action bar
     */
    public void setCustomViewActionBar() {
        try {
            ActionBar actionBar = getSupportActionBar();
            actionBar.setDisplayShowCustomEnabled(true);
            actionBar.setDisplayOptions(ActionBar.DISPLAY_SHOW_CUSTOM);
            actionBar.setCustomView(getLayoutInflater().inflate(R.layout.action_bar_home, null),
                  new ActionBar.LayoutParams(
                        ActionBar.LayoutParams.WRAP_CONTENT,
                        ActionBar.LayoutParams.MATCH_PARENT,
                        Gravity.START
                  ));
            setCustomViewActionBarRutas();
            setClickMensajesNoLeidos();
            setClickIdentificarVehiculo();
            setClickIdentificarRuta();
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[setCustomViewActionBar] " + e.getMessage());
        }
    }

    protected void hideRouteInfo() {
        try {
            RelativeLayout relativeLayoutTotalElementos = findViewById(R.id.relativeLayoutTotalElementos);
            RelativeLayout relativeLayoutbtnRutas_actionBar = findViewById(R.id.btnRutas_actionBar);
            relativeLayoutbtnRutas_actionBar.setVisibility(View.GONE);
            relativeLayoutTotalElementos.setVisibility(View.GONE);
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[hideRouteInfo] " + e.getMessage());
        }
    }

    public void setCustomViewActionBarRutas() {
        try {
            RelativeLayout relativeLayoutTotalElementos = findViewById(R.id.relativeLayoutTotalElementos);
            int idRutaIniciada = new RutasDAOProxy().getRutaIniciadaId(app.getEmpresa());

            if (idRutaIniciada == 0) {
                relativeLayoutTotalElementos.setVisibility(View.GONE);
                return;
            }

            relativeLayoutTotalElementos.setVisibility(View.VISIBLE);

            RutasModel ruta = new RutasDAOProxy().getRutaIniciada(app.getEmpresa());
            int numElemTotal;
            int numElemSinProcesar;

            if (ruta.esRutaDeIncidencias()) {
                numElemTotal = new IncidenciasRutaDAO().getNumIncidenciasByRuta(idRutaIniciada, app.getEmpresa());
                numElemSinProcesar = new IncidenciasRutaDAO().getNumIncidenciasSinProcesarByRuta(idRutaIniciada, app.getEmpresa());
            } else {
                numElemTotal = new RutasElementosDAOProxy().getNumElementosByRuta(idRutaIniciada, app.getEmpresa());
                numElemSinProcesar = new RutasElementosDAOProxy().getNumElementosSinProcesarByRuta(idRutaIniciada, app.getEmpresa());
            }
            TextView textViewElementosSinProcesar = Objects.requireNonNull(getSupportActionBar()).getCustomView().findViewById(R.id.textViewElementosRestantes);
            if (textViewElementosSinProcesar != null) {
                textViewElementosSinProcesar.setText(numElemSinProcesar + " / " + numElemTotal);
            }

            // Para generar eventos y poder debugear en Cieza
            if (app.getEmpresa() == 582 || app.getEmpresa() == 499) {
                ImageView imageView = findViewById(R.id.Logotipo_eco);

                imageView.setOnLongClickListener(v -> {
                    return true;
                });

            }
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[setCustomViewActionBarRutas] " + e.getMessage());
        }
    }

    private void createProgressBar(boolean visible) {
        try {
            IndeterminateHorizontalProgressDrawable toolbarIndeterminateDrawable = new IndeterminateHorizontalProgressDrawable(this);
            toolbarIndeterminateDrawable.setShowTrack(false);
            toolbarIndeterminateDrawable.setUseIntrinsicPadding(false);
            ProgressBar progress = getSupportActionBar().getCustomView().findViewById(R.id.indeterminate_horizontal_progress);
            progress.setIndeterminateDrawable(toolbarIndeterminateDrawable);
            if (visible)
                progress.setVisibility(View.VISIBLE);
            else
                progress.setVisibility(View.GONE);
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[createProgressBar] " + e.getMessage());
        }
    }

    /**
     * Te muestra u oculta el icono de mensajes en función del número de mensajes no leidos.
     *
     * @param totalMensajesNoLeidos
     */
    private void showNotificationMensajes(int totalMensajesNoLeidos) {
        try {
            ActionBar actionBar = getSupportActionBar();
            RelativeLayout btnMensage_actionBar =
                  findViewById(R.id.btnMensaje_actionBar);
            TextView textView = actionBar.getCustomView().findViewById(R.id.textNumeroMensajes);
            if (textView != null && btnMensage_actionBar != null) {
                if (totalMensajesNoLeidos > 0) {
                    btnMensage_actionBar.setVisibility(View.VISIBLE);
                    textView.setText("" + totalMensajesNoLeidos);
                } else {
                    btnMensage_actionBar.setVisibility(View.GONE);
                }
            }
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[showNotificationMensajes] " + e.getMessage());
        }
    }

    /**
     * Te muestra u oculta el icono de vehiculo identificado en función de si hay algún equipo asignado.
     */
    private void setIdentificacionVehiculo() {
        try {
            ActionBar actionBar = getSupportActionBar();

            RelativeLayout btnVehiculo_actionBar =
                  findViewById(R.id.btnVehiculo_actionBar);
            TextView textView = actionBar.getCustomView().findViewById(R.id.textIdentificacionVehiculo);
            int identificadorVehiculo = app.getCodigoMovil();
            MovilesDAO movilesDAO = new MovilesDAO();
            MovilesModel movilesModel = movilesDAO.getMovil(identificadorVehiculo);
            if (textView != null && btnVehiculo_actionBar != null) {
                if (identificadorVehiculo > 0) {
                    btnVehiculo_actionBar.setVisibility(View.VISIBLE);
                    if (movilesModel != null && movilesModel.Descripcion != null)
                        textView.setText(movilesModel.Descripcion);
                    else
                        textView.setText("");
                } else {
                    btnVehiculo_actionBar.setVisibility(View.GONE);
                }
            }
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[setIdentificacionVehiculo] " + e.getMessage());
        }
    }

    /**
     * Te muestra u oculta el icono de la ruta activa en función de si hay una ruta con actividad iniciada.
     */
    private void setIdentificacionRuta() {
        try {
            ActionBar actionBar = getSupportActionBar();
            RelativeLayout relativeLayoutbtnRutas_actionBar =
                  findViewById(R.id.btnRutas_actionBar);
            TextView textView = actionBar.getCustomView().findViewById(R.id.textRutas);
            RutasModel identificadorRuta = RutasManager.getInstance().getRutaIniciada();
            if (textView != null) {
                if (identificadorRuta != null && relativeLayoutbtnRutas_actionBar != null) {
                    relativeLayoutbtnRutas_actionBar.setVisibility(View.VISIBLE);
                    textView.setText(identificadorRuta.Nombre);
                    textView.setSelected(true);
                } else {
                    relativeLayoutbtnRutas_actionBar.setVisibility(View.GONE);
                }
            }
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[setIdentificacionRuta] " + e.getMessage());
        }
    }

    /**
     * Inicializa el boton atras en todas las actividades que hereden esta clase y tengan un recurso R.id.btnAtras.
     *
     * @param activity
     * @param resourceID
     */
    private void initBotonAtras(final Activity activity, int resourceID) {

        try {
            ImageButton btnAtras = activity.findViewById(resourceID);

            if (btnAtras != null) {
                btnAtras.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onBotonAtrasClick(activity);
                    }
                });
            }
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[initBotonAtras] " + e.getMessage());
        }
    }

    /**
     * Inicializa el boton refresh en todas las actividades que hereden esta clase y tengan un recurso R.id.btnRefresh (Diagnosis).
     *
     * @param activity
     * @param resourceID
     */
    private void initBotonRefresh(final Activity activity, int resourceID) {

        try {
            ImageButton btnRefresh = activity.findViewById(resourceID);

            if (btnRefresh != null) {
                btnRefresh.setOnClickListener(v -> {
                    //
                });
            }
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[initBotonRefresh] " + e.getMessage());
        }
    }

    protected void onBotonAtrasClick(Activity activity) {
        // Mantis 6589: EcoRutas solapa pantallas de procesados
        if (activity instanceof RutasActivity) {
            RutasModel rutaIniciada = RutasManager.getInstance().getRutaIniciada();
            if (rutaIniciada == null) {
                Intent intent = new Intent(activity, MainActivity.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP); // Limpio la pila de actividades por encima de esta.
                activity.startActivity(intent);
                activity.finish();
                return;
            }
        }
        activity.onBackPressed();
    }

    //endregion

    //region Establecer el click en iconos de action bar.
    protected void setClickMensajesNoLeidos() {
        try {
            RelativeLayout mensajesNoLeidos =
                  getSupportActionBar().getCustomView().findViewById(R.id.btnMensaje_actionBar);
            if (mensajesNoLeidos != null) {
                mensajesNoLeidos.setOnClickListener(new OnClickListenerBase(MensajeriaRecibidosActivity.class));
            }
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[setClickMensajesNoLeidos] " + e.getMessage());
        }
    }

    private void setClickIdentificarVehiculo() {
        try {
            final RelativeLayout identificadorVehiculo =
                  getSupportActionBar().getCustomView().findViewById(R.id.btnVehiculo_actionBar);
            if (identificadorVehiculo != null) {
                identificadorVehiculo.setOnClickListener(v -> {
                    MovilesDAO movilesDAO = new MovilesDAO();
                    vehiculoSeleccionado = movilesDAO.getMovil(app.getCodigoMovil());
                    if (vehiculoSeleccionado != null) {
                        Toast toast = Toast.makeText(getApplicationContext(), vehiculoSeleccionado.toString(), Toast.LENGTH_LONG);
                        toast.setGravity(Gravity.CENTER, 0, 0);
                        ViewGroup group = (ViewGroup) toast.getView();
                        TextView messageTextView = (TextView) group.getChildAt(0);
                        messageTextView.setTextSize(20);
                        toast.show();
                    }
                });
            }
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[setClickIdentificarVehiculo] " + e.getMessage());
        }
    }

    protected void setClickIdentificarRuta() {
        try {
            RelativeLayout identificadorRuta = getSupportActionBar().getCustomView().findViewById(R.id.btnRutas_actionBar);
            if (identificadorRuta != null) {
                identificadorRuta.setOnClickListener(new OnClickListenerBase(RutasActivity.class));
            }
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[setClickIdentificarRuta] " + e.getMessage());
        }
    }
    //endregion

    //region Eventos recogidos con EventBus

    public void onEventMainThread(OnNotifyMensajesRecibidosEvent event) {
        try {
            if (app.getNumeroMensajesNoLeidos() > 0) {
                //Player.getInstance().setContext(this).play(Player.MENSAJE_RECIBIDO, true);
                //playLastReceivedMessage();

                // Solo reproducir TTS si hay mensajes no leídos en las últimas 24 horas
                int mensajesEn24Horas = new MensajesDAO().getMensajesNoLeidosEn24Horas();
                if (mensajesEn24Horas > 0) {
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                TTSManager.get().speak("Mensaje recibido");
                                Thread.sleep(2000);
                                playLastReceivedMessage();
                            } catch (Throwable e) {
                                logger.WriteError(e);
                            }
                        }
                    }).start();
                }
            }
            showNotificationMensajes(app.getNumeroMensajesNoLeidos());
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[onEventMainThread-OnNotifyMensajesRecibidosEvent] " + e.getMessage());
        }
    }

    public void onEventMainThread(OnSavedIncidenciaHEvent event) {
        try {
            EventBus.getDefault().post(new OnRefrescarIncidenciasMapa());
            new ToastMensaje(BaseActivity.this).
                  show(getString(R.string.incidencias_incidencia_insertada), R.mipmap.incidencia);
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[onEventMainThread-OnSavedIncidenciaHEvent] " + e.getMessage());
        }
    }

    public void onEventMainThread(OnErrorIncidenciaHEvent event) {
        new ToastMensaje(BaseActivity.this).show(getString(R.string.incidencias_incidencia_insertada_error));
        Logg.error(getSubtag(),
              "[onEventMainThread-OnErrorIncidenciaHEvent] Ha habido un error al insertar la incidencia (BaseActivity.java) con fecha");
    }

    public void onEventMainThread(OnElementoProcesadoEvent event) {
        try {
            if (event.esDeElemento()) {
                ElementosModel elemento = event.getElemento();
                if (elemento.Matricula != null && !elemento.Matricula.equals("")) {
                    new ToastMensaje(BaseActivity.this).
                          show(getString(R.string.rutas_procesando_elemento_params_matricula,
                                elemento.Matricula), R.mipmap.procesar1);
                } else {
                    new ToastMensaje(BaseActivity.this).
                          show(getString(R.string.rutas_procesando_elemento_params,
                                elemento.Nombre), R.mipmap.procesar1);
                }

                // Para que se actualicen el número de elementos procesados en la barra
                setCustomViewActionBarRutas();

            } else if (event.esDeIncidencia()) {
                IncidenciasRutaModel incidencia = event.getIncidencia();
                new ToastMensaje(BaseActivity.this).
                      show(getString(R.string.rutas_procesando_incidencia_params_id,
                            String.valueOf(incidencia.Id)), R.mipmap.procesar1);

                // Para que se actualicen el número de elementos procesados en la barra
                setCustomViewActionBarRutas();
            }

        } catch (Throwable e) {
            Logg.error(getSubtag(), "[onEventMainThread-OnElementoProcesadoEvent] " + e.getMessage());
        }

        onProcessedElement.notify(event);
    }

    public void onEventMainThread(OnErrorConfiguracionEvent event) {
        try {
            ConfiguracionModel urlServerNew = new ConfiguracionModel();
            for (ConfiguracionModel config : event.configuracionMovil) {
                if (config.clave != null && config.clave.equals("urlServ")) {
                    urlServerNew.copy(config);
                }
            }
            DracoManager.setConfigBy(RutasApplication.CLAVE_URL_API, urlServerNew.valor);
            app.showDeleteDatabaseDialog();
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[onEventMainThread-OnErrorConfiguracionEvent] " + e.getMessage());
        }
    }

    /**
     * Muestra un mensaje de trabajadores descansando e inicia el activity de personal.
     *
     * @return Boolean con true si estan descansando.
     */
    public boolean isTrabajadoresDescansando() {
        boolean res = false;
        try {
            if (TrabajadoresManager.getInstance().isTrabajadoresDescansando(app.getCodigoMovil())) {
                final SweetAlertDialog dlgAviso = new SweetAlertDialog(BaseActivity.this, SweetAlertDialog.WARNING_TYPE)
                      .setTitleText(getString(R.string.atencion))
                      .setContentText(getString(R.string.trabajadores_descansando))
                      .setConfirmText(getString(R.string.si))
                      .setCancelText(getString(R.string.no));

                dlgAviso.setConfirmClickListener(sweetAlertDialog -> {
                    dlgAviso.dismiss();

                    Intent i = new Intent(BaseActivity.this, PersonalActivity.class);
                    startActivity(i);
                }).show();
                res = true;
            }
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[isTrabajadoresDescansando] " + e.getMessage());
        }
        return res;
    }

    //endregion

    protected class OnClickListenerBase implements View.OnClickListener {
        Class<?> objectClass;

        public OnClickListenerBase(Class<?> objectClass) {
            this.objectClass = objectClass;
        }

        @Override
        public void onClick(View v) {
            Intent i = new Intent(v.getContext(), objectClass);
            startActivity(i);
        }
    }

   /* @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {

        if (keyCode == KeyEvent.KEYCODE_MENU) {

            AlertDialog.Builder builder = new AlertDialog.Builder(this);
            builder.setTitle("Introduzca el numero de TAG");

            // Se crea el EditText para introducir texto y se incluye en el diálogo
            final EditText input = new EditText(this);
            input.setImeOptions(EditorInfo.IME_FLAG_NO_EXTRACT_UI);
            builder.setView(input);

            // Set up the buttons
            builder.setPositiveButton("OK", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    ContentValues values = new ContentValues();

                    String fecha = Utils.datetimeToString(new Date(), "yyyy-MM-dd HH:mm:ss");

                    values.put("sensor", 36);
                    values.put("valor", input.getText().toString().getBytes());
                    values.put("fecha", fecha);

                    Intent intent = new Intent();
                    intent.setAction("com.movisat.draco.action.NEW_SENSOR");
                    intent.putExtra("values", values);
                    intent.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
                    sendBroadcast(intent);
                    dialog.cancel();

                }
            });
            builder.setNegativeButton("Cancelar", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.cancel();
                }
            });

            builder.show();


        }

        return true;
    }*/

    /**
     * Capturamos el enter del keyboard de google para enviar la confirmación.
     */
    protected class MyOnEditorActionListener implements TextView.OnEditorActionListener {

        View button;

        public MyOnEditorActionListener(View button) {
            this.button = button;
        }

        @Override
        public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
            //Enter pulsado
            if ((event != null && (event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) || (actionId == EditorInfo.IME_ACTION_DONE))
                button.callOnClick();
            return false;
        }
    }

    /**
     * Mostrar el keyboard
     */
    protected void showKeyBoard(EditText editText) {
        InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        if (inputMethodManager != null) {
            //inputMethodManager.hideSoftInputFromWindow(editText.getWindowToken(), 0);

            //inputMethodManager.toggleSoftInputFromWindow(editText.getWindowToken(),
            //        InputMethodManager.SHOW_FORCED, 0);
            inputMethodManager.hideSoftInputFromWindow(getCurrentFocus().getWindowToken(), 0);
        }

    }

    /**
     * Este método se ejecuta cuando se lee un TAG
     *
     * @param event
     */
    public void onEventMainThread(OnNotifyRFidEvent event) {
        String result = processTag(event);
        if (Utilss.isFilled(result)) {
            new ToastMensaje(this).show(result);
        }
    }

    private String processTag(OnNotifyRFidEvent event) {
        try {

            Logg.info(getSubtag(), "[onEventMainThread-OnNotifyRFidEvent] Tag recibido: " + event.tagViewModel.Tag);

            if (app == null) {
                app = RutasApplication.getInstance();
            }

            RutasModel rutaActual = RutasManager.getInstance().getRutaIniciada();
            if (rutaActual == null) {
                String message = "No hay una ruta iniciada";
                Logg.info(getSubtag(), "[onEventMainThread-OnNotifyRFidEvent] " + message);
                TagManager.getInstance().skipTag(event.tagViewModel.Tag);
                return message;
            }

            // Si la fecha de lectura del tag es anterior a la fecha de inicio de actividad, no se procesa
            if (rutaActual.FechaInicio.after(event.tagViewModel.Fecha)) {
                String message = "La fecha de lectura del tag es anterior a la fecha de inicio de actividad";
                Logg.info(getSubtag(), "[onEventMainThread-OnNotifyRFidEvent] " + message);
                TagManager.getInstance().skipTag(event.tagViewModel.Tag);
                return message;
            }

            // 2024/09/04 Ahora se hace en Actions.java
            // Quito los posibles caracteres del TAG que no
            // correspondan con un número hexadecimal
//            byte[] bt = event.tagViewModel.Tag.getBytes();
//            StringBuilder tag = new StringBuilder();
//            for (byte b : bt) {
//                if ((b >= '0' && b <= '9') || (b >= 'A' && b <= 'F'))
//                    tag.append((char) b);
//            }
//            event.tagViewModel.Tag = tag.toString();
            Logg.info(getSubtag(), "[onEventMainThread-OnNotifyRFidEvent] Tag después de modificarlo: " + event.tagViewModel.Tag);

            // No se permite leer dos veces seguidas el mismo TAG
            if (event.tagViewModel.Tag.equals(lastTAG)) {
                String message = "Se ha leído el mismo TAG";
                Logg.info(getSubtag(), "[onEventMainThread-OnNotifyRFidEvent] " + message);
                TagManager.getInstance().skipTag(event.tagViewModel.Tag);
                return message;
            }

            lastTAG = event.tagViewModel.Tag;

            // Busco el elemento con ese TAG en la ruta actual
            RutasElementosDAO rutasElementosDAO = new RutasElementosDAOProxy();
            RutasElementosModel elementoRutaModel = rutasElementosDAO.geRutaElemByTag(
                  app.getEmpresa(), rutaActual.Codigo, event.tagViewModel.Tag);

            Logg.info(getSubtag(), "[onEventMainThread-OnNotifyRFidEvent] getElementoByTAG Ruta: " + rutaActual.Codigo +
                  " Empresa: " + app.getEmpresa() + " Tag: " + event.tagViewModel.Tag + " Elemento: " + (elementoRutaModel != null ? elementoRutaModel.IdElemento : "null"));

            if (elementoRutaModel != null) {

                // Sólo se procesa una vez
                if (elementoRutaModel.Estado == RutasElementosModel.ESTADO_PROCESADO) {
                    String message = "El elemento con TAG " + event.tagViewModel.Tag + " ya había sido procesado";
                    Logg.info(getSubtag(), "[onEventMainThread-OnNotifyRFidEvent] " + message);
                    TagManager.getInstance().skipTag(event.tagViewModel.Tag);
                    return message;
                }

                ElementosDAOProxy elementosDAO = new ElementosDAOProxy();
                ElementosModel elementoProcesado = elementosDAO.getElemento(elementoRutaModel.IdElemento,
                      app.getEmpresa());

                // Compruebo si el tipo de recogida del elemento coincide con el del vehículo
                MovilesModel movil = RutasApplication.getInstance().getMovil();
                if (movil != null && movil.TipoRecogida > 0 && elementoProcesado.TipoRecogida > 0 &&
                      movil.TipoRecogida != elementoProcesado.TipoRecogida) {
                    String message = "El tipo de recogida del elemento con TAG " + event.tagViewModel.Tag + " no coincide con el del vehículo";
                    Logg.info(getSubtag(), "[onEventMainThread-OnNotifyRFidEvent] " + message);
                    TagManager.getInstance().skipTag(event.tagViewModel.Tag);
                    return message;
                }

                // Obtengo el objeto que se encargará del procesado.
                // Las incidencias no tienen tag, por lo que no debería ejecutarse esta función al procesar incidencias.
                IProcesadoAction action = FactoryProcesado.get(
                      this,
                      elementoProcesado,
                      event.tagViewModel.Fecha.getTime(),
                      true,
                      false,
                      null,
                      0,
                      false,
                        0
                );

                if (action == null) {
                    Logg.error(getSubtag(), "[onEventMainThread-OnNotifyRFidEvent] No se ha encontrado action para procesar el elemento");
                    TagManager.getInstance().skipTag(event.tagViewModel.Tag);
                    return "Ha ocurrido un error al procesar el TAG " + event.tagViewModel.Tag;
                }

                action.clickButton();

            } else {

                // Compruebo si el elemento existe en la tabla de elementos
                // por si se trata de un elemento procesado fuera de ruta

                ElementosDAOProxy elementosDAO = new ElementosDAOProxy();
                ElementosModel elem = elementosDAO.getByTag(app.getEmpresa(), event.tagViewModel.Tag);

                if (elem == null) {
                    String message = "No se ha encontrado el elemento con TAG " + event.tagViewModel.Tag;
                    Logg.info(getSubtag(), "[onEventMainThread-OnNotifyRFidEvent] " + message);
                    TagManager.getInstance().skipTag(event.tagViewModel.Tag);
                    return message;
                }

                if (TagManager.getInstance().isOffRouteDone(rutaActual.Codigo, event.tagViewModel.Tag)) {
                    String msg = "El elemento fuera de ruta con TAG " + event.tagViewModel.Tag + " ya había sido procesado";
                    Logg.info(getSubtag(), msg);
                    TagManager.getInstance().skipTag(event.tagViewModel.Tag);
                    return msg;
                }

                // Compruebo si el tipo de recogida del elemento coincide con el del vehículo
                MovilesModel movil = RutasApplication.getInstance().getMovil();
                if (movil != null && movil.TipoRecogida > 0 && elem.TipoRecogida > 0 &&
                      movil.TipoRecogida != elem.TipoRecogida) {
                    String message = "El tipo de recogida del elemento con TAG " + event.tagViewModel.Tag + " no coincide con el del vehículo";
                    Logg.info(getSubtag(), "[onEventMainThread-OnNotifyRFidEvent] " + message);
                    TagManager.getInstance().skipTag(event.tagViewModel.Tag);
                    return message;
                }

                // Obtengo el objeto que se encargará del procesado según la actividad de la ruta
                // y si es de nivel de llenado saco el formulario
                ProcesadoPorActividadDAO procesadoPorActividadDAO = new ProcesadoPorActividadDAO();
                if (procesadoPorActividadDAO.getTipoProcesadoBy(rutaActual.ActividadId) !=
                      ProcesadoPorActividadModel.NIVEL_LLENADO) {
//                    String message = "El elemento con TAG " + event.tagViewModel.Tag + " ya había sido procesado";
//                    Logg.info(getSubtag(), "[onEventMainThread-OnNotifyRFidEvent] " + message);
//                    return message;
                    // Obtengo el objeto que se encargará del procesado.
                    // Las incidencias no tienen tag, por lo que no debería ejecutarse esta función al procesar incidencias.
                    IProcesadoAction action = FactoryProcesado.get(
                          this,
                          elem,
                          event.tagViewModel.Fecha.getTime(),
                          true,
                          true,
                          null,
                          0,
                          false,
                            0
                    );

                    if (action == null) {
                        Logg.error(getSubtag(), "[onEventMainThread-OnNotifyRFidEvent] No se ha encontrado action para procesar el elemento");
                        TagManager.getInstance().skipTag(event.tagViewModel.Tag);
                        return "Ha ocurrido un error al procesar el TAG " + event.tagViewModel.Tag;
                    }

                    action.clickButton();
                    new ToastMensaje(this)
                            .show(getString(R.string.elemento_procesado_fuera_ruta_params,
                                    event.tagViewModel.Tag), R.mipmap.procesar1);
                    return "";
                }

                new ClickProcesadoNivelLlenadoAction(
                      this,
                      elem,
                      event.tagViewModel.Fecha.getTime(),
                      true,
                      true,
                      0,
                        0,
                        false
                ).clickButton();

                new ToastMensaje(this).
                      show(getString(R.string.elemento_procesado_fuera_ruta_params,
                            event.tagViewModel.Tag), R.mipmap.procesar1);
            }
            return null;
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[onEventMainThread-OnNotifyRFidEvent] " + e.getMessage());
            lastTAG = "";
            TagManager.getInstance().skipTag(event.tagViewModel.Tag);
            return "Ha ocurrido un error al procesar el TAG " + event.tagViewModel.Tag;
        }
    }

    /**
     * Este método se ejecuta cuando se recibe una entrada digital u otros sensores como pesaje por ej.
     *
     * @param event
     */
    public void onEventMainThread(OnNotifyDinEvent event) {

        try {

            // Cuando se hacen diagnósticos no se hace nada con las cámaras
            if (DiagnosisActivity.isVisible) {
                return;
            }

            ConfiguracionModel configuracionModel;

            // Compruebo si se ha configurado algún sensor para procesar elementos
            configuracionModel = DracoManager.getConfigBy(ElementosCercanosViewModel.CONF_SENSOR_PROCESAR, "0");
            Set<Integer> sensorProcesarIds = new HashSet<>();
            if (configuracionModel != null && configuracionModel.valor != null &&
                  !configuracionModel.valor.isEmpty()) {
                for (String part : configuracionModel.valor.split(";")) {
                    try {
                        sensorProcesarIds.add(Integer.parseInt(part));
                    } catch (NumberFormatException e) {
                        Logg.error(getSubtag(), "[onEventMainThread-OnNotifyDinEvent] Error al parsear el sensor: " + part);
                    }
                }
            }

            // Para procesar elementos sólo se tratan las activaciones de sensor
            if (sensorProcesarIds.contains(event.dinViewModel.id) && event.dinViewModel.valor) {

                RutasModel rutaActual = RutasManager.getInstance().getRutaIniciada();

                if (rutaActual != null) {
                    if (rutaActual.esRutaDeIncidencias())
                        procesarIncidencia(event, rutaActual);
                    else {
                        if (event.dinViewModel.id == 42 && event.dinViewModel.entrada != 0) {
                            event.dinViewModel.setId(event.dinViewModel.entrada);
                        }
                        procesarElemento(event, rutaActual);
                    }
                }
            }

            // Asigno la incidencia según el sensor para enviar luego más tarde las fotos automática
            if (event.dinViewModel.id > 0 && event.dinViewModel.id < 8 && event.dinViewModel.valor)
                setIncidenceAuto(event.dinViewModel.id);

            // Con la entrada 1 compruebo si tengo que hacer las fotos automáticas y
            // enviar la incidencia
            //if (event.dinViewModel.id > 0 && event.dinViewModel.id < 5 && event.dinViewModel.valor)
            //checkIncidenceAuto();

        } catch (Throwable e) {
            Logg.error(getSubtag(), "[onEventMainThread-OnNotifyDinEvent] " + e.getMessage());
        }
    }

    /**
     * Procesa elementos cercanos cuando llega una entrada digital.
     * Implementa todas las reglas de vehículos bicompartimentados.
     */
    private void procesarElemento(OnNotifyDinEvent event, RutasModel rutaActual) {
        ConfiguracionModel configuracionModel;
        RutasElementosModel elementoRutaModel = null;

        /* ------------------------------------------------------------------
         * 1.  Obtener el radio de búsqueda configurado
         * ------------------------------------------------------------------ */
        int radio = ElementosCercanosViewModel.RADIO_ELEMENTOS_DEFECTO;
        configuracionModel = DracoManager.getConfigBy(ElementosCercanosViewModel.CONF_RADIO_ELEMENTOS, "30");
        if (configuracionModel.valor != null && !configuracionModel.valor.isEmpty()) {
            radio = Integer.parseInt(configuracionModel.valor);
            Logg.info(getSubtag(), "[procesarElemento] Radio obtenido desde configuración: " + radio);
        } else {
            Logg.info(getSubtag(), "[procesarElemento] Radio por defecto: " + radio);
        }

        /* ------------------------------------------------------------------
         * 2.  Obtener los elementos cercanos y filtrados a la posición del vehículo
         * ------------------------------------------------------------------ */
        ProcesaElementosPorEntradaDigitalUseCase useCase = new ProcesaElementosPorEntradaDigitalUseCase();
        List<ElementosCercanosViewModel> listaElementosCercanos = useCase.execute(
                app.getEmpresa(),
                rutaActual.Codigo,
                event.dinViewModel.posicion,
                radio,
                true,
                event.dinViewModel.id,
                false
        );

        if (listaElementosCercanos.isEmpty()) {
            Logg.info(getSubtag(), "[procesarElemento] No hay elementos que cumpla las condiciones para procesar en el radio de " + radio);
            return;
        }

        /* ------------------------------------------------------------------
         * 3.  Procesar el primer elemento de la lista
         * ------------------------------------------------------------------ */
        Logg.info(getSubtag(), "[procesarElemento] Elementos disponibles para procesar:" + listaElementosCercanos.size());
        elementoRutaModel = listaElementosCercanos.get(0).getElementoRuta();
        if (elementoRutaModel != null && elementoRutaModel.Estado == RutasElementosModel.ESTADO_NO_PROCESADO) {

            ElementosDAOProxy elementosDAO = new ElementosDAOProxy();
            ElementosModel elementosModel = elementosDAO.getElemento(
                    elementoRutaModel.IdElemento, app.getEmpresa());

            IProcesadoAction action = FactoryProcesado.get(
                    this,
                    elementosModel,
                    event.dinViewModel.Fecha.getTime(),
                    false,
                    false,
                    null,
                    listaElementosCercanos.size(),
                    false,
                    event.dinViewModel.id
            );
            if (action != null) action.clickButton();
        }
    }

    private void procesarIncidencia(OnNotifyDinEvent event, RutasModel rutaActual) {
        ConfiguracionModel configuracionModel;

        int radio = ElementosCercanosViewModel.RADIO_ELEMENTOS_DEFECTO;
        configuracionModel = DracoManager.getConfigBy(ElementosCercanosViewModel.CONF_RADIO_ELEMENTOS, "30");
        if (configuracionModel != null && configuracionModel.valor != null &&
              !configuracionModel.valor.isEmpty()) {
            radio = Integer.parseInt(configuracionModel.valor);
            Logg.info(getSubtag(), "[procesarIncidencia] Radio obtenido desde la configuración del draco: " + radio);
        } else {
            Logg.info(getSubtag(), "[procesarIncidencia] Radio por defecto al no haber encontrado la configuración del draco: " + radio);
        }

        IncidenciasRutaDAO incidenciasRutaDAO = new IncidenciasRutaDAO();
        ArrayList<ElementosCercanosViewModel> nearIncidences =
              incidenciasRutaDAO.getIncidenciasCercanas(app.getEmpresa(), rutaActual.Codigo,
                    event.dinViewModel.posicion, radio, true);

        IncidenciasRutaModel incidenciaRutaModel = null;

        int nearIncidencesCount = (Utilss.isFilled(nearIncidences) ? nearIncidences.size() : 0);
        Logg.info(getSubtag(), "[procesarIncidencia] Incidencias encontrados en el radio de " + radio + ": " + nearIncidencesCount);

        if (nearIncidences != null && !nearIncidences.isEmpty())
            incidenciaRutaModel = nearIncidences.get(0).getIncidencia();

        if (rutaActual != null && incidenciaRutaModel != null) {

            // Sólo se procesa una vez
            if (incidenciaRutaModel.Estado == RutasElementosModel.ESTADO_NO_PROCESADO) {

                // Obtengo el objeto que se encargará del procesado
                IProcesadoAction action = FactoryProcesado.get(
                      this,
                      null,
                      event.dinViewModel.Fecha.getTime(),
                      false,
                      false,
                      incidenciaRutaModel,
                      nearIncidencesCount,
                      false,
                        0
                );
                if (action != null)
                    action.clickButton();
            }
        }
    }

    private void setIncidenceAuto(int idSensor) {

        try {
            int modeloInci = 0;
            int tipoInci = 0;
            int motivoInci = 0;

            if (idSensor != 4) {
                ConfiguracionModel conf = DracoManager.getConfigBy("inciModeloFoto" + idSensor, "0");
                if (conf == null)
                    return;
                modeloInci = Integer.parseInt(conf.valor);

                conf = DracoManager.getConfigBy("inciTipoFoto" + idSensor, "0");
                if (conf == null)
                    return;
                tipoInci = Integer.parseInt(conf.valor);

                conf = DracoManager.getConfigBy("inciMotivoFoto" + idSensor, "0");
                if (conf == null)
                    return;
                motivoInci = Integer.parseInt(conf.valor);
            }

            if (modeloInci > 0 && tipoInci > 0 && motivoInci > 0) {

                // Indico los datos de la incidencia e inicializo la lista de imagenes
                AutoPhoto.modeloInci = modeloInci;
                AutoPhoto.tipoInci = tipoInci;
                AutoPhoto.motivoInci = motivoInci;
                AutoPhoto.clearImages();

                // Inicializo el temporizador para envío de incidencias
                AutoPhoto.timerAutoSend = System.currentTimeMillis();

                // Creo el hilo que se encarga de enviar la incidencia si después de 1 minuto no
                // se ha pulsado el evento para hacer la foto
                if (AutoPhoto.threadSend == null && (idSensor == 7 || idSensor == 6)) {

                    AutoPhoto.threadSend = new Thread() {

                        @Override
                        public void run() {

                            try {

                                // Después de un minuto si no se ha pulsado la entrada para hacer la
                                // foto se envía la incidencia pendiente
                                while (AutoPhoto.timerAutoSend > 0 && System.currentTimeMillis() - AutoPhoto.timerAutoSend < 60000)
                                    sleep(1000);

                                // Si se ha pasado el tiempo y no se ha pulsado el evento para hacer la foto
                                // envío la incidencia sin foto
                                if (AutoPhoto.timerAutoSend > 0)
                                    AutoPhoto.sendIncidence();

                            } catch (Throwable e) {
                            }

                            AutoPhoto.timerAutoSend = 0;
                            AutoPhoto.threadSend = null;
                        }
                    };

                    AutoPhoto.threadSend.start();
                }

                if (idSensor >= 1 && idSensor <= 3) {
                    checkIncidenceAuto(idSensor);
                }

            } else {

                if (idSensor == 4) { // Botón rojo para envío de fotos
                    // Inicializo el temporizador para envío de incidencias para que no se envíe
                    // la incidencia dado que se enviará junto con las fotos
                    AutoPhoto.timerAutoSend = 0;
                    checkIncidenceAuto(idSensor);
                }

            }

        } catch (Exception e) {
            Logg.error(getSubtag(), "[setIncidenceAuto] " + e.getMessage());
        }
    }

    private void checkIncidenceAuto(int id) {

        try {

            AutoPhoto.clearImages();
            capture2 = true; // Siempre se hacen dos fotos

            Intent intent = new Intent(RutasApplication.getInstance(), WifiCameraActivity.class);
            intent.putExtra("camara", 1);
            intent.putExtra("auto", true);
            startActivityForResult(intent, MainActivity.REQUEST_WIFI_CAMERA1_AUTO);

            //            if (id == 3) {
            //                // Realizo las fotografías con la primera cámara
            //                Intent intent = new Intent(RutasApplication.getInstance(), WifiCameraActivity.class);
            //                intent.putExtra("camara", 2);
            //                intent.putExtra("auto", true);
            //                startActivityForResult(intent, MainActivity.REQUEST_WIFI_CAMERA2_AUTO);
            //
            //            } else {
            //                if (id == 2) capture2 = true;
            //                Intent intent = new Intent(RutasApplication.getInstance(), WifiCameraActivity.class);
            //                intent.putExtra("camara", 1);
            //                intent.putExtra("auto", true);
            //                startActivityForResult(intent, MainActivity.REQUEST_WIFI_CAMERA1_AUTO);
            //            }

        } catch (Exception e) {
            Logg.error(getSubtag(), "[checkIncidenceAuto] " + e.getMessage());
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        byte[] foto;

        try {

            super.onActivityResult(requestCode, resultCode, data);

            switch (requestCode) {
                case ACTIVITY_ACTIVACION:
                    if (ActivacionActivity.checkLicense(this)) {
                        MainActivity.getInstance().checkGpsActive();
                        MainActivity.getInstance().checkFirstSincro();
                    } else
                        finish();

                    break;

                case ACTIVITY_SCREEN_SLIDE:
                    if (app.isPrimeraSincronizacion())
                        finish();

                    break;

                case REQUEST_WIFI_CAMERA1_AUTO:
                    // Añado las fotos de la primera cámara
                    foto = WifiCameraActivity.getLastPhoto(1);
                    if (foto != null)
                        AutoPhoto.addImage(foto);
                    foto = WifiCameraActivity.getLastPhoto(2);
                    if (foto != null)
                        AutoPhoto.addImage(foto);

                    // Realizo las fotografías con la segunda cámara
                    if (capture2) {
                        // Obtengo el tiempo que tengo que esperar antes de hacer la segunda foto
                        // Por defecto 10 segundos
                        ConfiguracionModel confModel = DracoManager.getConfigBy("intervaloFoto", "10");
                        Thread.sleep(Integer.parseInt(confModel.valor) * 1000L);
                        Intent intent2 = new Intent(RutasApplication.getInstance(), WifiCameraActivity.class);
                        intent2.putExtra("camara", 2);
                        intent2.putExtra("auto", true);
                        startActivityForResult(intent2, MainActivity.REQUEST_WIFI_CAMERA2_AUTO);
                    } else {
                        // Envío la incidencia con las fotos al servidor
                        if (AutoPhoto.tipoInci != 0 && AutoPhoto.motivoInci != 0 && AutoPhoto.modeloInci != 0)
                            AutoPhoto.sendIncidence();
                    }

                    break;

                case REQUEST_WIFI_CAMERA2_AUTO:
                    capture2 = false;
                    // Añado las fotos de la segunda cámara
                    foto = WifiCameraActivity.getLastPhoto(1);
                    if (foto != null)
                        AutoPhoto.addImage(foto);
                    foto = WifiCameraActivity.getLastPhoto(2);
                    if (foto != null)
                        AutoPhoto.addImage(foto);

                    // Envío la incidencia con las fotos al servidor
                    AutoPhoto.sendIncidence();

                    break;
            }
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[onActivityResult] " + e.getMessage());
        }
    }

    private class OnFindText implements androidx.appcompat.widget.SearchView.OnQueryTextListener {

        @Override
        public boolean onQueryTextSubmit(String query) {
            //Al devolver false automaticamente al pulsar la lupa se quita el teclado.
            return false;
        }

        @Override
        public boolean onQueryTextChange(String newText) {
            filtroBusqueda = newText;
            find(filtroBusqueda);
            return true;
        }

    }

    /**
     * Metodo para realizar busquedas. Lo implementara quien necesite utilizarlo.
     */
    protected void find(String filtro) {
    }

    //TODO: Vamos a usar el ocultar barras de dispositivo¿?.
    // This snippet hides the system bars.
    private void hideSystemUI() {
        try {

            View mDecorView = getWindow().getDecorView();
            // Set the IMMERSIVE flag.
            // Set the content to appear under the system bars so that the content
            // doesn't resize when the system bars hide and show.
            mDecorView.setSystemUiVisibility(
                  View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION // hide nav bar
                        | View.SYSTEM_UI_FLAG_FULLSCREEN // hide status bar
                        | View.SYSTEM_UI_FLAG_IMMERSIVE
            );
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[hideSystemUI] " + e.getMessage());
        }

    }

    // This snippet shows the system bars. It does this by removing all the flags
    // except for the ones that make the content appear under the system bars.
    private void showSystemUI() {

        try {

            View mDecorView = getWindow().getDecorView();

            mDecorView.setSystemUiVisibility(
                  View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);

        } catch (Throwable e) {
            Logg.error(getSubtag(), "[showSystemUI] " + e.getMessage());
        }
    }

    /**
     * Reproduce el último mensaje no leído recibido si es lo bastante reciente.
     */
    private void playLastReceivedMessage() {

        try {

            // Si el motor TextToSpeech está iniciado y no hay una llamada en curso...
            if (TTSManager.get().isTextToSpeechInitialized() && !PhoneUtils.isActiveCall()) {

                // Se obtienen los mensajes recibidos
                List<MensajesModel> mensajesRecibidos = new MensajesDAO().getMensajesRecibidos(app.getImei());
                if (mensajesRecibidos.size() > 0) {

                    final MensajesModel lastMessage = mensajesRecibidos.get(0);
                    // Si el último mensaje recibido no está leído y se ha recibido hace menos
                    // de 120 segundos, se reproduce con voz
                    Date limitDate = new Date(new Date().getTime() - 120000);
                    if (lastMessage.Leido == 0 && lastMessage.Fecha.after(limitDate)) {

                        // Se reproduce el mensaje tras un breve retardo
                        new Thread(new Runnable() {
                            @Override
                            public void run() {
                                try {
                                    Thread.sleep(1000);
                                    TTSManager.get().speak(lastMessage.Texto);
                                } catch (Throwable e) {
                                }
                            }
                        }).start();
                    }
                }
            }
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[playLastReceivedMessage] " + e.getMessage());
        }
    }

    public void mostrarMensaje() {

        String mensaje = "PROBLEMA TARJETA SIM DETECTADO.\n " +
              "Incidencia insertada, el dispositivo se reiniciará automáticamente";

        /*final SweetAlertDialog dlg = new SweetAlertDialog(BaseActivity.this, SweetAlertDialog.WARNING_TYPE)
                .setTitleText(getString(R.string.atencion))
                .setContentText(mensaje)
                .setConfirmText("OK")
                .setCancelText("Cancelar");

        dlg.setConfirmClickListener(new SweetAlertDialog.OnSweetClickListener() {
            @Override
            public void onClick(SweetAlertDialog sweetAlertDialog) {
                try {
                    dlg.dismiss();
                    Utils.deviceReset();
                }catch (Throwable e){
                    logger.WriteError(e);
                }
            }
        }).show();*/

        try {
            new ToastMensaje(BaseActivity.this).show(mensaje, R.mipmap.incidencia);
        } catch (Throwable e) {
            Logg.error(getSubtag(), "[mostrarMensaje] " + e.getMessage());
        }

    }

    /**
     * Método que devuelve el código para restaurar la aplicación.
     * Se calcula en función de la fecha actual.
     *
     * @return Year - (Month * Day)
     */
    public static int getResetCode() {
        Calendar calendar = Calendar.getInstance(TimeZone.getDefault());
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        int month = calendar.get(Calendar.MONTH) + 1; // Enero es 0, por eso sumamos 1
        int year = calendar.get(Calendar.YEAR);

        return year - (month * day);
    }

    /**
     * Devuelve el subtag de la clase, es decir, el nombre de la clase hija.
     *
     * @return Subtag de la clase.
     */
    public String getSubtag() {
        return getClass().getSimpleName();
    }

}
