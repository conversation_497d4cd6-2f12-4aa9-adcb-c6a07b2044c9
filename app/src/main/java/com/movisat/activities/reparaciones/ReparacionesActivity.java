package com.movisat.activities.reparaciones;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ListView;
import android.widget.TextView;

import com.movisat.activities.BaseActivity;
import com.movisat.activities.R;
import com.movisat.adapters.genericos.ListaAdapter;
import com.movisat.adapters.reparaciones.ListAdapterReparaciones;
import com.movisat.dao.elementos.ElementosDAOProxy;
import com.movisat.dao.reparaciones.ReparacionesDAO;
import com.movisat.models.elementos.ElementosModel;
import com.movisat.models.reparaciones.ReparacionesModel;

import java.util.ArrayList;


public class ReparacionesActivity extends BaseActivity {

    private ListView listViewReparaciones;
    private ArrayList<ReparacionesModel> reparaciones;
    private ListaAdapter listAdapter;
    private static ReparacionesActivity instance = null;
    public static final String ID_ELEMENTO = "idElemento";
    public static final String ID_REPARACION = "idReparacion";
    private int idElemento;
    private boolean isAlgoRevisado = false;

    public static ReparacionesActivity getInstance(){return instance;}

    public void setContentView(){setContentView(R.layout.activity_reparaciones);}

    @Override
    public void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            instance = this;
            fillListaReparaciones();
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }


    @Override
    protected void init()
    {
        try {
            super.init();
            listViewReparaciones = findViewById(R.id.ListView_reparaciones);


            //Pueden ser nulos idElemento, tipoElemento e idRuta
            Bundle bundle = getIntent().getExtras();
            idElemento = bundle.getInt(ID_ELEMENTO);

            ElementosDAOProxy elementosDAO = new ElementosDAOProxy();
            ElementosModel elementosModel = elementosDAO.getElemento(idElemento, app.getEmpresa());
            if(elementosModel != null) {
                TextView titulo = findViewById(R.id.textView_titulo_reparaciones);
                titulo.setText(getString(R.string.reparaciones_titulo) + ": " + elementosModel.Nombre);
            }

        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    @Override
    protected void setControls()
    {
        try {
            listViewReparaciones.setOnItemClickListener(new AdapterView.OnItemClickListener() {
                @Override
                public void onItemClick(AdapterView<?> pariente, View view, int posicion, long id) {
                    try {
                        ReparacionesModel reparacionElegida = (ReparacionesModel) listViewReparaciones.getItemAtPosition(posicion);
                        Intent i = new Intent(ReparacionesActivity.getInstance(), ReparacionesObservacionesActivity.class);

                        i.putExtra(ID_ELEMENTO, idElemento);
                        i.putExtra(ID_REPARACION, reparacionElegida.Id);
                        startActivityForResult(i, ACTIVITY_REPARACIONES_OBSERVACIONES);
                    }catch (Throwable e){
                        logger.WriteError(e);
                    }
                }
            });
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    /**
     * Construye la lista de reparaciones
     */
    public void fillListaReparaciones()
    {
        try {
            ReparacionesDAO reparacionesDAO = new ReparacionesDAO();
            reparaciones = (ArrayList<ReparacionesModel>) reparacionesDAO.getAllReparacionesBy(app.getCodigoMovil());

            if (reparaciones != null) {
                listAdapter = new ListAdapterReparaciones(this, R.layout.entrada_1text, reparaciones);
                listViewReparaciones.setAdapter(listAdapter);
            } else {
                finish();
            }
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        try {
            switch(requestCode) {
                case BaseActivity.ACTIVITY_REPARACIONES_OBSERVACIONES:
                    if (resultCode == RESULT_OK) {
                        // Marco que se ha revisado algo para posteriormente procesar el elemento
                        isAlgoRevisado = true;
                    }
                    break;
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void onBotonAtrasClick(Activity activity) {
        try {
            if (isAlgoRevisado)
                setResult(RESULT_OK);
            else
                setResult(RESULT_CANCELED);
            finish();
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }
}
