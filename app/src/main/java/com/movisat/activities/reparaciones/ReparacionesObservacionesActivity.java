package com.movisat.activities.reparaciones;


import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import com.jobqueue.JobManager;
import com.movisat.activities.BaseActivity;
import com.movisat.activities.R;
import com.movisat.application.RutasApplication;
import com.movisat.dao.elementos.ElementosDAOProxy;
import com.movisat.dao.rutas.RutasDAO;
import com.movisat.dao.rutas.RutasDAOProxy;
import com.movisat.jobs.reparaciones.InsertarReparacionesJob;
import com.movisat.managers.RutasManager;
import com.movisat.models.elementos.ElementosModel;
import com.movisat.models.rutas.RutasModel;
import com.movisat.utilities.SoftKeyboard;
import com.movisat.utilities.ToastMensaje;
import com.movisat.utilities.Utils;
import com.movisat.viewmodels.reparaciones.ReparacionesHViewModel;

public class ReparacionesObservacionesActivity extends BaseActivity{

    private Button btnEnviar;
    private EditText editObservaciones;
    private Button btnHideKeyboard;
    private static ReparacionesObservacionesActivity instance = null;
    private String sObservaciones;
    private int idReparacion;
    private int idElemento;
    private SoftKeyboard softKeyboard;

    public static ReparacionesObservacionesActivity getInstance(){return instance;}

    @Override
    public void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            instance = this;
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);
        }catch(Throwable e){
            logger.WriteError(e);
        }
    }

    @Override
    protected void setContentView() {setContentView(R.layout.activity_observaciones_reparaciones);}

    @Override
    protected void init()
    {
        try {
            super.init();
            editObservaciones = findViewById(R.id.editText_observaciones_reparaciones);
            btnEnviar = findViewById(R.id.btnEnviar);
            btnHideKeyboard = findViewById(R.id.btnHideKeyboard);

            //Pueden ser nulos idElemento, tipoElemento e idRuta
            Bundle bundle = getIntent().getExtras();
            idElemento = bundle.getInt(ReparacionesActivity.ID_ELEMENTO);
            idReparacion = bundle.getInt(ReparacionesActivity.ID_REPARACION);

            ElementosDAOProxy elementosDAO = new ElementosDAOProxy();
            ElementosModel elementosModel = elementosDAO.getElemento(idElemento, app.getEmpresa());
            if (elementosModel != null) {
                TextView titulo = findViewById(R.id.textView_titulo_reparaciones_observaciones);
                titulo.setText(getString(R.string.reparaciones_observaciones_titulo) + ": " + elementosModel.Nombre);
            }
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    @Override
    protected void setControls()
    {
        try {
            btnEnviar.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    try {
                        ElementosDAOProxy elementosDAO = new ElementosDAOProxy();
                        ElementosModel elementoReparacion = elementosDAO.getElemento(idElemento, RutasApplication.getInstance().getEmpresa());
                        insertReparacion(elementoReparacion);
                    } catch (Throwable e){
                        logger.WriteError(e);
                    }
                }
            });

            // Se crea el objeto para manejar el SoftKeyboard y saber cuándo se muestra / oculta
            softKeyboard = new SoftKeyboard(this);
            softKeyboard.setOnKeyboardVisibilityChangeListener(new SoftKeyboard.OnKeyboardVisibilityChangeListener() {
                @Override
                public void onVisibilityChange(final boolean keyboardVisible) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                btnHideKeyboard.setVisibility(keyboardVisible ? View.VISIBLE : View.INVISIBLE);
                            } catch (Throwable e) {
                                logger.WriteError(e);
                            }
                        }
                    });
                }
            });

            // Listener para ocultar el teclado al pulsar el botón de ocultar
            btnHideKeyboard.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    try {
                        softKeyboard.hide();
                    } catch (Throwable e){
                        logger.WriteError(e);
                    }
                }
            });

        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    /**
     * Creamos el objeto ReparacionesHViewModel y llamamos al job
     */
    private void insertReparacion(ElementosModel elementoReparacion)
    {
        try {
            sObservaciones = editObservaciones.getText().toString();

            ReparacionesHViewModel reparacion = new ReparacionesHViewModel();
            reparacion.CodigoMovil = app.getCodigoMovil();
            reparacion.IdEmpresa = app.getEmpresa();
            reparacion.IdReparacion = idReparacion;
            RutasModel ruta = RutasManager.getInstance().getRutaIniciada();
            reparacion.IdRuta = ruta.Codigo;
            // reparacion.IdRutaH = 0; // Este dato se cogerá a la hora de enviar el Job
            // Si lo tenemos lo asignamos ya
            String uuid = RutasManager.getInstance().getUUID(ruta.Codigo);
            reparacion.IdRutaH = RutasManager.getInstance().getIdRutaH(uuid);
            reparacion.CodigoElemento = idElemento;
            reparacion.X = elementoReparacion.X;
            reparacion.Y = elementoReparacion.Y;
            reparacion.Observaciones = sObservaciones;
            reparacion.Fecha = Utils.StringToDateTime(Utils.nowDateTimeToString(getString(R.string.mysql_date)));

            JobManager jobManager = RutasApplication.getInstance().getJobManager();
            jobManager.addJobInBackground(new InsertarReparacionesJob(reparacion));

            logger.WriteInfo(getString(R.string.reparaciones_reparacion_insertada));

            ToastMensaje toast = new ToastMensaje(getInstance());
            toast.show(getString(R.string.reparaciones_reparacion_insertada), R.mipmap.reparacionmastoast);

            setResult(RESULT_OK);
            finish();
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    @Override
    public void finish() {
        if (softKeyboard != null)
            softKeyboard.detach();
        super.finish();
    }
}
