package com.movisat.activities.personal;

import android.content.Intent;
import android.graphics.Color;
import android.nfc.Tag;
import android.os.Bundle;
import android.view.Menu;
import android.view.View;
import android.widget.Button;
import android.widget.ListView;

import androidx.annotation.NonNull;

import com.jobqueue.JobManager;
import com.movisat.activities.R;
import com.movisat.adapters.genericos.ListaAdapter;
import com.movisat.adapters.personal.ListAdapterTrabajadoresIdentificados;
import com.movisat.application.RutasApplication;
import com.movisat.dao.trabajadores.TrabajadoresDAO;
import com.movisat.events.nfc.OnNfcReaded;
import com.movisat.events.personal.OnNotifyAfterSincronizacionTrabajadores;
import com.movisat.events.personal.OnNotifyBeforeSincronizacionTrabajadores;
import com.movisat.events.personal.OnSavedDescansosHTrabajadoresEvent;
import com.movisat.events.personal.OnSavedTurnosHEvent;
import com.movisat.jobs.trabajadores.InsertarDescansosHJob;
import com.movisat.jobs.trabajadores.InsertarJornadasHJob;
import com.movisat.jobs.trabajadores.InsertarTurnosHJob;
import com.movisat.log.Logg;
import com.movisat.managers.ConfiguracionManager;
import com.movisat.managers.TrabajadoresManager;
import com.movisat.models.configuracion.ConfiguracionModel;
import com.movisat.models.trabajadores.TrabajadoresModel;
import com.movisat.utilities.DateTimePicker;
import com.movisat.utilities.ToastMensaje;
import com.movisat.utilities.Utils;
import com.movisat.viewmodels.trabajadores.DescansosHViewModel;
import com.movisat.viewmodels.trabajadores.JornadasViewModel;
import com.movisat.viewmodels.trabajadores.TurnosViewModel;

import java.util.ArrayList;
import java.util.Date;

import cn.pedant.SweetAlert.SweetAlertDialog;


public class PersonalActivity extends BasePersonalActivity {
    private static final int CODE_DESCANSO = 1;
    private static final int CODE_IDENTIFICACION = 2;
    public static final String CLAVE_JORNADA = "Inicio_JornadaTrabajo_EcoRutas";
    //determina si viene de una activity diferente de la de rutas
    public boolean activityFueraRuta = false;
    //Job Manager
    private JobManager jobManager;
    private static PersonalActivity instance = null;
    private Button btnInicioTurno;
    private Button btnFinTurno;
    private Button btnDescanso;
    private Button btnFinDescanso;
    //
    private Button btnFinTurnoTodos;
    // Indica si se ha lanzado la activity desde el menú principal
    // o desde el inicio/fin de actividad
//    public boolean activityFueraRuta = false;
    //Trabajadores Identificados
    private ArrayList<TrabajadoresModel> trabajadoresIdentificados;
    private ListView listViewTrabajadoresIdentificados;
    private ListaAdapter listAdapter;
    //Trabajadores elegidos de la lista de trabajadores identificados
    private ArrayList<TrabajadoresModel> trabajadoresElegidos = new ArrayList<>();
    //Acceso a la capa de datos
    private final TrabajadoresDAO trabajadoresDAO = new TrabajadoresDAO();
    private boolean seleccionarTodosTrabajadores = false;
    private static Date finTurno;

    public static PersonalActivity getInstance() {
        return instance;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            instance = this;

            setTituloActionBar(R.string.title_ecosat_personal);

            Intent intent = getIntent();

            if (intent != null && intent.hasExtra("tipoIdentificador")) {
                int tipoIdentificador = intent.getIntExtra("tipoIdentificador", INICIO_TURNO);
                // Seleccionamos todos los trabajadores identificados si venimos de la activity de rutas
                // con tipoIdentificador = FIN_TURNO
                if (tipoIdentificador == FIN_TURNO) {
                    seleccionarTodosTrabajadores = true;
                }
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    public void onResume() {
        try {
            super.onResume();
            if (trabajadoresIdentificados == null || trabajadoresIdentificados.isEmpty()) {
                btnFinTurnoTodos.setEnabled(false);
            }

            fillListaTrabajadores();

            if (app.isSincronizando(RutasApplication.GROUP_JOB_TRABAJADORES)) {
                onEventMainThread(new OnNotifyBeforeSincronizacionTrabajadores());
            }

            refreshListViewTrabajadoresIdentificados();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
    }

    @Override
    public void onStop() {
        super.onStop();
    }

    public void onEventMainThread(OnSavedTurnosHEvent event) {
        try {
            refreshListViewTrabajadoresIdentificados();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void onEventMainThread(OnSavedDescansosHTrabajadoresEvent event) {
        try {
            refreshListViewTrabajadoresIdentificados();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void onEventMainThread(OnNotifyBeforeSincronizacionTrabajadores event) {
        try {
            showProgressBar(true);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void onEventMainThread(OnNotifyAfterSincronizacionTrabajadores event) {
        try {
            showProgressBar(false);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        try {
            //getMenuInflater().inflate(R.menu.menu_buscar, menu);
            getMenuInflater().inflate(R.menu.menu_main, menu);
            return true;
        } catch (Throwable e) {
            logger.WriteError(e);
            return false;
        }
    }

    @Override
    protected void setContentView() {
        setContentView(R.layout.activity_personal);
    }

    @Override
    protected void init() {
        try {
            btnInicioTurno = findViewById(R.id.btnInicioTurno);
            btnFinTurno = findViewById(R.id.btnFinTurno);
            btnDescanso = findViewById(R.id.btnDescanso);
            btnFinDescanso = findViewById(R.id.btnFinDescanso);
            btnFinTurnoTodos = findViewById(R.id.btnFinTurnoTodos);
            listViewTrabajadoresIdentificados = findViewById(R.id.ListView_listado_personal);

            Bundle bundle = getIntent().getExtras();
            if (bundle != null) {
                activityFueraRuta = bundle.getBoolean("desdeMenu", false);
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void setControls() {
        try {
            //Descanso
            btnDescanso.setOnClickListener(v -> {
                try {
                    if (trabajadoresElegidos != null) {
                        if (!TrabajadoresManager.getInstance().isTrabajadoresDescansando(trabajadoresElegidos)) {
                            Intent i = new Intent(PersonalActivity.getInstance(), PersonalDescansoActivity.class);
                            i.putExtra("trabajadoresElegidos", trabajadoresElegidos);
                            startActivityForResult(i, CODE_DESCANSO);
                        } else {
                            String mensaje = getString(R.string.personal_trabajador_descansando_iniciado);
                            new ToastMensaje(instance).show(mensaje, R.mipmap.descanso);
                        }
                    } else {
                        String mensaje = getString(R.string.personal_error_seleccion_trabajador);
                        ToastMensaje toast = new ToastMensaje(getInstance());
                        toast.show(mensaje, R.mipmap.trabajar);
                    }
                } catch (Throwable e) {
                    logger.WriteError(e);
                }
            });

            //Fin Descanso
            btnFinDescanso.setOnClickListener(this::onClickListenerFinalizeDescanso);

            //Fin Turno
            btnFinTurno.setOnClickListener(v -> onClickListenerFinalizeTurno());

            //Inicio Turno
            btnInicioTurno.setOnClickListener(v -> {
                try {
                    Intent i = new Intent(PersonalActivity.getInstance(), PersonalIdentificacionActivity.class);
                    startActivityForResult(i, CODE_IDENTIFICACION);
                } catch (Throwable e) {
                    logger.WriteError(e);
                }
            });

            //Fin Turno Todos
            //        btnFinTurnoTodos.setOnClickListener(new OnClickListenerFinalizeTurnoTodos());

            //Evento al pulsar sobre un trabajador de la lista de trabajadores identificados
            listViewTrabajadoresIdentificados.setOnItemClickListener((pariente, view, posicion, id) -> {
                try {
                    TrabajadoresModel trabajadorElegido = (TrabajadoresModel) listViewTrabajadoresIdentificados.getItemAtPosition(posicion);

                    //Comprobamos que no existe ya en la lista y lo insertamos
                    if (!trabajadoresElegidos.contains(trabajadorElegido)) {
                        addTrabajadorElegido(view, trabajadorElegido);
                    } else //Borramos el trabajador de la lista y actualizamos botonera si es necesario
                    {
                        deleteTrabajadorElegido(view, trabajadorElegido);
                    }
                } catch (Throwable e) {
                    logger.WriteError(e);
                }
            });
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Funcion auxiliar que busca en el array de trabajadores elegidos
     * si existe un trabajador con estado igual al pasado como parametro
     *
     * @param estado Estado del trabajador a buscar
     * @return true si existe un trabajador con el estado pasado como parametro
     */

    private boolean findTrabajadorEstado(int estado) {
        try {
            for (TrabajadoresModel trabajador : trabajadoresElegidos) {
                if (trabajador.Estado == estado) {
                    return true;
                }
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return false;
    }

    /**
     * Construye el array de trabajadores descansando a partir del array de trabajadores elegidos,
     * así evitamos mandar trabajadores que no estén descansando aunque estén elegidos en la lista
     */
    @NonNull
    private ArrayList<TrabajadoresModel> getTrabajadoresDescansando() {

        //Construimos array de trabajadores descansando para mandar al API SERVICE (solamente los que tengan este estado)
        ArrayList<TrabajadoresModel> trabajadoresDescansando = new ArrayList<>();
        try {
            for (TrabajadoresModel trabajador : trabajadoresElegidos) {
                if (trabajador.Estado == TrabajadoresManager.ESTADO_DESCANSO) {
                    trabajadoresDescansando.add(trabajador);
                }
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return trabajadoresDescansando;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        try {
            super.onActivityResult(requestCode, resultCode, data);

            if (requestCode == ACTIVITY_REVISIONES) {
                finalizeTurno();
                refreshListViewTrabajadoresIdentificados();
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Construye el listview a partir de los trabajadores identificados en el sistema
     */
    private void fillListaTrabajadores() {
        try {
            TrabajadoresDAO trabajadoresDAO = new TrabajadoresDAO();
            trabajadoresIdentificados = (ArrayList<TrabajadoresModel>) trabajadoresDAO.getTrabajadoresIdentificados(app.getCodigoMovil());

            if (trabajadoresIdentificados != null) {
                // Añadir todos los trabajadores a trabajadoresElegidos si seleccionarTodosTrabajadores es true
                if (seleccionarTodosTrabajadores) {
                    for (TrabajadoresModel trabajador : trabajadoresIdentificados) {
                        if (!trabajadoresElegidos.contains(trabajador)) {
                            trabajadoresElegidos.add(trabajador);
                            // Aquí no puedes cambiar el color de fondo porque las vistas podrían no estar creadas
                        }
                    }
                    btnFinTurno.setEnabled(true);
                    seleccionarTodosTrabajadores = false;
                }
                if (listAdapter == null) {
                    listAdapter = new ListAdapterTrabajadoresIdentificados(this, R.layout.entrada_personal, trabajadoresIdentificados, trabajadoresElegidos);
                    listViewTrabajadoresIdentificados.setAdapter(listAdapter);
                } else {
                    listAdapter.updateList(trabajadoresIdentificados);
                }
            }
            if ((trabajadoresIdentificados == null || trabajadoresIdentificados.isEmpty()) && TrabajadoresManager.getInstance().hasConductorIdentificado()) {
                ConfiguracionManager.save(CONF_ID_CONDUCTOR, "");
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Actualiza el listView de trabajadores identificados en las acciones
     * (Insertar turno, finalizar Turno, Iniciar descanso, finalizar descanso)
     */
    public void refreshListViewTrabajadoresIdentificados() {
        try {
            //Se borran los trabajadores elegidos

            //Actualizamos la imagen en el ListView
            trabajadoresIdentificados = (ArrayList<TrabajadoresModel>) trabajadoresDAO.getTrabajadoresIdentificados(app.getCodigoMovil());
            listAdapter.addAll(trabajadoresIdentificados);

            if ((trabajadoresIdentificados == null || (trabajadoresIdentificados != null && trabajadoresIdentificados.isEmpty())) && TrabajadoresManager.getInstance().hasConductorIdentificado()) {
                ConfiguracionManager.save(CONF_ID_CONDUCTOR, "");
            }

            trabajadoresElegidos.clear();

            disableButtons();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Deshabilita los botones descanso, fin turno y fin descanso
     */
    private void disableButtons() {
        try {
            //Se deshabilitan los botones
            btnDescanso.setEnabled(false);
            btnFinTurno.setEnabled(false);
            btnFinDescanso.setEnabled(false);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Eliminamos trabajador elegido de la lista de trabajadores y actualizamos botonera
     *
     * @param view              Vista del trabajador
     * @param trabajadorElegido Trabajador elegido
     */
    private void deleteTrabajadorElegido(View view, TrabajadoresModel trabajadorElegido) {
        try {
            trabajadoresElegidos.remove(trabajadorElegido);
            if (trabajadorElegido.Estado == 1) {
                view.setBackgroundColor(getResources().getColor(R.color.background_material_light));
            } else {
                view.setBackgroundColor(Color.parseColor("#e9aa29"));
            }

            //Si la lista esta vacia deshabilitamos todos los botones
            if (trabajadoresElegidos.isEmpty()) {
                disableButtons();
            }

            //Si la lista no esta vacia comprobamos que no exista otro trabajador con estado = ESTADO_DESCANSO
            else if (!findTrabajadorEstado(TrabajadoresManager.ESTADO_DESCANSO)) {
                btnFinDescanso.setEnabled(false);
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Añadimos trabajador elegido de la lista de trabajadores y actualizamos botonera
     *
     * @param view              Vista del trabajador
     * @param trabajadorElegido Trabajador elegido
     */
    private void addTrabajadorElegido(View view, TrabajadoresModel trabajadorElegido) {
        try {
            trabajadoresElegidos.add(trabajadorElegido);

            view.setBackgroundColor(getResources().getColor(R.color.verde));

            //Actualizamos boton fin descanso si es necesario
            if (trabajadorElegido.Estado == TrabajadoresManager.ESTADO_DESCANSO) {
                btnFinDescanso.setEnabled(true);
            }

            btnDescanso.setEnabled(true);
            btnFinTurno.setEnabled(true);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Clase que implementa el listener del boton Fin Descanso
     */
    private void onClickListenerFinalizeDescanso(View v) {
        try {
            final ArrayList<TrabajadoresModel> trabajadoresDescansando = getTrabajadoresDescansando();
            String sNombresTrabajadoresDescansando = getNombreTrabajadoresForDialog(trabajadoresDescansando);

            final SweetAlertDialog dlg = new SweetAlertDialog(PersonalActivity.this, SweetAlertDialog.WARNING_TYPE)
                    .setTitleText(getString(R.string.atencion))
                    .setContentText(getString(R.string.personal_fin_descanso, sNombresTrabajadoresDescansando))
                    .setConfirmText(getString(R.string.si))
                    .setCancelText(getString(R.string.no));

            dlg.setConfirmClickListener(sweetAlertDialog -> {
                dlg.dismiss();
                finalizeDescanso(trabajadoresDescansando);
            }).show();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Finaliza descanso de uno o mas trabajadores elegidos
     *
     * @param trabajadoresDescansando Lista de trabajadores descansando
     */

    private void finalizeDescanso(ArrayList<TrabajadoresModel> trabajadoresDescansando) {
        try {
            if ((trabajadoresDescansando != null) && (!trabajadoresDescansando.isEmpty())) {
                ArrayList<DescansosHViewModel> descansosH = getDescansosHistorico(trabajadoresDescansando);
                //Guardamos los estados de los trabajadores
                trabajadoresDAO.save(trabajadoresDescansando);
                refreshListViewTrabajadoresIdentificados();

                jobManager = RutasApplication.getInstance().getJobManager();
                jobManager.addJobInBackground(new InsertarDescansosHJob(descansosH));

            } else {
                ToastMensaje toast = new ToastMensaje(getInstance());
                toast.show(getString(R.string.personal_error_seleccion_trabajador), R.mipmap.trabajar);
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @NonNull
    private ArrayList<DescansosHViewModel> getDescansosHistorico(ArrayList<TrabajadoresModel> trabajadoresDescansando) {
        //Construimos array de Descansos y lo actualizamos en el API Service
        ArrayList<DescansosHViewModel> descansosH = new ArrayList<>();

        try {
            for (TrabajadoresModel trabajador : trabajadoresDescansando) {
                DescansosHViewModel descanso = new DescansosHViewModel();
                descanso.Descanso = trabajador.Descanso;
                descanso.Estado = TrabajadoresManager.FIN_DESCANSO;
                descanso.Empresa = trabajador.IdEmpresa;
                descanso.Empleado = trabajador.Codigo;
                descanso.Fecha = Utils.StringToDateTime(Utils.nowDateTimeToString(getString(R.string.mysql_date)));
                descanso.Movil = app.getCodigoMovil();

                descansosH.add(descanso);
                trabajador.Estado = TrabajadoresManager.ESTADO_INICIO_TURNO;

                logger.WriteInfo("Finalizado descanso para trabajador con fecha " + descanso.Fecha + ", codigo: " + trabajador.Codigo + " y Nombre: " + trabajador.Nombre);
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return descansosH;
    }

    /**
     * Devuelve un texto usado para dialogs, de los nombre de los trabajadores separados por comas.
     *
     * @param trabajadores Lista de trabajadores
     * @return String con los nombres de los trabajadores separados por comas
     */
    @NonNull
    private String getNombreTrabajadoresForDialog(ArrayList<TrabajadoresModel> trabajadores) {
        StringBuilder sNombresTrabajadoresElegidos = new StringBuilder();
        try {
            for (TrabajadoresModel trabajador : trabajadores) {
                if (sNombresTrabajadoresElegidos.length() > 0) {
                    sNombresTrabajadoresElegidos.append(", ");
                }
                sNombresTrabajadoresElegidos.append(trabajador.Nombre);
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return sNombresTrabajadoresElegidos.toString();
    }

    /**
     * Clase que implementa el listener del boton Fin Turno
     */
    private void onClickListenerFinalizeTurno() {
        try {
            ArrayList<TrabajadoresModel> trabajadoresDescansando = getTrabajadoresDescansando();
            if (trabajadoresDescansando.isEmpty()) {

                String sNombresTrabajadoresElegidos = getNombreTrabajadoresForDialog(trabajadoresElegidos);

                TrabajadoresDAO trabajadoresDAO = new TrabajadoresDAO();
                trabajadoresIdentificados = (ArrayList<TrabajadoresModel>)
                        trabajadoresDAO.getTrabajadoresIdentificados(app.getCodigoMovil());

                // Si se ha accedido desde el menú principal y sólo queda un trabajador
                // identificado no le dejamos hasta que finalize actividad
                if (activityFueraRuta && trabajadoresIdentificados.size() -
                        trabajadoresElegidos.size() < 1) {

                    if (RutasApplication.getInstance().isRutaIniciada()) {
                        new ToastMensaje(instance).show(getString(R.string.personal_hay_actividad_iniciada), R.mipmap.play);
                        return;
                    }

                } else if (RutasApplication.getInstance().isRutaIniciada() && conductorSeleccionado()) {
                    new ToastMensaje(instance).show(getString(R.string.personal_conductor_con_actividad_iniciada), R.mipmap.play);
                    return;
                }

                final SweetAlertDialog dlg = new SweetAlertDialog(PersonalActivity.this, SweetAlertDialog.WARNING_TYPE)
                        .setTitleText(getString(R.string.atencion))
                        .setContentText(getString(R.string.personal_fin_turno, sNombresTrabajadoresElegidos))
                        .setConfirmText(getString(R.string.si))
                        .setCancelText(getString(R.string.no));

                dlg.setConfirmClickListener(sweetAlertDialog -> {
                    try {
                        dlg.dismiss();
                        finTurno = new Date();
                        ConfiguracionModel conf = ConfiguracionManager.getConfBy(CLAVE_JORNADA);
                        if (conf != null && conf.valor != null && conf.valor.equals("1")) {
                            // Si hay más de un trabajador elegido, mostramos un dialogo de confirmación para el fin de jornada
                            if (trabajadoresElegidos.size() > 1) {
                                final SweetAlertDialog dlg1 = new SweetAlertDialog(PersonalActivity.this, SweetAlertDialog.WARNING_TYPE)
                                        .setTitleText(getString(R.string.atencion))
                                        .setContentText(getString(R.string.personal_fin_jornada))
                                        .setConfirmText(getString(R.string.si))
                                        .setCancelText(getString(R.string.no));
                                dlg1.setConfirmClickListener(sweetAlertDialog1 -> {
                                    dlg1.dismiss();
                                    new DateTimePicker(sweetAlertDialog1.getContext(), dateTime -> {
                                        // TODO: Pasar a un método
                                        ArrayList<JornadasViewModel> jornadas = new ArrayList<>();
                                        for (TrabajadoresModel trabajador : trabajadoresElegidos) {
                                            JornadasViewModel jornada = new JornadasViewModel();
                                            jornada.IdEmpresa = trabajador.IdEmpresa;
                                            jornada.IdUsuario = trabajador.Codigo;
                                            jornada.FechaEntrada = trabajador.FechaInicioJornada;
                                            jornada.FechaSalida = dateTime;
                                            jornada.FTurnoInicio = Utils.datetimeToString(trabajador.FechaInicioTurno, "yyyy-MM-dd HH:mm:ss");
                                            jornadas.add(jornada);
                                        }
                                        jobManager = RutasApplication.getInstance().getJobManager();
                                        jobManager.addJobInBackground(new InsertarJornadasHJob(jornadas));

                                        //Obtenemos el codigo del trabjador identificado como conductor.
                                        ConfiguracionModel conf1 = ConfiguracionManager.getConfBy(CONF_ID_CONDUCTOR);
                                        int codigoConductor = 0;
                                        if (conf1 != null && !conf1.valor.isEmpty()) {
                                            codigoConductor = Integer.parseInt(conf1.valor);
                                        }

                                        if (codigoConductor > 0 && trabajadoresElegidos != null) {
                                            for (TrabajadoresModel trabajador : trabajadoresElegidos) {
                                                if (trabajador.Codigo == codigoConductor) {
                                                    // Cuando se desidentifica el conductor se lanzan las revisiones y
                                                    // esperamos a que terminen para crear las desidentificaciones
                                                    startActivityRevisiones(PersonalActivity.this, FIN_TURNO, codigoConductor, finTurno);
                                                    return;
                                                }
                                            }
                                        }
                                        // Si no se trata del conductor finalizamos turno ya mismo
                                        finalizeTurno();
                                    }, "Fin de jornada", 1, finTurno).show();
                                }).show();
                            } else {
                                new DateTimePicker(sweetAlertDialog.getContext(), dateTime -> {
                                    JornadasViewModel jornada = new JornadasViewModel();
                                    jornada.IdEmpresa = trabajadoresElegidos.get(0).IdEmpresa;
                                    jornada.IdUsuario = trabajadoresElegidos.get(0).Codigo;
                                    jornada.FechaEntrada = trabajadoresElegidos.get(0).FechaInicioJornada;
                                    jornada.FechaSalida = dateTime;
                                    jornada.FTurnoInicio = Utils.datetimeToString(trabajadoresElegidos.get(0).FechaInicioTurno, "yyyy-MM-dd HH:mm:ss");

                                    ArrayList<JornadasViewModel> jornadas = new ArrayList<>();
                                    jornadas.add(jornada);

                                    jobManager = RutasApplication.getInstance().getJobManager();
                                    jobManager.addJobInBackground(new InsertarJornadasHJob(jornadas));

                                    //Obtenemos el codigo del trabjador identificado como conductor.
                                    ConfiguracionModel conf2 = ConfiguracionManager.getConfBy(CONF_ID_CONDUCTOR);
                                    int codigoConductor = 0;
                                    if (conf2 != null && !conf2.valor.isEmpty()) {
                                        codigoConductor = Integer.parseInt(conf2.valor);
                                    }

                                    if (codigoConductor > 0 && trabajadoresElegidos != null) {
                                        for (TrabajadoresModel trabajador : trabajadoresElegidos) {
                                            if (trabajador.Codigo == codigoConductor) {
                                                // Cuando se desidentifica el conductor se lanzan las revisiones y
                                                // esperamos a que terminen para crear las desidentificaciones
                                                startActivityRevisiones(PersonalActivity.this, FIN_TURNO, codigoConductor, finTurno);
                                                return;
                                            }
                                        }
                                    }
                                    // Si no se trata del conductor finalizamos turno ya mismo
                                    finalizeTurno();
                                }, "Fin de jornada", 1, finTurno).show();
                            }
                        } else {
                            //Obtenemos el codigo del trabjador identificado como conductor.
                            conf = ConfiguracionManager.getConfBy(CONF_ID_CONDUCTOR);
                            int codigoConductor = 0;
                            if (conf != null && !conf.valor.isEmpty()) {
                                codigoConductor = Integer.parseInt(conf.valor);
                            }

                            if (codigoConductor > 0 && trabajadoresElegidos != null) {
                                for (TrabajadoresModel trabajador : trabajadoresElegidos) {
                                    if (trabajador.Codigo == codigoConductor) {
                                        // Cuando se desidentifica el conductor se lanzan las revisiones y
                                        // esperamos a que terminen para crear las desidentificaciones
                                        startActivityRevisiones(PersonalActivity.this, FIN_TURNO, codigoConductor, finTurno);
                                        return;
                                    }
                                }
                            }
                            // Si no se trata del conductor finalizamos turno ya mismo
                            finalizeTurno();
                        }
                    } catch (Throwable e) {
                        logger.WriteError(e);
                    }
                }).show();
            } else {
                new ToastMensaje(instance).show(getString(R.string.personal_trabajadores_descansando), R.mipmap.descanso);
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }


    /**
     * Finaliza turno de uno o mas trabajadores elegidos
     */
    private void finalizeTurno() {
        try {
            if (trabajadoresElegidos != null && !trabajadoresElegidos.isEmpty()) {
                ArrayList<TurnosViewModel> turnosH = getTurnosHistorico();

                //Guardamos los trabajadores identificados
                trabajadoresDAO.save(trabajadoresElegidos);
                refreshListViewTrabajadoresIdentificados();

                jobManager = RutasApplication.getInstance().getJobManager();
                jobManager.addJobInBackground(new InsertarTurnosHJob(turnosH));

            } else {
                ToastMensaje toast = new ToastMensaje(getInstance());
                toast.show(getString(R.string.personal_error_seleccion_trabajador), R.mipmap.trabajar);
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    private boolean conductorSeleccionado() {
        ConfiguracionModel conf = ConfiguracionManager.getConfBy(CONF_ID_CONDUCTOR);
        for (TrabajadoresModel trabajador : trabajadoresElegidos) {
            if (conf != null && trabajador.Codigo == Integer.parseInt(conf.valor)) return true;
        }
        return false;
    }


    /**
     * Parsea a partir de los trabajadores elegidos en historico de turnos,
     * para prepararlo antes de enviarselo al servidor.
     *
     * @return ArrayList de TurnosViewModel
     */
    @NonNull
    private ArrayList<TurnosViewModel> getTurnosHistorico() {
        int idConductor = 0;
        if (trabajadoresElegidos == null)
            return new ArrayList<>();

        //Construimos array de Descansos y lo actualizamos en el API Service
        ArrayList<TurnosViewModel> turnosH = new ArrayList<>();
        try {

            // Recupero de la configuración el conductor actual
            ConfiguracionModel conf = ConfiguracionManager.getConfBy(CONF_ID_CONDUCTOR);
            if (conf != null && conf.valor != null && !conf.valor.isEmpty()) {
                idConductor = Integer.parseInt(conf.valor);
            }


            //Actualizamos estado en el API Service
            for (TrabajadoresModel trabajador : trabajadoresElegidos) {
                TurnosViewModel turno = new TurnosViewModel();
                turno.Estado = TrabajadoresManager.FIN_TURNO;
                //turno.Tipo = trabajador.CodigoCategoria;
                turno.Tipo = trabajador.Codigo == idConductor ? IDENTIFICACION_CONDUCTOR : IDENTIFICACION_EMPLEADO;
                turno.Empleado = trabajador.Codigo;
                turno.Movil = app.getCodigoMovil();
                if (finTurno != null) {
                    turno.Fecha = Utils.datetimeToString(finTurno, getString(R.string.mysql_date));
                } else {
                    turno.Fecha = Utils.nowDateTimeToString(getString(R.string.mysql_date));
                }

                turnosH.add(turno);
                trabajador.Estado = TrabajadoresManager.ESTADO_FIN_TURNO;

                if (idConductor > 0 && trabajador.Codigo == idConductor) {
                    // Borro el conductor actual de la configuración
                    ConfiguracionManager.save(CONF_ID_CONDUCTOR, "");
                }

                logger.WriteInfo("Finalizado turno para trabajador con fecha " + turno.Fecha + ", codigo: " + trabajador.Codigo + " y Nombre: " + trabajador.Nombre);
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return turnosH;
    }

    public void onEventMainThread(OnNfcReaded event) {
        try {
            if (event != null) {
                Tag tag = event.tag;
                String tagId = Utils.arrayToHex(tag.getId(), 0, tag.getId().length);

                TrabajadoresModel trabajador = trabajadoresDAO.getTrabajadorByNfc(tagId);

                if (trabajador == null || trabajador.Estado != TrabajadoresManager.ESTADO_INICIO_TURNO) {
                    return;
                }
                trabajadoresElegidos.clear();
                trabajadoresElegidos.add(trabajador);

                btnDescanso.setEnabled(true);
                btnFinTurno.setEnabled(true);
                onClickListenerFinalizeTurno();
                listAdapter.notifyDataSetChanged();
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

}
