package com.movisat.activities.splashScreen;

import android.Manifest;
import android.content.ContentValues;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.PixelFormat;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Debug;
import android.os.Handler;
import android.os.Message;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import android.provider.Settings;
import android.text.InputType;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.view.Menu;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.movisat.activities.BaseActivity;
import com.movisat.activities.BuildConfig;
import com.movisat.activities.MainActivity;
import com.movisat.activities.R;
import com.movisat.application.RutasApplication;
import com.movisat.managers.DracoManager;
import com.movisat.managers.StatusManager;
import com.movisat.managers.log.LoggerManager;
import com.movisat.utilities.AdminUtils;
import com.movisat.utilities.Utils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Pantalla de inicio de la aplicación con logo de empresa, duración de 3 seg. 16/11/2015.
 */
public class SplashScreenActivity extends AppCompatActivity {

    private void DEBUG() {
        /*
         * DEBUG - INIT - Comentar el código antes de compilar para que no se incluya en el código del apk.
         *                Aunque no se comente, las variables no se asignarán si no se está en modo debug.
         */
        //         EnvironmentDebug.enableDebugMode(
        //                 new EnvironmentDebugData(
        //                         null,
        //                         null,
        //                         null,
        //                         null,
        //                         null,
        //                         "86641905545246500",
        //                         null,
        //                         false,
        //                         100,
        //                         3
        //                 )
        //         );

        // final Handler handler = new Handler();
        // final int delay = 1000 * 60; // 1000 milliseconds == 1 second

        // handler.postDelayed(new Runnable() {
        //     public void run() {
        //         EventBus.getDefault().post(
        //                 new OnNotifyDinEvent(
        //                         new DinViewModel(
        //                                 1,
        //                                 Utilss.now(),
        //                                 true,
        //                                 new LatLng(38.00296946696541, -1.1449583210353484)
        //                         )
        //                 )
        //         );
        //         handler.postDelayed(this, delay);
        //     }
        // }, delay);

        // Para que espere al attach del debugger
        // Debug.waitForDebugger();

        /*
         * DEBUG - FIN.
         */
    }


    private static SplashScreenActivity instance = null;

    private static final boolean APP_FUNCTIONAL = true;

    private static final int REQUEST_CODE_PERMISSIONS = 1;
    private static final int REQUEST_CODE_OVERLAY = 101;
    private static final int REQUEST_CODE_WRITE_SETTINGS = 102;

    private static final String[] REQUIRED_PERMISSIONS = new String[]{
          Manifest.permission.ACCESS_FINE_LOCATION,
          Manifest.permission.ACCESS_COARSE_LOCATION,
          Manifest.permission.CALL_PHONE,
          Manifest.permission.CAMERA,
          Manifest.permission.READ_PHONE_STATE,
    };
    
    // Solo para soportar Bluetooth en Android 12+
    private static String[] getBluetoothPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            return new String[]{
                Manifest.permission.BLUETOOTH_CONNECT
            };
        }
        return new String[]{};
    }
    
    // Combina los permisos requeridos con los de Bluetooth
    private static String[] getAllRequiredPermissions() {
        String[] bluetoothPermissions = getBluetoothPermissions();
        String[] allPermissions = new String[REQUIRED_PERMISSIONS.length + bluetoothPermissions.length];

        System.arraycopy(REQUIRED_PERMISSIONS, 0, allPermissions, 0, REQUIRED_PERMISSIONS.length);
        System.arraycopy(bluetoothPermissions, 0, allPermissions, REQUIRED_PERMISSIONS.length, bluetoothPermissions.length);
        return allPermissions;
    }

    private final Set<Integer> requestedSpecialPermissions = new HashSet<>();

    TextView versionSoft;
    private Menu mMenu = null;

    // Permite lanzar la aplicación cuando arranca Android (se llama desde EcosatBroadcast)
    public static void initApp(Context context) {

        try {

            Intent i = new Intent(context, SplashScreenActivity.class);
            i.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_FROM_BACKGROUND);
            context.startActivity(i);

        } catch (Throwable e) {

            LoggerManager.getInstance().WriteError(e);
        }
    }

    public void onAttachedToWindow() {
        try {

            super.onAttachedToWindow();
            Window window = getWindow();
            window.setFormat(PixelFormat.RGBA_8888);

        } catch (Throwable e) {

            LoggerManager.getInstance().WriteError(e);
        }
    }

    public static SplashScreenActivity getInstance() {
        return instance;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        try {
            DEBUG();

            // Indico que ya a arrancado la aplicación para que el núcleo compruebe
            // cada x tiempo si está en marcha o no
            DracoManager.setConfigBy("appStoppedByUser", "0");

            if (APP_FUNCTIONAL)
                setTheme(R.style.AppTheme_NoActionBar);

            super.onCreate(savedInstanceState);

            instance = this;

            if (APP_FUNCTIONAL) {
                setContentView(R.layout.splash);
                versionSoft = (TextView) findViewById(R.id.versionSoft);
                versionSoft.setText("VERSIÓN: " + Utils.getVersionName(this));
            } else {
                setContentView(R.layout.splash_static);
            }

            checkPermissions();
        } catch (Throwable e) {

            LoggerManager.getInstance().WriteError(e);
        }
    }

    private void checkPermissions() {
        if (!allPermissionsGranted() || !areSpecialPermissionsGranted())
            autoRequestPermissions();

        if (!allPermissionsGranted()) {
            ActivityCompat.requestPermissions(this, getAllRequiredPermissions(), REQUEST_CODE_PERMISSIONS);
        } else if (!areSpecialPermissionsGranted()) {
            requestSpecialPermissions();
        } else {
            proceedAfterPermissionsGranted();
        }
    }

    public static void autoRequestPermissions() {
        for (String permission : getAllRequiredPermissions()) {
            AdminUtils.grantPermission(permission);
        }

        AdminUtils.grantPermission("SYSTEM_ALERT_WINDOW");
        AdminUtils.grantPermission("WRITE_SETTINGS");
    }

    private boolean allPermissionsGranted() {
        for (String permission : getAllRequiredPermissions()) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }

    private boolean areSpecialPermissionsGranted() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return Settings.canDrawOverlays(this) && Settings.System.canWrite(this);
        }
        return true;
    }

    private void requestSpecialPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(this)) {
                Intent overlayIntent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                      Uri.parse("package:" + getPackageName()));
                startActivityForResult(overlayIntent, REQUEST_CODE_OVERLAY);
            } else if (!Settings.System.canWrite(this)) {
                Intent writeSettingsIntent = new Intent(Settings.ACTION_MANAGE_WRITE_SETTINGS,
                      Uri.parse("package:" + getPackageName()));
                startActivityForResult(writeSettingsIntent, REQUEST_CODE_WRITE_SETTINGS);
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == REQUEST_CODE_PERMISSIONS) {
            if (allPermissionsGranted()) {
                if (!areSpecialPermissionsGranted()) {
                    requestSpecialPermissions();
                } else {
                    proceedAfterPermissionsGranted();
                }
            } else {
                showPermissionsDeniedDialog();
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == REQUEST_CODE_OVERLAY || requestCode == REQUEST_CODE_WRITE_SETTINGS) {
            requestedSpecialPermissions.add(requestCode);

            boolean allRequested = requestedSpecialPermissions.contains(REQUEST_CODE_OVERLAY) &&
                  requestedSpecialPermissions.contains(REQUEST_CODE_WRITE_SETTINGS);

            if (areSpecialPermissionsGranted()) {
                proceedAfterPermissionsGranted();
            } else if (allRequested) {
                showPermissionsDeniedDialog();
            } else {
                requestSpecialPermissions();
            }
        }
    }

    private void proceedAfterPermissionsGranted() {
        onCreateDefault();
    }

    private void showPermissionsDeniedDialog() {
        new AlertDialog.Builder(this)
              .setTitle("Permisos requeridos")
              .setMessage("Algunos permisos son necesarios para que la aplicación funcione correctamente. Por favor, habilítalos en la configuración.")
              .setPositiveButton("Intentar de nuevo", (dialog, which) -> checkPermissions())
              .setNegativeButton("Salir", (dialog, which) -> finish())
              .show();
    }

    private void onCreateDefault() {
//        WindowManager window = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
//        Display display = window.getDefaultDisplay();
//        int width = display.getWidth();
//        int height = display.getHeight();
//        DisplayMetrics metrics = getResources().getDisplayMetrics();

        // Se establece la aplicación como device-owner, se da permiso de Lock task mode
        // a las aplicaciones y se entra en modo kiosko
        if (!BuildConfig.DEBUG) {
            AdminUtils.setDeviceOwner(this);
            AdminUtils.setActiveAdmin(this);
            String[] packages = {getPackageName(), "com.movisat.draco", "com.movisat.updateapp"};
            AdminUtils.setLockTaskPackages(this, packages);
            AdminUtils.setKioskModeEnabled(this, true);
            // AdminUtils.removeActiveAdmin(this);
            AdminUtils.removeDeviceOwner(this);
        } else {
            AdminUtils.setKioskModeEnabled(this, false);
            AdminUtils.removeActiveAdmin(this);
            AdminUtils.removeDeviceOwner(this);
            AdminUtils.setLockTaskPackages(this, null);
        }

        if (APP_FUNCTIONAL) {

            new Thread() {

                @Override
                public void run() {
                    try {
                        startAnimacion();
                        sleep(1000);

                    } catch (InterruptedException e) {

                        LoggerManager.getInstance().WriteError(e);

                    } finally {

                        handler.sendMessage(new Message());
                    }
                }
            }.start();
        }
    }

    private void startAnimacion() {

        try {

            Animation animacion = AnimationUtils.loadAnimation(this, R.anim.alpha);
            animacion.reset();
            Animation animacionEcoRutas = AnimationUtils.loadAnimation(this, R.anim.success_mask_layout);
            animacionEcoRutas.reset();
            FrameLayout layout = (FrameLayout) findViewById(R.id.frameLayout_splash);
            layout.clearAnimation();
            layout.startAnimation(animacion);
            animacion = AnimationUtils.loadAnimation(this, R.anim.translate);
            animacion.reset();
            ImageView logo = (ImageView) findViewById(R.id.logo);
            logo.clearAnimation();
            logo.startAnimation(animacion);
            TextView ecoRutas = (TextView) findViewById(R.id.ecoRutas);
            ecoRutas.startAnimation(animacion);
            versionSoft.startAnimation(animacionEcoRutas);

        } catch (Throwable e) {

            LoggerManager.getInstance().WriteError(e);
        }
    }

    private static final Handler handler = new Handler() {
        @Override
        public void handleMessage(Message msg) {

            try {

                //super.sendMessage(msg);
                Intent abrirMainActivity = new Intent(SplashScreenActivity.getInstance(), MainActivity.class);
                instance.startActivity(abrirMainActivity);
                instance.finish();

            } catch (Throwable e) {

                LoggerManager.getInstance().WriteError(e);
            }
        }
    };

    @Override
    public void onBackPressed() {
        try {
            if (!APP_FUNCTIONAL)
                DialogoSalir();
        } catch (Throwable e) {
        }
    }

    public void DialogoSalir() {

        try {
            // Se crea un cuadro de diálogo para la introducción de texto. Si el texto
            // introducido coincide con la contraseña de salida, se puede salir de la aplicación.
            AlertDialog.Builder builder = new AlertDialog.Builder(this);
            builder.setTitle("Introduzca el código de salida");

            // Se crea el EditText para introducir texto y se incluye en el diálogo
            final EditText input = new EditText(this);
            input.setInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_VARIATION_PASSWORD);
            builder.setView(input);

            // Set up the buttons
            builder.setPositiveButton("OK", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    String texto;
                    texto = input.getText().toString();
                    if (texto.equals(BaseActivity.EXIT_PASSWORD)) {
                        // Se sale del modo kiosko y de la aplicación
                        AdminUtils.setKioskModeEnabled(SplashScreenActivity.getInstance(), false);

                        // Aviso que el usuario a salido de la aplicación usando la contraseña
                        // para que el Watchdog no la vuelva a arrancar
                        DracoManager.setConfigBy("appStoppedByUser", "1");

                        finish();
                    }
                }
            });
            builder.setNegativeButton("Cancelar", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.cancel();
                }
            });

            builder.show();

        } catch (Throwable e) {

            e.printStackTrace();
        }
    }


    @Override
    protected void onResume() {
        try {
            super.onResume();

            if (!APP_FUNCTIONAL) {
                // Se establece el menú en el StatusManager para mostrar los iconos de estado
                if (mMenu != null) {
                    StatusManager statusManager = StatusManager.getInstance();
                    statusManager.setMenu(mMenu);
                    statusManager.loadIcons();  // Para mostrar los iconos al instante
                }
            }

        } catch (Throwable e) {
            LoggerManager.getInstance().WriteError(e);
        }
    }


    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        try {
            if (!APP_FUNCTIONAL) {
                getMenuInflater().inflate(R.menu.menu_status, menu);
            }

        } catch (Throwable e) {
            LoggerManager.getInstance().WriteError(e);
        }
        return true;
    }

    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {
        try {
            if (!APP_FUNCTIONAL) {
                mMenu = menu;

                // Se establece el menú en el StatusManager para mostrar los iconos de estado
                StatusManager statusManager = StatusManager.getInstance();
                statusManager.setMenu(menu);
                statusManager.loadIcons();  // Para mostrar los iconos al instante
                if (statusManager.getStatus() != AsyncTask.Status.RUNNING)
                    statusManager.execute();
            }

        } catch (Throwable e) {
            LoggerManager.getInstance().WriteError(e);
        }

        return super.onPrepareOptionsMenu(menu);
    }
}