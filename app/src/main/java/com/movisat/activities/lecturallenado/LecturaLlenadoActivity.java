package com.movisat.activities.lecturallenado;


import android.os.Bundle;
import android.view.Menu;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.jobqueue.JobManager;
import com.movisat.activities.BaseActivity;
import com.movisat.activities.R;
import com.movisat.activities.rutas.ElementoProcesarBaseActivity;
import com.movisat.activities.rutas.ElementoProcesarNivelLlenadoActivity;
import com.movisat.dao.elementos.ElementosDAOProxy;
import com.movisat.dao.elementos.LecturasLlenadoDAO;
import com.movisat.events.map.OnRefrescarElementosMapa;
import com.movisat.jobs.lecturallenado.InsertarLecturaLlenadoJob;
import com.movisat.log.Logg;
import com.movisat.models.elementos.ElementosModel;
import com.movisat.models.elementos.LecturasLlenadoModel;
import com.movisat.utilities.ToastMensaje;
import com.movisat.viewmodels.lecturallenado.LecturaLlenadoViewModel;

import java.util.Date;

import de.greenrobot.event.EventBus;

public class LecturaLlenadoActivity extends BaseActivity {
    static final String TAG = "LecturaLlenadoActivity";

    //Job Manager
    private JobManager jobManager;

    private Button btnContVacio;
    private Button btnContLlenado25;
    private Button btnContLlenado50;
    private Button btnContLlenado75;
    private Button btnContLlenado100;
    private Button btnContDesbordado;
    private TextView nombreElemento;

    private static final int ID_PORCENTAJE_VACIO = 1;
    private static final int ID_PORCENTAJE_25 = 2;
    private static final int ID_PORCENTAJE_50 = 3;
    private static final int ID_PORCENTAJE_75 = 4;
    private static final int ID_PORCENTAJE_100 = 5;
    private static final int ID_PORCENTAJE_DESBORDADO = 6;

    private static final int VALOR_PORCENTAJE_VACIO = 0;
    private static final int VALOR_PORCENTAJE_25 = 25;
    private static final int VALOR_PORCENTAJE_50 = 50;
    private static final int VALOR_PORCENTAJE_75 = 75;
    private static final int VALOR_PORCENTAJE_100 = 100;
    private static final int VALOR_PORCENTAJE_DESBORDADO = 101;

    public static final String ID_ELEMENTO = "idElemento";
    public static final String NIVEL_DE_LLENADO = "NivelDeLLenado";
    public static final String PORCENTAJE_LLENADO = "PorcentajeDeLLenado";
    private ElementosModel elementoSeleccionado = null;
    private boolean nivelDeLlenado = false;

    private boolean procesadoPorTAG = false;
    private long fechaProcesar = 0;

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        try {
            getMenuInflater().inflate(R.menu.menu_main, menu);
            return true;
        } catch (Throwable e) {
            Logg.error(TAG, "[onCreateOptionsMenu] " + e.getMessage());
            return false;
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
        } catch (Throwable e) {
            Logg.error(TAG, "[onCreate] " + e.getMessage());
        }
    }

    protected void setContentView() {
        setContentView(R.layout.activity_llenado);
    }

    protected void init() {
        try {
            // super.init();

            btnContVacio = (Button) findViewById(R.id.btnContVacio);
            btnContLlenado25 = (Button) findViewById(R.id.btn25);
            btnContLlenado50 = (Button) findViewById(R.id.btn50);
            btnContLlenado75 = (Button) findViewById(R.id.btn75);
            btnContLlenado100 = (Button) findViewById(R.id.btn100);
            btnContDesbordado = (Button) findViewById(R.id.btndesbordado);
            nombreElemento = (TextView) findViewById(R.id.nombreElemento);

            Bundle bundle = getIntent().getExtras();
            // Compruebo si la llamada viene desde nivel de llenado o desde lectura de llenado
            nivelDeLlenado = bundle.getBoolean(NIVEL_DE_LLENADO);
            procesadoPorTAG = bundle.getBoolean(ElementoProcesarBaseActivity.ID_ELEMENTO_PROCESADO_X_TAG, false);
            fechaProcesar = bundle.getLong(ElementoProcesarBaseActivity.ID_FECHA_PROCESAR, new Date().getTime());
            int idElemento = (int) bundle.get(ID_ELEMENTO);

            initTagBotonesPorcentaje();

            // Es posible que el código de elemento sea 0 si se trata de una elemento fuera de
            // ruta que se ha leido por TAG
            elementoSeleccionado = new ElementosDAOProxy().getElemento(idElemento, app.getEmpresa());
        } catch (Throwable e) {
            Logg.error(TAG, "[init] " + e.getMessage());
        }
    }

    private void initTagBotonesPorcentaje() {
        try {
            if (nivelDeLlenado) {
                btnContVacio.setTag((Integer) ID_PORCENTAJE_VACIO);
                btnContLlenado25.setTag((Integer) ID_PORCENTAJE_25);
                btnContLlenado50.setTag((Integer) ID_PORCENTAJE_50);
                btnContLlenado75.setTag((Integer) ID_PORCENTAJE_75);
                btnContLlenado100.setTag((Integer) ID_PORCENTAJE_100);
                btnContDesbordado.setTag((Integer) ID_PORCENTAJE_DESBORDADO);
            } else {
                btnContVacio.setTag((Integer) VALOR_PORCENTAJE_VACIO);
                btnContLlenado25.setTag((Integer) VALOR_PORCENTAJE_25);
                btnContLlenado50.setTag((Integer) VALOR_PORCENTAJE_50);
                btnContLlenado75.setTag((Integer) VALOR_PORCENTAJE_75);
                btnContLlenado100.setTag((Integer) VALOR_PORCENTAJE_100);
                btnContDesbordado.setTag((Integer) VALOR_PORCENTAJE_DESBORDADO);
            }
        } catch (Throwable e) {
            Logg.error(TAG, "[initTagBotonesPorcentaje] " + e.getMessage());
        }
    }

    protected void setControls() {
        try {
            //Boton Contenedor Vacío
            btnContVacio.setOnClickListener(new ClickPorcentaje());
            //Boton Llenado 25%
            btnContLlenado25.setOnClickListener(new ClickPorcentaje());
            //Boton Llenado 50%
            btnContLlenado50.setOnClickListener(new ClickPorcentaje());
            //Boton Llenado 75%
            btnContLlenado75.setOnClickListener(new ClickPorcentaje());
            //Boton Llenado 100%
            btnContLlenado100.setOnClickListener(new ClickPorcentaje());
            //Boton Desbordado
            btnContDesbordado.setOnClickListener(new ClickPorcentaje());

            //TODO: Descomentar si no queremos mostrar el nombre del elemento cuando se procesan varios
            //if(nivelDeLlenado && RutasManager.getInstance().isProcesarVariosElementos()) {
            //    //Si se van a procesar varios no se pone el nombre del elemento.
            //    nombreElemento.setText("");
            //}else

            // Compruebo que no se trate de un nivel de llenado de un elemento que esta fuera
            // de ruta y se ha leido por TAG en cuyo caso no hay elemento
            if (elementoSeleccionado != null) {

                if (elementoSeleccionado.Matricula == null) {
                    nombreElemento.setText(elementoSeleccionado.Nombre);
                } else {
                    nombreElemento.setText(elementoSeleccionado.Matricula + ", " + elementoSeleccionado.Nombre);
                }
            } else {
                nombreElemento.setText("");
            }
        } catch (Throwable e) {
            Logg.error(TAG, "[setControls] " + e.getMessage());
        }
    }


    /**
     * Creamos lectura de llenado y llamamos al job
     *
     * @param porcentajeLlenado
     */
    public void insertLecturaDeLlenado(int porcentajeLlenado) {
        try {
            Logg.info(TAG, "[insertLecturaDeLlenado] nivelDeLlenado: " + nivelDeLlenado + ". porcentajeLlenado: " + porcentajeLlenado);

            if (!nivelDeLlenado) {
                LecturaLlenadoViewModel lecturaLlenado = new LecturaLlenadoViewModel();
                lecturaLlenado.CodigoElemento = elementoSeleccionado != null ? elementoSeleccionado.Codigo : 0;
                if (!procesadoPorTAG)
                    //lecturaLlenado.Fecha = Utils.StringToDateTime(Utils.nowDateTimeToString(getString(R.string.mysql_date)));
                    lecturaLlenado.Fecha = new Date();
                else
                    lecturaLlenado.Fecha = new Date(fechaProcesar);
                lecturaLlenado.IdEmpresa = app.getEmpresa();
                lecturaLlenado.PorcentajeLlenado = porcentajeLlenado;
                lecturaLlenado.CodigoMovil = app.getCodigoMovil();

                // Guardamos la lectura de llenado en la tabla de lecturas de llenado (para pintarlas en el mapa)
                LecturasLlenadoDAO lecturasLlenadoDAO = new LecturasLlenadoDAO();
                LecturasLlenadoModel model = new LecturasLlenadoModel(lecturaLlenado.CodigoElemento, lecturaLlenado.IdEmpresa, lecturaLlenado.PorcentajeLlenado, lecturaLlenado.Fecha);
                Logg.info(TAG, "[insertLecturaDeLlenado] Se va a guardar LecturasLlenadoModel: " + model.toString());

                lecturasLlenadoDAO.save(model);
                EventBus.getDefault().post(new OnRefrescarElementosMapa());

                jobManager = app.getInstance().getJobManager();
                jobManager.addJobInBackground(new InsertarLecturaLlenadoJob(lecturaLlenado));

                // Compruebo que no se trate de un nivel de llenado de un elemento que esta fuera
                // de ruta y se ha leido por TAG en cuyo caso no hay elemento
                if (elementoSeleccionado != null) {
                    Logg.info(TAG, "[insertLecturaDeLlenado] Lectura guardada con un elemento seleccionado. elementoSeleccionado.Nombre: " + elementoSeleccionado.Nombre + ". lecturaLlenado.PorcentajeLlenado: " + lecturaLlenado.PorcentajeLlenado);
                    new ToastMensaje(this).show(getString(R.string.lecturallenado_lectura_insertada, elementoSeleccionado.Nombre, lecturaLlenado.PorcentajeLlenado), R.mipmap.llenadovacio1);
                }

                // setResult(RESULT_OK);

            } else {

                Logg.info(TAG, "[insertLecturaDeLlenado] No se puede realizar la lectura de llenado porque nivelDeLlenado es TRUE");

                // El resultado se trata en la actividad ElementoProcesarNivelLlenadoActivity
                // Intent intent = new Intent();
                // intent.putExtra(PORCENTAJE_LLENADO, porcentajeLlenado);
                // setResult(RESULT_OK, intent);

                ElementoProcesarNivelLlenadoActivity.getInstance().sendNivelLlenado(porcentajeLlenado);
            }

            finish();

        } catch (Throwable e) {
            Logg.error(TAG, "[insertLecturaDeLlenado] " + e.getMessage());
        }
    }

    @Override
    public void onBackPressed() {
        // Desactiva la posibilidad de ir atrás en esta pantalla
    }

    private class ClickPorcentaje implements View.OnClickListener {

        @Override
        public void onClick(View v) {
            insertLecturaDeLlenado((Integer) v.getTag());
        }
    }
}
