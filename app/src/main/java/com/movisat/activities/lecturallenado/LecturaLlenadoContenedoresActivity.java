package com.movisat.activities.lecturallenado;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ListView;

import com.google.android.gms.maps.model.LatLng;
import com.movisat.activities.BaseActivity;
import com.movisat.activities.R;
import com.movisat.adapters.genericos.ListaAdapter;
import com.movisat.adapters.rutas.ListaAdapterElementosCercanos;
import com.movisat.dao.elementos.ElementosDAOProxy;
import com.movisat.dao.elementos.LecturasLlenadoDAO;
import com.movisat.listeners.gps.MyLocationListener;
import com.movisat.log.Logg;
import com.movisat.managers.DracoManager;
import com.movisat.models.configuracion.ConfiguracionModel;
import com.movisat.models.elementos.LecturasLlenadoModel;
import com.movisat.utilities.ToastMensaje;
import com.movisat.viewmodels.elementos.ElementosCercanosViewModel;
import com.movisat.viewmodels.mapa.PosicionGPSViewModel;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LecturaLlenadoContenedoresActivity extends BaseActivity {

    private static final String TAG = "LecturaLlenadoContenedoresActivity";
    private static final int TIEMPO_MAXIMO_MINUTOS = 1;
    private static final String ID_ELEMENTO = "idElemento";
    private ListView listViewElementosCercanos;
    private ArrayList<ElementosCercanosViewModel> elementosCercanos;
    private PosicionGPSViewModel posicionGps;
    private ElementosCercanosViewModel elementoSeleccionado;
    private Button btnLlenadoContenedor;
    private int radioElementos = ElementosCercanosViewModel.RADIO_ELEMENTOS_DEFECTO;

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        try {
            getMenuInflater().inflate(R.menu.menu_main, menu);
            return true;
        } catch (Throwable e) {
            Logg.error(TAG, "[onCreateOptionsMenu] " + e.getMessage());
            return false;
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            if (posicionGps != null) {
                if (!isValidTiempoPosicion(posicionGps)) {
                    new ToastMensaje(this).show(getString(R.string.rutas_posicion_gps_antigua_params,
                          TIEMPO_MAXIMO_MINUTOS), R.mipmap.gps);
                }
            } else {
                new ToastMensaje(this).show(getString(R.string.rutas_no_hay_posicio_gps_valida), R.drawable.gps_85dp);
                finish();
            }
        } catch (Throwable e) {
            Logg.error(TAG, "[onCreate] " + e.getMessage());
        }
    }

    @Override
    protected void setContentView() {
        setContentView(R.layout.activity_llenado_elementos);
    }

    @Override
    protected void init() {
        try {
            super.init();
            btnLlenadoContenedor = (Button) findViewById(R.id.btnLlenadoContenedor);
            listViewElementosCercanos = (ListView) findViewById(R.id.ListView_listado_llenado_contenedores);

            // Obtengo la última posición GPS
            posicionGps = MyLocationListener.getInstance().getUltimaPosicionGPS();

            // Obtengo el radio para procesar elementos
            ConfiguracionModel configuracionModel =
                  DracoManager.getConfigBy(ElementosCercanosViewModel.CONF_RADIO_ELEMENTOS, "30");
            if (configuracionModel != null && !configuracionModel.valor.isEmpty())
                radioElementos = Integer.parseInt(configuracionModel.valor);

            if (posicionGps.fakePosition)
                radioElementos = 10000000; // Si se trata de una posición simulada pongo un radio de diez mil kilómetros (para demos)

            // Obtengo los elementos sin procesar dentro del radio
            if (posicionGps != null && getElementosCercanos(posicionGps)) {

                // Seleeciono siempre por defecto el primer elemento de la lista
                // que es el más cerano
                elementoSeleccionado = elementosCercanos.get(0);

            } else {
                new ToastMensaje(this).show(getString(R.string.lecturallenado_no_hay_elementos_radio_params,
                      radioElementos), R.mipmap.procesar);
                finish();
            }

        } catch (Throwable e) {
            Logg.error(TAG, "[init] " + e.getMessage());
        }
    }

    @Override
    protected void setControls() {
        try {
            //Lista de elementos cercanos
            if (elementosCercanos != null) {
                ListaAdapter listaAdapter =
                      new ListaAdapterElementosCercanos(this, R.layout.entrada_imagen_2text_2textderecha, elementosCercanos, elementoSeleccionado);

                // Se pasan los últimos niveles de llenado al adaptador
                Date fromDate = new Date(System.currentTimeMillis() - 8 * 60 * 60 * 1000);    // 8 horas
                List<LecturasLlenadoModel> lecturasLlenado = new LecturasLlenadoDAO().getLecturasLlenadoFrom(app.getEmpresa(), fromDate);
                if (lecturasLlenado != null && lecturasLlenado.size() > 0)
                    ((ListaAdapterElementosCercanos) listaAdapter).setLecturasLlenado(lecturasLlenado);

                listViewElementosCercanos.setAdapter(listaAdapter);
            }

            // Sólo se muestra la lista si hay más de un elemento sin procesar dentro del radio
            if (elementosCercanos != null && elementosCercanos.size() == 1) {
                btnLlenadoContenedor.performClick();
            }

            // Elemento seleccionado de la lista
            listViewElementosCercanos.setOnItemClickListener(new AdapterView.OnItemClickListener() {
                @Override
                public void onItemClick(AdapterView<?> pariente, View view, int posicion, long id) {
                    try {
                        elementoSeleccionado = (ElementosCercanosViewModel) listViewElementosCercanos.getItemAtPosition(posicion);

                        for (int j = 0; j < listViewElementosCercanos.getChildCount(); j++) {

                            listViewElementosCercanos.getChildAt(j).setBackgroundColor(getResources().getColor(
                                  R.color.bright_foreground_material_dark));
                        }

                        view.setBackgroundColor(getResources().getColor(
                              R.color.verde));
                    } catch (Throwable e) {
                        Logg.error(TAG, "[setControls.listViewElementosCercanos.onItemClick] " + e.getMessage());
                    }
                }
            });

            //Boton llenado
            btnLlenadoContenedor.setOnClickListener(v -> {
                      try {
                          Intent i = new Intent(this, LecturaLlenadoActivity.class);
                          int idElemento = elementoSeleccionado.getElemento().Codigo;
                          i.putExtra(ID_ELEMENTO, idElemento);
                          startActivityForResult(i, ACTIVITY_NIVEL_DE_LLENADO);
                      } catch (Throwable e) {
                          Logg.error(TAG, "[LecturaLlenadoOnClickListener.onClick] " + e.getMessage());
                      }
                  }
            );
        } catch (Throwable e) {
            Logg.error(TAG, "[setControls] " + e.getMessage());
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        try {
            super.onActivityResult(requestCode, resultCode, data);
            if (requestCode == ACTIVITY_NIVEL_DE_LLENADO && resultCode == RESULT_OK) {
                setResult(RESULT_OK);
                finish();
            }
        } catch (Throwable e) {
            Logg.error(TAG, "[onActivityResult] " + e.getMessage());
        }
    }

    /**
     * Recuperamos los elementos cercanos dado un radio de busqueda
     *
     * @param posicion
     * @return true si se han recuperado elementos cercanos
     */
    private boolean getElementosCercanos(PosicionGPSViewModel posicion) {
        elementosCercanos = null;

        try {

            if (posicion != null) {
                // Obtengo la lista de elementos sin procesar dentro del radio
                elementosCercanos = new ElementosDAOProxy().getAllByRadio(
                      app.getEmpresa(), new LatLng(posicion.latitud, posicion.longitud), radioElementos);
            }

        } catch (Throwable e) {
            Logg.error(TAG, "[getElementosCercanos] " + e.getMessage());
        }

        return elementosCercanos != null;
    }

    /**
     * Comprobar si desde la ultima posición a la fecha actual ha pasado X tiempo.
     *
     * @param posicion
     * @return
     */
    private boolean isValidTiempoPosicion(PosicionGPSViewModel posicion) {
        boolean isValid = false;
        try {
            // Si la posición es de hace menos de 1 minuto la damos por buena
            if (posicion != null && System.currentTimeMillis() -
                  posicion.fecha.getTime() <= 60 * 1000 * TIEMPO_MAXIMO_MINUTOS) {
                isValid = true;
            }
        } catch (Throwable e) {
            Logg.error(TAG, "[isValidTiempoPosicion] " + e.getMessage());
        }
        return isValid;
    }

}
