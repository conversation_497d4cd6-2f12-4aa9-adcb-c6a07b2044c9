package com.movisat.activities.wificamera;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.view.TextureView;
import android.view.View;
import android.widget.ImageButton;

import com.movisat.activities.R;
import com.movisat.managers.ConfiguracionManager;
import com.movisat.managers.DracoManager;
import com.movisat.models.configuracion.ConfiguracionModel;
import com.movisat.utilities.ToastMensaje;
import com.movisat.wificamera.CameraManager;
import com.movisat.wificamera.FrameProviderH264;
import com.movisat.wificamera.StreamClient;
import com.movisat.wificamera.VideoSurfaceManager;

import java.io.ByteArrayOutputStream;


public class WifiCameraActivity extends Activity {
    private static final String TAG = "CAMERA_ACTIVITY";

    static final int WIDTH = 1280;
    static final int HEIGHT = 720;

    private VideoSurfaceManager mVideoSurfaceManager = null;   // Muestra los frames en el TextureView

    private CameraManager cameraInstance = null; // Instancia de la cámara que se usa (1 o 2)

    // Controles
    private TextureView mTextureView;       // TextureView que mostrará la imagen
    private ImageButton btnCapture;         // Botón para congelar la imagen y poder capturarla
    private ImageButton btnAcceptCapture;   // Botón para aceptar la imagen congelada y cerrar el activity
    private ImageButton btnBack;            // Botón para cancelar el activity
    private static StreamClient.OnConnectionChangedListener mOnConnectionChangedListener = null;
    private boolean auto = false;

    private static Bitmap capture = null;
    private static Bitmap capture2 = null;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {

            Bundle bundle = getIntent().getExtras();
            ConfiguracionModel conf;

            auto = bundle.getBoolean("auto"); // Fotos automáticas

            if (bundle.getInt("camara") == 1) {
                conf = ConfiguracionManager.getConfBy("dracoSensor.camID", "");
                cameraInstance = CameraManager.getInstance(conf.valor);
            } else  if (bundle.getInt("camara") == 2){
                conf = ConfiguracionManager.getConfBy("dracoSensor.camID2", "");
                cameraInstance = CameraManager.getInstance2(conf.valor);
            }

            capture = capture2 = null;

            if (!cameraInstance.startClient()) {
                new ToastMensaje(this).show(getString(R.string.cam_disconnected));
                finish();
                return;
            } else
                cameraInstance.startStreaming();

            setContentView(R.layout.activity_wificamera);

            // Se obtienen los controles
            mTextureView = (TextureView) findViewById(R.id.textureCamera);
            btnCapture = (ImageButton) findViewById(R.id.btnCapture);
            btnAcceptCapture = (ImageButton) findViewById(R.id.btnAcceptCapture);
            btnBack = (ImageButton) findViewById(R.id.btnBack);

            // Se crea el objeto que mostrará los frames del FrameProvider en el TextureView
            FrameProviderH264 frameProvider = cameraInstance.getFrameProvider();
            mVideoSurfaceManager = new VideoSurfaceManager(mTextureView, frameProvider, WIDTH, HEIGHT);

            // Listener para el botón de captura.
            btnCapture.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {

                    // Se congela la imagen
                    mVideoSurfaceManager.pause();

                    // Se sustituye el botón de capturar por el de aceptar la captura
                    btnCapture.setVisibility(View.INVISIBLE);
                    btnAcceptCapture.setVisibility(View.VISIBLE);
                }
            });

            // Listener para el botón de aceptar la imagen (una vez detenida)
            btnAcceptCapture.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    Intent intent = new Intent();

                    try {
                        // Se obtiene la imagen y se introduce en el Intent como array de bytes
                        capture = mVideoSurfaceManager.captureImage();

                        if (capture != null) {
                            ByteArrayOutputStream stream = new ByteArrayOutputStream();
                            capture.compress(Bitmap.CompressFormat.JPEG, 100, stream);
                            intent.putExtra("capture", stream.toByteArray());
                        }

                        setResult(RESULT_OK, intent);

                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    finish();
                }
            });

            btnBack.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    onBackPressed();
                }
            });

            if (auto && mVideoSurfaceManager != null) {

                new Thread() {
                    @Override
                    public void run() {
                        try {

                            // Espero a que se reciba la imagen (10 seg)
                            for (int i = 0; i < 100 && !mVideoSurfaceManager.isReady(); i++)
                                Thread.sleep(100);

                            // Se obtiene la imagen y se introduce en el Intent como array de bytes
                            mVideoSurfaceManager.pause();
                            capture = mVideoSurfaceManager.captureImage();

// De momento la segunda foto no la hago porque no da tiempo, la maniobra es muy rápida
//                            mVideoSurfaceManager.resume();
//                            Thread.sleep(2000);
//                            mVideoSurfaceManager.pause();
//                            capture2 = mVideoSurfaceManager.captureImage();

                            setResult(RESULT_OK);

                            cameraInstance.stopStreaming();
                            if (mVideoSurfaceManager != null)
                                mVideoSurfaceManager.stop();
                            cameraInstance.disconnect();

                            finish();
                            finalize();

                        } catch (Throwable e) {

                            e.printStackTrace();
                        }
                    }
                }.start();
            }

        } catch (Throwable e) {

            e.printStackTrace();
        }
    }

    public static byte[] getLastPhoto(int numPhoto) {
        byte[] res = null;
        ByteArrayOutputStream stream;

        try {

            switch (numPhoto) {
                case 1:
                    if (capture != null) {
                        stream = new ByteArrayOutputStream();
                        capture.compress(Bitmap.CompressFormat.JPEG, 100, stream);
                        res = stream.toByteArray();
                    }
                    break;

                case 2:
                    if (capture2 != null) {
                        stream = new ByteArrayOutputStream();
                        capture2.compress(Bitmap.CompressFormat.JPEG, 100, stream);
                        res = stream.toByteArray();
                    }
            }

        } catch (Throwable e) {
        }

        return res;
    }

    @Override
    public void onBackPressed() {

        if (mVideoSurfaceManager != null) {
            // Si la imagen está pausada, se reanuda para volver a capturar la imagen.
            // Si no lo está, se cancela la captura.
            if (mVideoSurfaceManager.isPaused()) {
                mVideoSurfaceManager.resume();

                // Se vuelve a mostrar el botón de capturar
                btnAcceptCapture.setVisibility(View.INVISIBLE);
                btnCapture.setVisibility(View.VISIBLE);
            } else {
                // Se cancela la captura
                setResult(RESULT_CANCELED);
                finish();
            }
        } else {
            // Se cancela la captura
            setResult(RESULT_CANCELED);
            finish();
        }

    }

    @Override
    public void finish() {
        if (cameraInstance != null) {
            cameraInstance.stopStreaming();
            if (mVideoSurfaceManager != null)
                mVideoSurfaceManager.stop();
            cameraInstance.disconnect();
        }
        super.finish();
    }

    @Override
    public void onStop() {
        super.onStop();
        if (mVideoSurfaceManager != null)
            mVideoSurfaceManager.stop();
    }

}
