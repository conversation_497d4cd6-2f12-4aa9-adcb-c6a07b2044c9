package com.movisat.activities.revisiones;

import android.app.Service;
import android.content.Context;
import android.graphics.Rect;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;

import com.movisat.activities.BaseActivity;
import com.movisat.activities.R;
import com.movisat.dao.revisiones.RevisionesDAO;
import com.movisat.models.revisiones.RevisionesDatosModel;
import com.movisat.utilities.SoftKeyboard;

public class RevisionesObservacionesActivity extends BaseActivity {
    private static RevisionesObservacionesActivity instance = null;
    private ImageButton btnConfirmacion;
    private EditText editObservaciones;
    private Button btnHideKeyboard;
    private RevisionesDAO revisionesDAO = null;
    private SoftKeyboard softKeyboard;

    int idRevision, idAtributo;
    String valor;

    RevisionesDatosModel revisionesDatosModel;

    public static RevisionesObservacionesActivity getInstance() {
        return instance;
    }

    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View rootView = null;
        try {
            super.onCreate(savedInstanceState);
            rootView = inflater.inflate(R.layout.activity_revision_detalle, container, false);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return rootView;
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        try {
            // Inflate the menu; this adds items to the action bar if it is present.
            //getMenuInflater().inflate(R.menu.menu_buscar, menu);
            getMenuInflater().inflate(R.menu.menu_main, menu);
            return true;
        } catch (Throwable e) {
            logger.WriteError(e);
            return false;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);
            logger.WriteInfo("Iniciando RevisionesDetalleActivity");
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void setContentView() {
        try {
            setContentView(R.layout.activity_revision_detalle);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void init() {
        try {
            // Creamos los botones
            btnConfirmacion = (ImageButton) findViewById(R.id.btnConfirmacion);
            btnHideKeyboard = (Button) findViewById(R.id.btnHideKeyboard);

            Bundle bundle = getIntent().getExtras();
            idRevision = bundle.getInt(RevisionesActivity.REVISION_ID);
            idAtributo = bundle.getInt(RevisionesActivity.REVISION_ID_ATRIBUTO);

            revisionesDAO = new RevisionesDAO();
            revisionesDatosModel = revisionesDAO.getRevisionDetalleBy(idRevision, idAtributo, app.getCodigoMovil());

            valor = revisionesDatosModel.Valor;

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void setControls() {
        try {
            setTituloActionBar(R.string.title_ecosat_rutas);

            btnConfirmacion.setOnClickListener(new OnClickListenerConfirmacion());

            editObservaciones = (EditText) findViewById(R.id.observaciones);
            if (editObservaciones != null && revisionesDatosModel.Observaciones != null)
                editObservaciones.setText(revisionesDatosModel.Observaciones);

            // Se crea el objeto para manejar el SoftKeyboard y saber cuándo se muestra / oculta
            softKeyboard = new SoftKeyboard(this);
            softKeyboard.setOnKeyboardVisibilityChangeListener(new SoftKeyboard.OnKeyboardVisibilityChangeListener() {
                @Override
                public void onVisibilityChange(final boolean keyboardVisible) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                btnHideKeyboard.setVisibility(keyboardVisible ? View.VISIBLE : View.INVISIBLE);
                            } catch (Throwable e) {
                                logger.WriteError(e);
                            }
                        }
                    });
                }
            });

            // Listener para ocultar el teclado al pulsar el botón de ocultar
            btnHideKeyboard.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    try {
                        softKeyboard.hide();
                    } catch (Throwable e) {
                        logger.WriteError(e);
                    }
                }
            });

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public class OnClickListenerConfirmacion implements View.OnClickListener {

        @Override
        public void onClick(View v) {
            try {
                if (isTrabajadoresDescansando()) {
                    return;
                }
                //Guardamos un nuevo valor o lo actualizamos
                String observaciones = editObservaciones.getText().toString();
                RevisionesDatosModel rev =
                        new RevisionesDatosModel(idRevision, idAtributo, app.getEmpresa(),
                                app.getCodigoMovil(), true, valor, observaciones);
                revisionesDAO.save(rev);
                finish();
            } catch (Throwable e) {
                logger.WriteError(e);
            }
        }
    }

    @Override
    public void finish() {
        if (softKeyboard != null)
            softKeyboard.detach();
        super.finish();
    }
}
