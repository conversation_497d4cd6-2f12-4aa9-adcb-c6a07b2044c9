package com.movisat.activities.revisiones;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.AbsListView;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ListView;

import com.movisat.activities.R;
import com.movisat.adapters.genericos.ListaAdapter;
import com.movisat.adapters.revisiones.EditTextRevisiones;
import com.movisat.dao.revisiones.RevisionesAtributosDAO;
import com.movisat.dao.revisiones.RevisionesDAO;
import com.movisat.models.revisiones.RevisionesDatosModel;
import com.movisat.activities.BaseActivity;
import com.movisat.adapters.revisiones.ListaAdapterRevisionesAtributos;
import com.movisat.models.revisiones.RevisionesAtributosModel;

import java.util.ArrayList;
import java.util.List;

public class RevisionesAtributosActivity extends BaseActivity {
    private static RevisionesAtributosActivity instance = null;
    private ArrayList<RevisionesAtributosModel> revisionesAtributosDatos;
    private ListView listViewRevisionesAtributos;
    private RevisionesDAO revisionesDAO = null;
    private RevisionesAtributosDAO revisionesAtributosDAO = null;
    private ImageButton btnConfirmacion;

    int idRevision;

    public static RevisionesAtributosActivity getInstance() {
        return instance;
    }

    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        View rootView = null;
        try {

            rootView = inflater.inflate(R.layout.activity_revision, container, false);
        }catch (Throwable e){
            logger.WriteError(e);
        }
        return rootView;

    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        try {
            // Inflate the menu; this adds items to the action bar if it is present.
            //getMenuInflater().inflate(R.menu.menu_buscar, menu);
            getMenuInflater().inflate(R.menu.menu_main, menu);
            return true;
        }catch (Throwable e){
            logger.WriteError(e);
            return false;
        }
    }

    @Override
    public void onResume() {
        super.onResume();

        try {
            fillListaRevisionesAtributos();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            logger.WriteInfo("Iniciando RevisionesAtributosActivity");
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    @Override
    protected void setContentView() {
        try {
            setContentView(R.layout.activity_revision);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void init() {
        try {
            // Creamos los botones
            btnConfirmacion = (ImageButton) findViewById(R.id.btnConfirmacion);

            Bundle bundle = getIntent().getExtras();
            idRevision = bundle.getInt(RevisionesActivity.REVISION_ID);

            // Creamos la lista
            listViewRevisionesAtributos = (ListView) findViewById(R.id.ListView_listado_revisiones);

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void setControls() {
        try {
            setTituloActionBar(R.string.title_ecosat_rutas);

            listViewRevisionesAtributos.setAdapter(new ListaAdapterRevisionesAtributos(
                    this, R.layout.entrada_revision, revisionesAtributosDatos,idRevision, app, this));

            btnConfirmacion.setOnClickListener(new OnClickListenerConfirmacion());

            addListViewScrollListener();

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Construye la lista de revisiones de atributos
     */
    private void fillListaRevisionesAtributos() {
        try {
            if (listViewRevisionesAtributos != null) {

                RevisionesDAO revisionesDAO = new RevisionesDAO();
                List<RevisionesDatosModel> revisionesDatosModel =
                        revisionesDAO.getRevisionDetalleBy(idRevision, app.getCodigoMovil());

                revisionesAtributosDAO = new RevisionesAtributosDAO();
                revisionesAtributosDatos = (ArrayList<RevisionesAtributosModel>)
                        revisionesAtributosDAO.getAtributosBy(revisionesDatosModel);

                if (revisionesAtributosDatos != null && revisionesAtributosDatos.size() > 0) {
                    ListaAdapter listaAdapter = (ListaAdapter) listViewRevisionesAtributos.getAdapter();
                    if (listaAdapter != null)
                        listaAdapter.updateList(revisionesAtributosDatos);
                }
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Añade un ScrollListener a la lista de revisiones para evitar un error en el ListView.
     * Este error ocurre cuando un EditText se encuentra en una fila de un ListView y tiene el foco.
     * En este punto, el teclado se muestra en la pantalla. Si se hace scroll hasta un punto en el
     * que el EditText deja de ser visible, el error ocurre.
     * (java.lang.IllegalArgumentException: parameter must be a descendant of this view)
     * https://stackoverflow.com/questions/7100555/preventing-catching-illegalargumentexception-parameter-must-be-a-descendant-of
     */
    private void addListViewScrollListener() {
        try {
            listViewRevisionesAtributos.setRecyclerListener(new AbsListView.RecyclerListener() {
                @Override
                public void onMovedToScrapHeap(View view) {
                    try {
                        if ( view.hasFocus()){
                            view.clearFocus();
                            // Opcional: se oculta el teclado
                            InputMethodManager imm = (InputMethodManager) view.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                            imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
                        }

                    } catch (Throwable e) {
                        logger.WriteError(e);
                    }
                }
            });

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public class OnClickListenerConfirmacion implements View.OnClickListener {

        @Override
        public void onClick(View v) {
            try {
                if (isTrabajadoresDescansando()) {
                    return;
                }

                finish();
            }catch (Throwable e){
                logger.WriteError(e);
            }
        }
    }
}
