package com.movisat.activities.revisiones;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ImageButton;
import android.widget.ListView;

import com.jobqueue.JobManager;
import com.movisat.activities.BaseActivity;
import com.movisat.activities.R;
import com.movisat.activities.personal.BasePersonalActivity;
import com.movisat.activities.personal.PersonalIdentificacionActivity;
import com.movisat.adapters.genericos.ListaAdapter;
import com.movisat.adapters.revisiones.ListaAdapterRevisiones;
import com.movisat.application.RutasApplication;
import com.movisat.dao.revisiones.RevisionesDAO;
import com.movisat.jobs.revisiones.InsertarRevisionJob;
import com.movisat.managers.ConfiguracionEcoRutasManager;
import com.movisat.managers.RevisionesManager;
import com.movisat.models.revisiones.RevisionesDatosModel;
import com.movisat.models.revisiones.RevisionesModel;
import com.movisat.utilities.ToastMensaje;
import com.movisat.viewmodels.revisiones.RevisionesViewModel;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.pedant.SweetAlert.SweetAlertDialog;

public class RevisionesActivity extends BaseActivity {
    private static final RevisionesActivity instance = null;
    private ImageButton btnConfirmacion;
    private List<RevisionesModel> revisionesDatos;
    private ListView listViewRevisiones;
    private RevisionesModel revisionSelecionada = null;
    private RevisionesDAO revisionesDAO = null;
    private static JobManager jobManager = null;
    private int idConductor;
    public static final String REVISION_ID = "idRevision";
    public static final String REVISION_ID_ATRIBUTO = "idAtributo";
    public static final String REVISION_VALOR = "valorRevision";
    private int tipoSolicitud;
    private Date fecha;
    
    public static RevisionesActivity getInstance() {
        return instance;
    }

    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View rootView = null;
        try {
            super.onCreate(savedInstanceState);
            rootView = inflater.inflate(R.layout.activity_revision, container, false);

        }catch (Throwable e){
            logger.WriteError(e);
        }
        return rootView;

    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        try {
            // Inflate the menu; this adds items to the action bar if it is present.
            //getMenuInflater().inflate(R.menu.menu_buscar, menu);
            getMenuInflater().inflate(R.menu.menu_main, menu);
            return true;
        }catch (Throwable e){
            logger.WriteError(e);
            return false;
        }
    }

    @Override
    public void onResume() {
        super.onResume();

        try {
            fillListaRevisiones();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
        }catch (Throwable e){
            logger.WriteError(e);
        }
    }

    @Override
    protected void setContentView() {
        try {
            setContentView(R.layout.activity_revision);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void init() {
        try {
            // Creamos los botones;
            btnConfirmacion = findViewById(R.id.btnConfirmacion);

            // Creamos la lista de revisiones
            listViewRevisiones = findViewById(R.id.ListView_listado_revisiones);
            revisionesDAO = new RevisionesDAO();

            Bundle bundle = getIntent().getExtras();
            idConductor = bundle.getInt(PersonalIdentificacionActivity.CONDUCTOR_ID);

            jobManager = RutasApplication.getInstance().getJobManager();

            tipoSolicitud = getIntent().getIntExtra(BasePersonalActivity.TURNO,0);

            if (tipoSolicitud == BasePersonalActivity.FIN_TURNO) {
                long fechaFinTurno = getIntent().getLongExtra("finTurno", 0);
                fecha = new Date(fechaFinTurno);
            } else {
                fecha = new Date();
            }

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    protected void setControls() {
        try {
            setTituloActionBar(R.string.title_ecosat_rutas);
            listViewRevisiones.setAdapter(new ListaAdapterRevisiones(this, R.layout.entrada_2campos_izq_der, revisionesDatos,
                    revisionesDAO, revisionSelecionada, app));
            btnConfirmacion.setOnClickListener(new OnClickListenerConfirmacion());

            listViewRevisiones.setOnItemClickListener(new AdapterView.OnItemClickListener() {
                @Override
                public void onItemClick(AdapterView<?> parent, View view, int pos, long id) {
                    revisionSelecionada = (RevisionesModel) listViewRevisiones.getItemAtPosition(pos);

                    if(view != null) {
                        for (int j = 0; j < listViewRevisiones.getChildCount(); j++) {
                            listViewRevisiones.getChildAt(j).setBackgroundColor(getResources().getColor(
                                    R.color.bright_foreground_material_dark));
                        }
                        view.setBackgroundColor(getResources().getColor(R.color.verde));
                    }

                    Intent i = new Intent(RevisionesActivity.this, RevisionesAtributosActivity.class);
                    i.putExtra(REVISION_ID, revisionSelecionada.Id);

                    startActivity(i);
                }
            });

            fillListaRevisiones();

            // Si sólo hay una revisión muestro directamente los elementos de la misma
            if(revisionesDatos.size() == 1) {
                listViewRevisiones.performItemClick(null, 0, 0);
            }

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    @Override
    public void onBackPressed() {
    }

    @Override
    protected void onBotonAtrasClick(final Activity activity) {
        try {
            //TODO: De momento las revisiones son obligatorias pero hay que hacerlo configurable
            if(true) {
                new ToastMensaje(this).show(getString(R.string.revisiones_no_puede_salir));
            } else {
                final SweetAlertDialog dlg = new SweetAlertDialog(RevisionesActivity.this, SweetAlertDialog.WARNING_TYPE)
                        .setTitleText(getString(R.string.atencion))
                        .setContentText(getString(R.string.revisiones_quiere_salir))
                        .setConfirmText(getString(R.string.si))
                        .setCancelText(getString(R.string.no));

                dlg.setConfirmClickListener(new SweetAlertDialog.OnSweetClickListener() {
                    @Override
                    public void onClick(SweetAlertDialog sweetAlertDialog) {
                        // Aunque salgan con botón atras se envían las revisiones
                        sendRevisiones();
                        dlg.dismiss();
                        finish();
                    }
                }).show();
            }
        } catch(Throwable e) {
            logger.WriteError(e);
        }
    }

    private void fillListaRevisiones() {
        try {
            if (listViewRevisiones != null && tipoSolicitud>0) {
                revisionesDatos = ConfiguracionEcoRutasManager.getInstance().getRevisiones(tipoSolicitud);

                if (!revisionesDatos.isEmpty()) {
                    ListaAdapter listaAdapter = (ListaAdapter) listViewRevisiones.getAdapter();
                    if (listaAdapter != null)
                        listaAdapter.updateList(revisionesDatos);
                }
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public class OnClickListenerConfirmacion implements View.OnClickListener {

        @Override
        public void onClick(View v) {

            boolean revisionOk = false;
            boolean revisionIncompleta = false;
            int nAtributosRevisados = 0;
            try {
                //TODO: De momento las revisiones son obligatorias pero hay que hacerlo configurable
                if (true) {

                    if (revisionesDatos.size() > 0) {
                        for (RevisionesModel rev : revisionesDatos) {

                            if (rev.RevisionDetalle != null && rev.RevisionDetalle.size() > 0) {
                                nAtributosRevisados = 0;
                                for (RevisionesDatosModel det : rev.RevisionDetalle) {
                                    //if (!det.Revisado) {
                                    //    new ToastMensaje(RevisionesActivity.this).show(getString(R.string.revisiones_faltan_revisiones));
                                    //    return;
                                    //}
                                    if (det.Revisado)
                                        nAtributosRevisados++;

                                }
                                if (rev.RevisionDetalle.size() == nAtributosRevisados) {
                                    //Revision completa de al menos una revision
                                    revisionOk = true;
                                } else if (nAtributosRevisados > 0) {
                                    revisionIncompleta = true;
                                }
                            }
                        }
                    }

                    if (!revisionOk || revisionIncompleta) {
                        new ToastMensaje(RevisionesActivity.this).show(getString(R.string.revisiones_faltan_revisiones));
                        return;
                    }
                }

                final SweetAlertDialog dlg = new SweetAlertDialog(RevisionesActivity.this, SweetAlertDialog.WARNING_TYPE)
                        .setTitleText(getString(R.string.atencion))
                        .setContentText(getString(R.string.revisiones_quiere_enviar))
                        .setConfirmText(getString(R.string.si))
                        .setCancelText(getString(R.string.no));

                dlg.setConfirmClickListener(new SweetAlertDialog.OnSweetClickListener() {
                    @Override
                    public void onClick(SweetAlertDialog sweetAlertDialog) {
                        try {
                            dlg.dismiss();

                            sendRevisiones();
                            finish();
                        } catch (Throwable e) {
                            logger.WriteError(e);
                        }
                    }
                }).show();
            }catch (Throwable e){
                logger.WriteError(e);
            }
        }
    }

    private void sendRevisiones() {
        try {
            if(revisionesDatos != null && revisionesDatos.size() > 0) {
                // Ahora recorro todas las revisiones y creo la lista para enviar al servidor
                List<RevisionesViewModel> revisionesViewModels =
                        RevisionesManager.getInstance().parse(revisionesDatos, idConductor, fecha);

                // Creo el job para enviar la información al servidor
                jobManager.addJobInBackground(new InsertarRevisionJob(revisionesViewModels));

                // Inicializo todas las reviciones para la siguiente vez
                revisionesDAO.initRevisionesBy(app.getCodigoMovil());
            }
        } catch(Throwable e) {
            logger.WriteError(e);
        }
    }
}
