package com.movisat.wificamera;

import android.graphics.Bitmap;
import android.graphics.SurfaceTexture;
import android.media.MediaCodec;
import android.media.MediaFormat;
import android.os.AsyncTask;
import android.os.Build;
import android.util.Log;
import android.view.Surface;
import android.view.TextureView;

import java.nio.ByteBuffer;

/**
 * Created by dsanchez on 22/03/2018.
 */

public class VideoSurfaceManager implements TextureView.SurfaceTextureListener {

    private static final String TAG = "VIDEO_SURF_MGR";

    private int mVideoWidth;
    private int mVideoHeight;

    private MediaCodec mCodec;                  // Codec para decodificación
    private DecodeFramesTask mFrameTask;        // AsyncTask que toma los frames H264 del FrameProvider y utiliza el decodificador para mostrarlos en el TextureView
    private FrameProviderH264 mFrameProvider;   // Cola de frames recibidos en formato H264
    private TextureView mTextureView;           // TextureView que mostrará la imagen

    private boolean mPaused = false;             // Indica si el vídeo está pausado
    private boolean mKeyFrameReceived = false;   // Indica si se ha recibido un primer keyframe

    private boolean imageReady = false;
    private int frameCount = 0;

    /**
     * Inicializa el objeto para mostrar los frames de un FrameProvider en formato H264
     * en un TextureView.
     *
     * @param textureView   TextureView en el que se mostrarán los frames de vídeo.
     * @param frameProvider Cola de donde se leerán los frames.
     * @param videoWidth    Ancho del vídeo en píxeles.
     * @param videoHeight   Alto del vídeo en píxeles.
     */
    public VideoSurfaceManager(TextureView textureView, FrameProviderH264 frameProvider,
                               int videoWidth, int videoHeight) {
        mTextureView = textureView;
        mFrameProvider = frameProvider;
        mVideoWidth = videoWidth;
        mVideoHeight = videoHeight;
        // Se asocian lo eventos del SurfaceTexture a esta clase
        mTextureView.setSurfaceTextureListener(this);
    }

    /**
     * Establece la instancia del FrameProvider.
     */
    public void setFrameProvider(FrameProviderH264 frameProvider) {
        mFrameProvider = frameProvider;
    }

    /**
     * Indica si la imagen está pausada.
     */
    public boolean isPaused() {
        return mPaused;
    }

    /**
     * Pausa la imagen.
     */
    public void pause() {
        if (mPaused)
            return;
        if (mCodec != null)
            mCodec.stop();
        mPaused = true;
        mKeyFrameReceived = false;
    }

    /**
     * Reanuda el vídeo desde un estado de pausa.
     */
    public void resume() {

        try {

            if (!mPaused)
                return;
            configureCodec();
            mCodec.start();
            mPaused = false;
            mKeyFrameReceived = false;

        } catch (Throwable e) {

            e.printStackTrace();
        }
    }

    /**
     * Captura el frame actualmente mostrado y lo devuelve como un Bitmap del tamaño del vídeo.
     */
    public Bitmap captureImage() {
        try {
//            return mTextureView.getBitmap(mVideoWidth, mVideoHeight);
            return mTextureView.getBitmap();
        } catch (Throwable e) {
        }
        return null;
    }

    @Override
    // Se llama cuando el SurfaceTexture del TextureView está listo para ser usado.
    public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height) {

        configureCodec();

        try {
            // Inicia el codec
            mCodec.start();
            // Crea una AsyncTask para obtener los frames del FrameProvider y decodificarlos
            mFrameTask = new DecodeFramesTask();
            mFrameTask.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    /**
     * Configura el Codec para la decodificación de vídeo H264.
     */
    private void configureCodec() {

        // Crea el formato para el MediaCodec con las especificaciones del vídeo
        MediaFormat format = MediaFormat.createVideoFormat(MediaFormat.MIMETYPE_VIDEO_AVC, mVideoWidth, mVideoHeight);
        // Establece el tamaño del buffer
        format.setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, 1024 * 1000);

        try {
            // Obtiene una instancia del decodificador indicando el formato del vídeo
            mCodec = MediaCodec.createDecoderByType(MediaFormat.MIMETYPE_VIDEO_AVC);
            // Configura el Codec, vinculándolo al SurfaceTexture del TextureView
            SurfaceTexture st = mTextureView.getSurfaceTexture();
            mCodec.configure(format, new Surface(mTextureView.getSurfaceTexture()), null, 0);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    @Override
    // Invoked when the SurfaceTexture's buffers size changed
    public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) {
    }

    @Override
    // Invoked when the specified SurfaceTexture is about to be destroyed
    public boolean onSurfaceTextureDestroyed(SurfaceTexture surface) {
        return false;
    }

    @Override
    // Invoked when the specified SurfaceTexture is updated through updateTexImage()
    public void onSurfaceTextureUpdated(SurfaceTexture surface) {
    }

    // Devuelve si se ha recibido un key-frame (una imagen completa)
    public boolean isReady() {
        return imageReady;
    }


    /**
     * Tarea asíncrona para la obtener los frames del FrameProvider y decodificarlos utilizando
     * el Codec, que los mostrará en el TextureView.
     */
    private class DecodeFramesTask extends AsyncTask<Void, Void, Void> {

        @Override
        protected Void doInBackground(Void... voids) {
            MediaCodec.BufferInfo info = new MediaCodec.BufferInfo();

            while (!isCancelled()) {
                try {
                    if (mPaused) {
                        Thread.sleep(100);
                        continue;
                    }

                    // Obtiene el siguiente frame
                    FrameProviderH264.FramePacket framePacket = mFrameProvider.nextFrame();

                    if (framePacket == null) {
                        Thread.sleep(100);
                        continue;
                    }

                    // Para que la imagen se muestre correctamente y no se produzcan errores, es
                    // necesario que el primer frame mostrado sea un keyframe (un frame que contiene
                    // toda la imagen e información de formato, y no solo las diferencias entre los
                    // frames). Se ignoran todos los frames hasta obtener un keyframe.
                    if (!mKeyFrameReceived) {
                        if (framePacket.isKeyFrame())
                            mKeyFrameReceived = true;
                        else {
                            Thread.sleep(20);
                            continue;
                        }
                    }

                    // Se pasa este frame al Codec para que lo decodifique sobre el SurfaceTexture
                    if (isCancelled() || mPaused)
                        continue;

                    // Obtiene el índice del buffer de entrada del decodificador
                    int inputIndex = mCodec.dequeueInputBuffer(-1);// Pass in -1 here as in this example we don't have a playback time reference

                    if (inputIndex >= 0) {

                        ByteBuffer buffer;

                        // Obtiene el buffer donde colocar el frame
                        if (Build.VERSION.SDK_INT >= 21) {
                            buffer = mCodec.getInputBuffer(inputIndex);
                        } else {
                            ByteBuffer inputs[] = mCodec.getInputBuffers();
                            buffer = inputs[inputIndex];
                        }

                        // Se indica al decodificador que procese el frame
                        buffer.put(framePacket.frame);
                        mCodec.queueInputBuffer(inputIndex, 0, framePacket.frame.length, 0, 0);

                        if (frameCount > 10)
                            imageReady = true;
                        frameCount++;
                    }

                    int outputIndex = mCodec.dequeueOutputBuffer(info, 0);
                    if (outputIndex >= 0)
                        mCodec.releaseOutputBuffer(outputIndex, true);

                } catch (InterruptedException ie) {
                    Log.i(TAG, "VideoSurfaceManager interrumpido.");
                } catch (Throwable e) {
                    e.printStackTrace();
                }
            }
            return null;
        }

        @Override
        protected void onPostExecute(Void aVoid) {
            try {
                mCodec.stop();
                mCodec.release();
            } catch (Throwable e) {
                e.printStackTrace();
            }
            mFrameProvider.clear();
        }
    }

    /**
     * Detiene el procesado de la imagen.
     */
    public void stop() {
        try {
            if (mFrameTask == null || mFrameTask.isCancelled())
                return;
            mFrameTask.cancel(false);
            mFrameProvider.clear();
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }
}
