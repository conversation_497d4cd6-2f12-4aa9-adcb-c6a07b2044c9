package com.movisat.wificamera;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.MediaCodec;
import android.media.MediaFormat;
import android.util.Log;

import java.nio.ByteBuffer;
import java.util.LinkedList;

/**
 * Created by dsanchez on 21/03/2018.
 */

/**
 * Implementa una cola para almacenar los frames de vídeo recibidos.
 */
public class FrameProviderH264 {

    public enum DropFramesMode {DROP_NEW_FRAMES, DROP_OLD_FRAMES}

    private int mCapacity = 1;
    private DropFramesMode mDropFramesMode = DropFramesMode.DROP_NEW_FRAMES;
    private LinkedList<FramePacket> frames;

    private static String semaph = "Fram_semaph";

    /**
     * Almacena un frame y su información (recibida en el streaming).
     */
    public class FramePacket {
        byte[] info;
        public byte[] frame;

        FramePacket(byte[] info, byte[] frame) {
            this.info = info;
            this.frame = frame;
        }

        /**
         * Indica si el frame es un keyFrame (si el tercer byte del array de información es 1).
         */
        boolean isKeyFrame() {
            return (info == null || info.length < 3) ? false : (info[2] == 1);
        }
    }

    /**
     * Inicializa el contenedor de frames.
     */
    public FrameProviderH264() {
        frames = new LinkedList<>();
    }

    /**
     * Inicializa el contenedor de frames indicando su capacidad máxima y el modo de manejar los
     * frames introducidos cuando se ha alcanzado esta capacidad máxima.
     *
     * @param capacity       Número de frames que pueden ser almacenados.
     * @param dropFramesMode Indica si el frame introducido con la función putFrame() será desechado
     *                       (DROP_NEW_FRAMES) o si se introducirá eliminando el frame almacenado
     *                       más antiguo (DROP_OLD_FRAMES).
     */
    public FrameProviderH264(int capacity, DropFramesMode dropFramesMode) {
        this();
        mCapacity = capacity;
        mDropFramesMode = dropFramesMode;
    }

    public void setDropFramesMode(DropFramesMode dropFramesMode) {
        mDropFramesMode = dropFramesMode;
    }

    public DropFramesMode getDropFramesMode() {
        return mDropFramesMode;
    }

    public int getCapacity() {
        return mCapacity;
    }

    /**
     * Introduce un frame en la lista, junto con su array de bytes de información.
     *
     * @param info        Array de bytes de información del frame.
     * @param infoLength  Tamaño del array de bytes de información.
     * @param frame       Array de bytes del frame recibido.
     * @param frameLength Tamaño del array de bytes del frame.
     */
    public void putFrame(byte[] info, int infoLength, byte[] frame, int frameLength) {

        synchronized (semaph) {
            if (frames.size() >= mCapacity) {
                if (mDropFramesMode == DropFramesMode.DROP_NEW_FRAMES)
                    return;                 // Se pierde el frame
                else
                    frames.removeFirst();   // Se quita el primer frame para dejar sitio al nuevo
            }
            ;

            // Se copia el frame y la información para ser almacenada
            byte[] infoCopy = null;
            byte[] frameCopy;

            if (info != null) {
                infoCopy = new byte[frameLength];
                System.arraycopy(info, 0, infoCopy, 0, infoLength);
            }

            frameCopy = new byte[frameLength];
            System.arraycopy(frame, 0, frameCopy, 0, frameLength);

            frames.add(new FramePacket(infoCopy, frameCopy));
        }
    }

    /**
     * Introduce un frame en la lista sin especificar información.
     *
     * @param frame       Array de bytes del frame recibido.
     * @param frameLength Tamaño del array de bytes del frame.
     */
    public synchronized void putFrame(byte[] frame, int frameLength) {
        putFrame(null, 0, frame, frameLength);
    }

    /**
     * Obtiene el siguiente frame de la lista.
     */
    public FramePacket nextFrame() {
        synchronized (semaph) {
            if (frames.size() > 0)
                return frames.removeFirst();
        }
        return null;
    }

    /**
     * Elimina todos los frames almacenados.
     */
    public synchronized void clear() {
        frames.clear();
    }

    /**
     * Decodes the next frame and returns it as a Bitmap.
     */
    public Bitmap getBitmapFromFrame(int width, int height) {
        FramePacket packet = nextFrame();
        if (packet != null) {
            return decodeFrameToBitmap(packet.frame, width, height);
        }
        return null;
    }

    /**
     * Decodes a H264 frame into a Bitmap using MediaCodec.
     */
    private Bitmap decodeFrameToBitmap(byte[] frameData, int width, int height) {
        try {
            MediaFormat format = MediaFormat.createVideoFormat(MediaFormat.MIMETYPE_VIDEO_AVC, width, height);
            MediaCodec codec = MediaCodec.createDecoderByType(MediaFormat.MIMETYPE_VIDEO_AVC);

            codec.configure(format, null, null, 0);
            codec.start();

            int inputBufferIndex = codec.dequeueInputBuffer(10000);
            if (inputBufferIndex >= 0) {
                ByteBuffer inputBuffer = codec.getInputBuffer(inputBufferIndex);
                inputBuffer.clear();
                inputBuffer.put(frameData);
                codec.queueInputBuffer(inputBufferIndex, 0, frameData.length, 0, 0);
            } else {
                Log.e("FrameProviderH264", "No available input buffer");
                return null;
            }

            MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();
            int outputBufferIndex = codec.dequeueOutputBuffer(bufferInfo, 10000);
            while (outputBufferIndex >= 0) {
                // Get output buffer
                ByteBuffer outputBuffer = codec.getOutputBuffer(outputBufferIndex);

                // Get raw data from buffer
                byte[] yuvData = new byte[bufferInfo.size];
                outputBuffer.get(yuvData);

                // Convert YUV to Bitmap
                Bitmap bitmap = yuvToBitmap(yuvData, width, height);

                codec.releaseOutputBuffer(outputBufferIndex, false);
                codec.stop();
                codec.release();

                return bitmap;
            }

            codec.stop();
            codec.release();

        } catch (Exception e) {
            Log.e("FrameProviderH264", "Decoding error: " + e.getMessage());
            e.printStackTrace();
        }

        return null;
    }

    /**
     * Converts YUV data to a Bitmap.
     */
    private Bitmap yuvToBitmap(byte[] yuvData, int width, int height) {
        try {
            // Create a Bitmap from the YUV data
            // This method depends on the specific YUV format (e.g., NV21, YUV420P)
            // For simplicity, assume YUV420P format here

            int frameSize = width * height;
            int[] colors = new int[frameSize];

            for (int i = 0; i < height; i++) {
                for (int j = 0; j < width; j++) {
                    int y = yuvData[i * width + j] & 0xFF;
                    int u = yuvData[frameSize + (i / 2) * (width / 2) + (j / 2)] & 0xFF;
                    int v = yuvData[frameSize + (frameSize / 4) + (i / 2) * (width / 2) + (j / 2)] & 0xFF;

                    int r = y + (int) (1.370705 * (v - 128));
                    int g = y - (int) (0.698001 * (v - 128) + 0.337633 * (u - 128));
                    int b = y + (int) (1.732446 * (u - 128));

                    r = r < 0 ? 0 : (r > 255 ? 255 : r);
                    g = g < 0 ? 0 : (g > 255 ? 255 : g);
                    b = b < 0 ? 0 : (b > 255 ? 255 : b);

                    colors[i * width + j] = 0xFF000000 | (r << 16) | (g << 8) | b;
                }
            }

            return Bitmap.createBitmap(colors, width, height, Bitmap.Config.ARGB_8888);
        } catch (Exception e) {
            Log.e("FrameProviderH264", "YUV to Bitmap conversion error: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
}
