package com.movisat.wificamera;

import android.util.Base64;
import android.util.Log;

import com.movisat.utilities.TimeoutBlock;
import com.tutk.IOTC.AVAPIs;

import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Arrays;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

import static com.tutk.IOTC.AVAPIs.AV_ER_NoERROR;
import static com.tutk.IOTC.AVAPIs.AV_ER_REMOTE_TIMEOUT_DISCONNECT;
import static com.tutk.IOTC.AVAPIs.AV_ER_SENDIOCTRL_ALREADY_CALLED;
import static com.tutk.IOTC.AVAPIs.AV_ER_SESSION_CLOSE_BY_REMOTE;

/**
 * Created by dsanchez on 27/03/2018.
 */

public class CameraManager {

    private static final String TAG = "CAMERA_MANAGER";

    private static CameraManager instance;
    private static CameraManager instance2;

    private StreamClient mStreamClient; // StreamClient para la recepcción del vídeo/audio
    private FrameProviderH264 mFrameProvider; // Cola de frames recibidos en formato H264

    private String mUid = "";
    private String mPassword = "123456";
    private String mIp = "";

    private volatile boolean mConnected = false; // Indica el estado de conexión del StreamClient

    /**
     * Obtiene una instancia para manejar la cámara 1
     */
    public static CameraManager getInstance(String uid) {

        if (instance == null) {

            instance = new CameraManager();
            instance.setUID(uid);
        }

        return instance;
    }


    /**
     * Obtiene una instancia para manejar la cámara 2
     */
    public static CameraManager getInstance2(String uid) {

        if (instance2 == null) {

            instance2 = new CameraManager();
            instance2.setUID(uid);
        }

        return instance2;
    }


    /**
     * Desconecta la instancia actual.
     */
    public void disconnect() {

        try {

            disconnect(StreamClient.ExitCode.SUCCESS);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * Inicia el streaming de audio y/o vídeo.
     */
    public synchronized void startStreaming() {

        try {

            if (mStreamClient != null)
                mStreamClient.start();

        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    public boolean isStreamReady() {
        if (mStreamClient != null)
            return mStreamClient.isReady();
        return false;
    }

    public int getNumFrames() {
        if (mStreamClient != null)
            return mStreamClient.getNumFrames();
        return 0;
    }

    /**
     * Detiene la recepción de audio y vídeo.
     */
    public synchronized void stopStreaming() {

        try {

            if (mStreamClient != null)
                mStreamClient.stop();

        } catch (Throwable e) {
            e.printStackTrace();
        }
    }


    /**
     * Arranca el hilo para conectar con la cámara y gestionar los reintentos de conexión.
     */
    public boolean startClient() {

        try {

            if (mStreamClient == null)
                mStreamClient = new StreamClient(mUid, mPassword, getFrameProvider(), this);

            Thread threadConnect = new Thread() {
                @Override
                public void run() {
                    // Intenta conectar con la cámara
                    try {
                        mConnected = mStreamClient.connect(true, false, 10);
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                }
            };
            threadConnect.start();

            threadConnect.join(10000);
            if (threadConnect.isAlive())
                mStreamClient.close(StreamClient.ExitCode.ERROR_INIT);

        } catch (Throwable e) {
            e.printStackTrace();
        }

        return mConnected;
    }


    /**
     * Cierra la conexión.
     */
    private synchronized void disconnect(StreamClient.ExitCode exitCode) {

        try {

            if (mStreamClient != null)
                mStreamClient.close(exitCode);
            mStreamClient = null;

            mConnected = false;

        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    /**
     * Obtiene la instancia de FrameProviderH264, creando una si no existe.
     */
    public FrameProviderH264 getFrameProvider() {

        try {

            if (mFrameProvider == null)
                mFrameProvider = new FrameProviderH264(5, FrameProviderH264.DropFramesMode.DROP_OLD_FRAMES);

        } catch (Throwable e) {
            e.printStackTrace();
        }

        return mFrameProvider;
    }

    /**
     * Establece la instancia del FrameProvider.
     */
    public void setFrameProvider(FrameProviderH264 frameProvider) {

        try {

            mFrameProvider = frameProvider;
            if (mStreamClient != null)
                mStreamClient.setFrameProvider(mFrameProvider);

        } catch (
                Throwable e) {
            e.printStackTrace();
        }
    }

    /**
     * Obtiene el UID de la cámara Wifi.
     */
    public String getUID() {
        return mUid;
    }

    /**
     * Establece el UID de la cámara Wifi.
     */
    public void setUID(String uid) {
        this.mUid = uid;
    }

    /**
     * Obtiene la contraseña de la cámara Wifi.
     */
    public String getPassword() {
        return mPassword;
    }

    /**
     * Establece la contraseña de la cámara Wifi.
     */
    public void setPassword(String password) {
        this.mPassword = password;
    }

    /**
     * Obtiene la contraseña de la cámara Wifi codificada en base 64 (para uso con el servidor web).
     *
     * @return
     */
    private String getEncodedPassword() {
        String res = "";

        try {
            res = Base64.encodeToString(mPassword.getBytes(), Base64.NO_WRAP | Base64.URL_SAFE);
        } catch (Throwable e) {
            e.printStackTrace();
        }

        return res;
    }

    /**
     * Obtiene la IP de la cámara Wifi.
     */
    public String getIP() {
        return mIp;
    }

    /**
     * Establece la IP de la cámara Wifi.
     */
    public void setIP(String ip) {
        this.mIp = ip;
    }


    /**
     * Establece la fecha de la cámara Wifi mediante una petición al servidor web.
     *
     * @param time_ms Timestamp que se establecerá (en milisegundos).
     */
    public boolean setDatetime(final long time_ms) {

        try {

            if (mIp == null || mIp.length() == 0)
                return false;

            new Thread(new Runnable() {
                @Override
                public void run() {

                    HttpURLConnection urlConnection = null;

                    try {
                        String urlStr = String.format("http://%s:81/cgi-bin/set_datetime.cgi", mIp);

                        urlStr += "?next_url=datetime.htm";
                        urlStr += "&ntp_svr=time.nist.gov";
                        //urlStr += "&tz=-7200";
                        urlStr += "&tz=0"; // UTC
                        urlStr += "&ntp_enable=0";
                        urlStr += "&now=" + String.format("%.3f", time_ms / 1000.0).replace(',', '.');

                        URL url = new URL(urlStr);
                        urlConnection = (HttpURLConnection) url.openConnection();
                        urlConnection.setDoOutput(true);
                        urlConnection.connect();
                        urlConnection.getOutputStream().close();

                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        if (urlConnection != null)
                            urlConnection.disconnect();
                    }
                }
            }).start();

        } catch (Throwable e) {
            e.printStackTrace();
        }

        return true;
    }

    /**
     * Reinicia la cámara Wifi mediante una petición al servidor web.
     */
    public boolean reboot() {

        try {

            if (mIp.length() == 0)
                return false;

            new Thread(new Runnable() {
                @Override
                public void run() {

                    HttpURLConnection urlConnection = null;

                    try {

                        String urlStr = String.format("http://%s:81/cgi-bin/reboot.cgi", mIp);

                        urlStr += "?next_url=reboot.htm";
                        urlStr += "&loginuser=admin";
                        urlStr += "&loginpass=" + getEncodedPassword();

                        URL url = new URL(urlStr);
                        urlConnection = (HttpURLConnection) url.openConnection();
                        int code = urlConnection.getResponseCode();

                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        if (urlConnection != null)
                            urlConnection.disconnect();
                    }
                }
            }).start();

        } catch (Throwable e) {
            e.printStackTrace();
        }

        return true;
    }


    /**
     * Envía a la cámara Wifi el código de solicitud y la información especificados y obtiene los
     * datos enviados por la cámara como respuesta.
     *
     * @param avIndex        ID del canal AV.
     * @param request_code   Código de solicitud de la operación (definidos en AVIOCTRLDEFs).
     * @param request_bytes  Array de bytes que se enviarán con la solicitud.
     * @param response_code  Código de respuesta de la operación (definidos en AVIOCTRLDEFs).
     * @param response_bytes Array de bytes recibidos con la repsuesta.
     * @param timeout_ms     Timeout de la función en milisegundos.
     * @param log            Indica si se desean mostrar los mensajes de depuración.
     * @return true si la respuesta a la solicitud se ha recibido correctamente, o false en otro caso.
     */
    public synchronized boolean sendRequest(final int avIndex,
                                            final int request_code, final byte[] request_bytes,
                                            final int response_code, final ByteArray response_bytes,
                                            long timeout_ms, final boolean log) {
        boolean success = false;

        try {
            success = TimeoutBlock.run(new Callable<Boolean>() {
                @Override
                public Boolean call() throws Exception {

                    // Se envía la solicitud a la cámara Wifi.
                    int ret = AVAPIs.avSendIOCtrl(avIndex, request_code, request_bytes, request_bytes.length);

                    if (ret == AV_ER_NoERROR) {
                        if (log)
                            log(String.format("avSendIOCtrl (0x%x) - OK", request_code));
                    } else {
                        if (log)
                            log(String.format("avSendIOCtrl (0x%x) - Error %d", request_code, ret));

                        switch (ret) {
                            case AV_ER_SENDIOCTRL_ALREADY_CALLED:
                                AVAPIs.avSendIOCtrlExit(avIndex);
                                Thread.sleep(500);
                                break;

                            case AV_ER_SESSION_CLOSE_BY_REMOTE:
                            case AV_ER_REMOTE_TIMEOUT_DISCONNECT:
                                // Si la conexión se ha cerrado, se desconecta el StreamClient
                                disconnect(StreamClient.ExitCode.CONNECTION_LOST);
                                break;
                        }

                        return false;
                    }

                    // Este tiempo debería ser suficiente para que la cámara haya recibido
                    // la solicitud y haya enviado la respuesta.
                    Thread.sleep(1000);

                    int[] pioType = new int[1];             // pioType[0] contendrá el código de respuesta
                    byte[] ioCtrlIBuf = new byte[1000];     // Datos recibidos en la respuesta

                    // Se recibe la respuesta de la cámara Wifi.
                    ret = AVAPIs.avRecvIOCtrl(avIndex, pioType, ioCtrlIBuf, 1500, 5000);
                    if (ret > 0) {
                        if (pioType[0] == response_code) {
                            if (log) log(String.format("avRecvIOCtrl (0x%x) - OK", response_code));
                            if (response_bytes != null)
                                response_bytes.setArray(Arrays.copyOf(ioCtrlIBuf, ret));
                            return true;
                        }
                    } else {
                        if (log)
                            log(String.format("avRecvIOCtrl (0x%x) - Error %d", response_code, ret));
                    }

                    return false;
                }
            }, timeout_ms, TimeUnit.MILLISECONDS);

        } catch (Exception e) {
            if (log)
                log(String.format("sendRequest (0x%x) - TIMEOUT", request_code));
        }

        return success;
    }


    private void log(String message) {
        Log.i(TAG, message);
    }


    private static class ByteArray {
        byte[] array = null;

        public void setArray(byte[] array) {
            this.array = array;
        }

        public byte[] getArray() {
            return array;
        }
    }

}
