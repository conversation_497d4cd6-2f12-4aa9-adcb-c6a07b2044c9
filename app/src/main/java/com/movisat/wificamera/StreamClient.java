package com.movisat.wificamera;

import static com.tutk.IOTC.AVIOCTRLDEFs.IOTYPE_USER_IPCAM_AUDIOSTART;
import static com.tutk.IOTC.AVIOCTRLDEFs.IOTYPE_USER_IPCAM_AUDIOSTOP;
import static com.tutk.IOTC.AVIOCTRLDEFs.IOTYPE_USER_IPCAM_START;
import static com.tutk.IOTC.AVIOCTRLDEFs.IOTYPE_USER_IPCAM_STOP;

import android.util.Log;

import com.movisat.utilities.TimeoutBlock;
import com.tutk.IOTC.AVAPIs;
import com.tutk.IOTC.IOTCAPIs;
import com.tutk.IOTC.St_SInfo;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

public class StreamClient {

    private static final String TAG = "STREAM_CLIENT";

    private static final int SEND_IOCTRL_TIMEOUT_MS = 2000;

    private CameraManager cameraManager = null;

    private String mUID;
    private String mPassword;
    private int mSID;
    public int mAvIndex;

    private FrameProviderH264 mFrameProvider;   // Cola de frames recibidos en formato H264

    private boolean mVideoEnabled;
    private boolean mAudioEnabled;

    private Thread mVideoThread;
    private Thread mAudioThread;
    private VideoThread mVideoRunnable;
    private AudioThread mAudioRunnable;

    private volatile int numFrames = 0;

    public enum ExitCode {
        SUCCESS,                // Salida programada
        ERROR_INIT,             // Error en la inicialización de los módulos o conexión con la cámara
        ERROR_INIT_STREAMING,   // Error inicializando streaming
        CANCELLED,              //
        CONNECTION_LOST,
        NONE
    }

    public enum ConnectionStatus {CONNECTED, DISCONNECTED, CONNECTING}

    private OnConnectionChangedListener mOnConnectionChangedListener;

    private boolean mStopped;           // Indica si el streaming está detenido
    private static boolean mClosed;     // Indica si los módulos no están inicializados

    private volatile ConnectionStatus mConnectionStatus;


    /**
     * Crea un cliente para la recepción de un streaming de audio/vídeo.
     *
     * @param uid           UID de la cámara Wifi.
     * @param password      Contraseña de la cámara Wifi.
     * @param frameProvider FrameProvider en el que se guardarán los frames de vídeo obtenidos.
     */
    public StreamClient(String uid, String password, FrameProviderH264 frameProvider, CameraManager cameraManager) {
        this.cameraManager = cameraManager;
        mUID = uid;
        mPassword = password;
        mFrameProvider = frameProvider;
        mStopped = true;
        mClosed = true;
        mConnectionStatus = ConnectionStatus.DISCONNECTED;
    }

    // Devuelve true si se han recibido más de X frames de video
    public boolean isReady() {
        return numFrames >= 30;
    }

    // Devuelve el número de frames de video recibidos hasta el momento
    public int getNumFrames() {
        return numFrames;
    }

    /**
     * Inicia la conexión con la cámara Wifi.
     *
     * @param videoEnabled Si es true, inicia el streaming de vídeo.
     * @param audioEnabled Si es true, inicia el streaming de audio.
     * @param timeout_sec  Timeout en segundos para la conexión (0 para realizar un solo intento).
     * @return true si la conexión se ha realizado, false en caso contrario.
     */
    public boolean connect(boolean videoEnabled, boolean audioEnabled, int timeout_sec) {

        mVideoEnabled = videoEnabled;
        mAudioEnabled = audioEnabled;

        Log.i(TAG, "connect() - Conectando StreamClient...");

        mConnectionStatus = ConnectionStatus.CONNECTING;

        mClosed = false;

        int ret = IOTCAPIs.IOTC_Initialize2(0);
        Log.i(TAG, String.format("IOTC_Initialize() ret = %d", ret));
        if (ret != IOTCAPIs.IOTC_ER_NoERROR && ret != IOTCAPIs.IOTC_ER_ALREADY_INITIALIZED) {
            Log.i(TAG, "IOTCAPIs_Device exit...!");
            cancel(ExitCode.ERROR_INIT);
            return false;
        }

        // Se establece un canal para el vídeo y dos para el audio
        AVAPIs.avInitialize(audioEnabled ? 6 : 2); // 2 canales para vídeo, 2 para audio

        // Se obtiene el identificador de la sesión
        mSID = IOTCAPIs.IOTC_Get_SessionID();
        if (mSID < 0) {
            Log.i(TAG, String.format("IOTC_Get_SessionID error code [%d]", mSID));
            cancel(ExitCode.ERROR_INIT);
            return false;
        }

        // Se conecta con el dispositivo y se asocia la conexión a una sesión
        Log.i(TAG, "connect() - IOTC_Connect_ByUID_Parallel...");
        ret = IOTCAPIs.IOTC_Connect_ByUID_Parallel(mUID, mSID);
        Log.i(TAG, String.format("IOTC_Connect_ByUID_Parallel ret=(%s)", mUID));
        if (ret < 0) {
            Log.i(TAG, String.format("IOTC_Connect_ByUID_Parallel failed[%d]", ret));
            cancel(ExitCode.ERROR_INIT);
            return false;
        }

        // Se inicia la conexión con el cliente
        final int[] srvType = new int[1];
        Log.i(TAG, "connect() - avClientStart...");
        mAvIndex = AVAPIs.avClientStart(mSID, "admin", mPassword, timeout_sec, srvType, 0);
        Log.i(TAG, String.format("avClientStart ret=(%d)", mAvIndex));
        if (mAvIndex < 0) {
            Log.i(TAG, String.format("avClientStart failed[%d]", mAvIndex));
            cancel(ExitCode.ERROR_INIT);
            return false;
        }

        // Se obtiene la IP de la cámara
        St_SInfo sessionInfo = new St_SInfo();
        IOTCAPIs.IOTC_Session_Check(mSID, sessionInfo);
        if (sessionInfo != null && sessionInfo.RemoteIP != null) {
            Log.i("CAMARA", "IP: " + sessionInfo.RemoteIP);
            if (new String(sessionInfo.RemoteIP).split("\0").length > 0) {
                String cameraIP = new String(sessionInfo.RemoteIP).split("\0")[0];
                cameraManager.setIP(cameraIP);
                Log.i(TAG, "IP: " + cameraIP);
            }
        }

        // Se sincroniza la hora de la cámara con la del equipo
        cameraManager.setDatetime(System.currentTimeMillis());

        mConnectionStatus = ConnectionStatus.CONNECTED;

        return true;
    }


    /**
     * Inicia el streaming de audio y/o vídeo.
     */
    public void start() {

        // Se inician los threads para la recepción de vídeo y audio
        if (startIpcamStream()) {

            if (mVideoEnabled) {
                mVideoRunnable = new VideoThread(mAvIndex, mFrameProvider);
                mVideoThread = new Thread(mVideoRunnable, "Video Thread");
                mVideoThread.start();
            }

            if (mAudioEnabled) {
                mAudioRunnable = new AudioThread(mAvIndex);
                mAudioThread = new Thread(mAudioRunnable, "Audio Thread");
                mAudioThread.start();
            }

            mStopped = false;
        }
    }


    /**
     * Detiene la recepción de audio y vídeo.
     */
    public void stop() {

        if (mStopped)
            return;

        int ret;

        if (mVideoEnabled && mVideoRunnable != null)
            mVideoRunnable.stop();

        if (mAudioEnabled && mAudioRunnable != null)
            mAudioRunnable.stop();

        if (mVideoEnabled && mVideoThread != null && mVideoThread.isAlive()) {
            try {
                mVideoThread.join();

                // Se detiene el streaming de vídeo
                ret = avSendIOCtrl(mAvIndex, IOTYPE_USER_IPCAM_STOP, new byte[8], 8, SEND_IOCTRL_TIMEOUT_MS);
                if (ret < 0) Log.i(TAG, String.format("stop_ipcam_stream video failed[%d]", ret));

            } catch (Exception e) {
                System.out.println(e.getMessage());
                return;
            }
        }
        if (mAudioEnabled && mAudioThread != null && mAudioThread.isAlive()) {
            try {
                mAudioThread.join();

                // Se detiene el streaming de audio
                ret = avSendIOCtrl(mAvIndex, IOTYPE_USER_IPCAM_AUDIOSTOP, new byte[8], 8, SEND_IOCTRL_TIMEOUT_MS);
                if (ret < 0) Log.i(TAG, String.format("stop_ipcam_stream audio failed[%d]", ret));

            } catch (Exception e) {
                System.out.println(e.getMessage());
                return;
            }
        }

        mFrameProvider.clear();

        Log.i(TAG, "StreamClient exit...");
        mStopped = true;
    }


    /**
     * Cancela un intento de conexión, indicándo el código de salida en el callback OnFinished.
     */
    public void cancel(ExitCode exitCode) {
        Log.i(TAG, "StreamClient cancelled. Exiting...");
        mStopped = true;

        // Si el error es al iniciar el streaming, no se marca la conexión como desconectada
        if (exitCode != ExitCode.ERROR_INIT_STREAMING)
            mConnectionStatus = ConnectionStatus.DISCONNECTED;

        if (mOnConnectionChangedListener != null) {
            mOnConnectionChangedListener.connectionChanged(mConnectionStatus, exitCode);
        }
    }


    /**
     * Cierra la conexión con la cámara Wifi.
     *
     * @param exitCode Código de salida (motivo de la parada del módulo).
     */
    public synchronized void close(ExitCode exitCode) {

        if (mClosed) {
            Log.i(TAG, "close() - Ya está cerrado");
            return;
        }

        // Si no se ha perdido la conexión, no tiene sentido intentar cerrar el streaming
        if (exitCode != ExitCode.CONNECTION_LOST)
            stop();

        try {
            AVAPIs.avClientStop(mAvIndex);
            Log.i(TAG, "avClientStop OK");
            IOTCAPIs.IOTC_Session_Close(mSID);
            Log.i(TAG, "IOTC_Session_Close OK");
            AVAPIs.avDeInitialize();
            Log.i(TAG, "avDeInitialize OK");
            IOTCAPIs.IOTC_DeInitialize();
            Log.i(TAG, "IOTC_DeInitialize OK");
        } catch (UnsatisfiedLinkError ule) {
            Log.i(TAG, "UnsatisfiedLinkError");
        } catch (Exception e) {
            Log.i(TAG, "Exception");
        }

        mClosed = true;
        mConnectionStatus = ConnectionStatus.DISCONNECTED;

        if (mOnConnectionChangedListener != null)
            mOnConnectionChangedListener.connectionChanged(mConnectionStatus, exitCode);
    }


    /**
     * Manda a la cámara los comandos para iniciar el streaming de vídeo/audio.
     *
     * @return true si ha ido bien, false si no se han podido mandar los comandos a la cámara.
     */
    private boolean startIpcamStream() {

        int ret = avSendIOCtrl(mAvIndex, AVAPIs.IOTYPE_INNER_SND_DATA_DELAY, new byte[2], 2, SEND_IOCTRL_TIMEOUT_MS);
        if (ret < 0) {
            Log.i(TAG, String.format("start_ipcam_stream failed[%d]", ret));
            cancel(ExitCode.ERROR_INIT_STREAMING);
            return false;
        }

        // Se inicia el streaming de vídeo y de audio
        if (mVideoEnabled) {
            ret = avSendIOCtrl(mAvIndex, IOTYPE_USER_IPCAM_START, new byte[8], 8, SEND_IOCTRL_TIMEOUT_MS);
            if (ret < 0) {
                Log.i(TAG, String.format("start_ipcam_stream video failed[%d]", ret));
                cancel(ExitCode.ERROR_INIT_STREAMING);
                return false;
            }
        }

        if (mAudioEnabled) {
            ret = avSendIOCtrl(mAvIndex, IOTYPE_USER_IPCAM_AUDIOSTART, new byte[8], 8, SEND_IOCTRL_TIMEOUT_MS);
            if (ret < 0) {
                Log.i(TAG, String.format("start_ipcam_stream audio failed[%d]", ret));
                cancel(ExitCode.ERROR_INIT_STREAMING);
                return false;
            }
        }

        return true;
    }


    /**
     * Envía un comando con la función AVAPIs.avSendIOCtrl() con timeout.
     *
     * @param avIndex
     * @param ioType
     * @param ioCtrlBuf
     * @param ioCtrlBufSize
     * @param timeout       Timeout en milisegundos.
     * @return Valor de AVAPIs.avSendIOCtrl() si ha ido bien, -1 si se supera el tiempo.
     */
    private static synchronized int avSendIOCtrl(final int avIndex, final int ioType, final byte[] ioCtrlBuf,
                                                 final int ioCtrlBufSize, long timeout) {
        int ret = -1;

        try {
            ret = TimeoutBlock.run(new Callable<Integer>() {
                @Override
                public Integer call() throws Exception {
                    return AVAPIs.avSendIOCtrl(avIndex, ioType, ioCtrlBuf, ioCtrlBufSize);
                }
            }, timeout, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            Log.i(TAG, "avSendIOCtrl() - TIMEOUT (ioType=" + ioType + ")");
        }

        return ret;
    }


    /**
     * Indica si la recepción del streaming de audio/vídeo está detenida.
     */
    public boolean isStopped() {
        return mStopped;
    }


    /**
     * Obtiene el FrameProvider en el que se guardan los frames recibidos en el streaming.
     */
    public FrameProviderH264 getFrameProvider() {
        return mFrameProvider;
    }


    /**
     * Establece el FrameProvider en el que se guardarán los frames recibido en el streaming.
     */
    public void setFrameProvider(FrameProviderH264 mFrameProvider) {
        this.mFrameProvider = mFrameProvider;
    }


    /**
     * Establece la función callback que se ejecutará al cambiar el estado de la conexión.
     */
    public void setOnConnectionChangedListener(OnConnectionChangedListener onConnectionChangedListener) {
        mOnConnectionChangedListener = onConnectionChangedListener;
    }


    /**
     * Interfaz para recibir los cambios de conexión de StreamClient.
     */
    public interface OnConnectionChangedListener {
        /**
         * Se ejecuta cuando cambia el estado de conexión del StreamClient.
         *
         * @param status   Estado de conexión (CONNECTED, DISCONNECTED, CONNECTING).
         * @param exitCode Código de salida de la instancia (si status == DISCONNECTED).
         */
        void connectionChanged(ConnectionStatus status, ExitCode exitCode);
    }


    /**
     * Realiza la recepción del streaming de vídeo.
     */
    public class VideoThread implements Runnable {
        static final int VIDEO_BUF_SIZE = 1024 * 1000;
        static final int FRAME_INFO_SIZE = 16;

        private int avIndex;
        private boolean mStop = false;
        private FrameProviderH264 mFrameProvider;

        /**
         * @param frameProvider FrameProvider en el que se guardarán los frames de vídeo obtenidos.
         */
        public VideoThread(int avIndex, FrameProviderH264 frameProvider) {
            this.avIndex = avIndex;
            mFrameProvider = frameProvider;
        }

        @Override
        public void run() {
//            Log.i(TAG, String.format("[%s] Start", Thread.currentThread().getName()));
            byte[] frameInfo = new byte[FRAME_INFO_SIZE];
            byte[] videoBuffer = new byte[VIDEO_BUF_SIZE];
            int[] outBufSize = new int[1];
            int[] outFrameSize = new int[1];
            int[] outFrmInfoBufSize = new int[1];
            int[] frameNumber = new int[1];

            while (!mStop) {

                int ret = AVAPIs.avRecvFrameData2(avIndex, videoBuffer,
                        VIDEO_BUF_SIZE, outBufSize, outFrameSize, frameInfo, FRAME_INFO_SIZE,
                        outFrmInfoBufSize, frameNumber);

                if (ret == AVAPIs.AV_ER_DATA_NOREADY) {

                    try {
                        Thread.sleep(30);
                        continue;
                    } catch (InterruptedException e) {
//                        System.out.println(e.getMessage());
                        break;
                    }

                } else if (ret == AVAPIs.AV_ER_LOSED_THIS_FRAME) {
//                    Log.i(TAG, String.format("[%s] Lost video frame number[%d]", Thread.currentThread().getName(), frameNumber[0]));
                    continue;
                } else if (ret == AVAPIs.AV_ER_INCOMPLETE_FRAME) {
//                    Log.i(TAG, String.format("[%s] Incomplete video frame number[%d]", Thread.currentThread().getName(), frameNumber[0]));
                    continue;
                } else if (ret == AVAPIs.AV_ER_SESSION_CLOSE_BY_REMOTE) {
//                    Log.i(TAG, String.format("[%s] AV_ER_SESSION_CLOSE_BY_REMOTE", Thread.currentThread().getName()));
                    break;
                } else if (ret == AVAPIs.AV_ER_REMOTE_TIMEOUT_DISCONNECT) {
//                    Log.i(TAG, String.format("[%s] AV_ER_REMOTE_TIMEOUT_DISCONNECT", Thread.currentThread().getName()));
                    break;
                } else if (ret == AVAPIs.AV_ER_INVALID_SID) {
//                    Log.i(TAG, String.format("[%s] Session cant be used anymore", Thread.currentThread().getName()));
                    break;
                }

                numFrames++;

                // Now the data is ready in videoBuffer[0 ... ret - 1]
                mFrameProvider.putFrame(frameInfo, outFrmInfoBufSize[0], videoBuffer, outFrameSize[0]);
            }

//            Log.i(TAG, String.format("[%s] Exit", Thread.currentThread().getName()));
        }

        /**
         * Detiene la recepción de vídeo.
         */
        public void stop() {

            mStop = true;
        }
    }

    /**
     * Realiza la recepción del streaming de audio.
     */
    public class AudioThread implements Runnable {
        static final int AUDIO_BUF_SIZE = 1024 * 1000;
        static final int FRAME_INFO_SIZE = 16;

        private int avIndex;
        private boolean mStop = false;

        public AudioThread(int avIndex) {
            this.avIndex = avIndex;
        }

        @Override
        public void run() {
//            Log.i(TAG, String.format("[%s] Start", Thread.currentThread().getName()));

            byte[] frameInfo = new byte[FRAME_INFO_SIZE];
            byte[] audioBuffer = new byte[AUDIO_BUF_SIZE];

            while (!mStop) {
                int ret = AVAPIs.avCheckAudioBuf(avIndex);

                if (ret < 0) {
                    // Same error codes as below
//                    Log.i(TAG, String.format("[%s] avCheckAudioBuf() failed: %d", Thread.currentThread().getName(), ret));
                    break;
                } else if (ret < 3) {
                    try {
                        Thread.sleep(120);
                        continue;
                    } catch (InterruptedException e) {
//                        System.out.println(e.getMessage());
                        break;
                    }
                }

                int[] frameNumber = new int[1];
                ret = AVAPIs.avRecvAudioData(avIndex, audioBuffer, AUDIO_BUF_SIZE, frameInfo, FRAME_INFO_SIZE, frameNumber);

                if (ret == AVAPIs.AV_ER_SESSION_CLOSE_BY_REMOTE) {
//                    Log.i(TAG, String.format("[%s] AV_ER_SESSION_CLOSE_BY_REMOTE", Thread.currentThread().getName()));
                    break;
                } else if (ret == AVAPIs.AV_ER_REMOTE_TIMEOUT_DISCONNECT) {
//                    Log.i(TAG, String.format("[%s] AV_ER_REMOTE_TIMEOUT_DISCONNECT", Thread.currentThread().getName()));
                    break;
                } else if (ret == AVAPIs.AV_ER_INVALID_SID) {
//                    Log.i(TAG, String.format("[%s] Session cant be used anymore", Thread.currentThread().getName()));
                    break;
                } else if (ret == AVAPIs.AV_ER_LOSED_THIS_FRAME) {
//                    Log.i(TAG, String.format("[%s] Audio frame losed", Thread.currentThread().getName()));
                    continue;
                }

                // Now the data is ready in audioBuffer[0 ... ret - 1]
                // Do something here
            }

//            Log.i(TAG, String.format("[%s] Exit", Thread.currentThread().getName()));
        }

        /**
         * Detiene la recepción de audio.
         */
        public void stop() {

            mStop = true;
        }
    }
}
