package com.movisat.utilities;

import android.Manifest;
import android.app.Activity;
import android.app.ActivityManager;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.app.admin.DevicePolicyManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;
import android.view.View;

import com.movisat.receivers.AdminReceiver;
import com.movisat.activities.BuildConfig;

import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserFactory;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;

import static android.content.Context.DEVICE_POLICY_SERVICE;

/**
 * Created by dsanchez on 05/02/2018.
 */

public class AdminUtils {
    private static final int ACTIVE_ADMIN = 0;
    private static final int DEVICE_OWNER = 1;
    private static boolean kioskActive = false;

    /**
     * Establece la aplicación como administrador activo.
     *
     * @param context Contexto de la aplicación.
     * @return True si la aplicación se estableció como active-admin, False en otro caso.
     */
    public static boolean setActiveAdmin(Context context) {

        return execDpmCommand(context, ACTIVE_ADMIN);
    }

    /**
     * Desactiva la aplicación como device-owner.
     *
     * @param context Contexto de la aplicación.
     * @return True si la aplicación se desactivó como active-admin, False en otro caso.
     */
    public static boolean removeActiveAdmin(Context context) {

        try {
            ComponentName devAdminReceiver = new ComponentName(context, AdminReceiver.class);
            DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(DEVICE_POLICY_SERVICE);
            dpm.removeActiveAdmin(devAdminReceiver);
            return true;
        } catch (Exception e) {
            debug(e.getMessage());
        }
        return false;
    }

    /**
     * Establece la aplicación como device-owner.
     *
     * @param context Contexto de la aplicación.
     * @return True si la aplicación se estableció como device-owner, False en otro caso.
     */
    public static boolean setDeviceOwner(Context context) {

        return execDpmCommand(context, DEVICE_OWNER);
    }

    /**
     * Desactiva la aplicación como device-owner.
     *
     * @param context Contexto de la aplicación.
     * @return True si la aplicación se desactivó como device-owner, False en otro caso.
     */
    public static boolean removeDeviceOwner(Context context) {

        if (android.os.Build.VERSION.SDK_INT >= 21) {
            try {
                DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(DEVICE_POLICY_SERVICE);
                dpm.clearDeviceOwnerApp(context.getPackageName());
                return true;
            } catch (Exception e) {
                debug(e.getMessage());
            }
        }
        return false;
    }


    /**
     * Establece una lista de paquetes que pueden entrar en el modo Lock Task.
     * Esta función solo puede ser utilizada por un device-owner.
     *
     * @param context  Contexto de la aplicación.
     * @param packages Lista de nombres de paquete de las aplicaciones.
     * @return True si ha ido bien, False en otro caso.
     */
    public static boolean setLockTaskPackages(Context context, String[] packages) {
        boolean res = false;

        try {

            if (android.os.Build.VERSION.SDK_INT >= 21) {

                DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(DEVICE_POLICY_SERVICE);
                ComponentName mDeviceAdminRcvr = new ComponentName(context, AdminReceiver.class);
                dpm.setLockTaskPackages(mDeviceAdminRcvr, packages);
                res = true;
            }

        } catch (Exception e) {

            debug(e.getMessage());
        }

        return res;
    }


    /**
     * Indica si la aplicación desde la que se realiza la llamada es device-owner.
     *
     * @param context Contexto de la aplicación.
     * @return True si la aplicación es device-owner, False en otro caso.
     */
    public static boolean isThisAppDeviceOwner(Context context) {
        return (getDeviceOwnerPackage().equals(context.getPackageName()) ? true : false);
    }


    /**
     * Obtiene el nombre del paquete establecido como device-owner.
     *
     * @return Nombre del paquete device-owner, o una cadena vacía (error o no hay device-owner).
     */
    public static String getDeviceOwnerPackage() {
        String ret = "";

        try {
            // Se lee el archivo utilizando comandos (es un archivo del sistema)
            Process process = Runtime.getRuntime().exec("su");
            DataOutputStream dos = new DataOutputStream(process.getOutputStream());
            dos.writeBytes("cat /data/system/device_owner.xml\n");
            dos.writeBytes("exit\n");
            dos.flush();
            dos.close();
            process.waitFor();

            // Se procesa la salida del comando para obtener el contenido del archivo
            if (process != null) {
                // Se crea el objeto para parsear el XML
                XmlPullParserFactory xmlFactoryObject = XmlPullParserFactory.newInstance();
                XmlPullParser myParser = xmlFactoryObject.newPullParser();
                myParser.setInput(process.getInputStream(), null);

                int event = myParser.getEventType();
                while (event != XmlPullParser.END_DOCUMENT) {
                    String name = myParser.getName();
                    if (event == XmlPullParser.END_TAG) {
                        if (name.equals("device-owner")) {
                            ret = myParser.getAttributeValue(null, "package");
                            return ret;
                        }
                    }
                    event = myParser.next();
                }
            }

        } catch (Exception e) {
            debug(e.getMessage());
        }

        return ret;
    }


    /**
     * Activa o desactiva el modo Lock Task, para bloquear la pantalla. Nota: La aplicación tiene
     * privilegios de administrador, de modo que el usuario no podrá salir de este modo.
     *
     * @param activity Activity que lanzará o parará el Lock Task Mode.
     * @param enabled  Estado del modo kiosko. True para entrar, False para salir.
     * @return True si la operación se completó con éxito, False en otro caso.
     */
    public static boolean setKioskModeEnabled(final Activity activity, boolean enabled) {
        // Para el modo debug no es necesario activar el modo kiosko
        if (enabled && BuildConfig.DEBUG) return false;
        if (activity == null) return false;
        if (Build.VERSION.SDK_INT >= 21) {
            try {

                if (enabled) {

                    // Se pone la aplicación en Lock Task Mode(kiosko)
                    activity.startLockTask();

                    activity.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            // Se oculta la Status Bar
                            View decorView = activity.getWindow().getDecorView();
                            int visibility = View.SYSTEM_UI_FLAG_FULLSCREEN // Ocultar Status Bar
                                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY; // Oculta la Status Bar tras un pequeño retraso, o si el usuario interactúa con la pantalla
                            decorView.setSystemUiVisibility(visibility);
                        }
                    });

                    setHomeButtonEnabled(false);

                    kioskActive = true;

                } else {
                    activity.stopLockTask();
                    kioskActive = false;
                }

                return true;

            } catch (Exception e) {
                debug(e.getMessage());
            }
        }

        return false;
    }

    public static boolean isKioskMode() {
        return kioskActive;
    }


    /**
     * Indica si la aplicación se encuentra en modo Lock Task (kiosko) o no.
     *
     * @param context Contexto de la aplicación.
     * @return True si la aplicación está en modo Lock Task, False si no lo está.
     */
    public static boolean isAppInKioskMode(Context context) {
        ActivityManager activityManager;

        activityManager = (ActivityManager)
                context.getSystemService(Context.ACTIVITY_SERVICE);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // For SDK version 23 and above.
            return activityManager.getLockTaskModeState()
                    != ActivityManager.LOCK_TASK_MODE_NONE;
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // When SDK version >= 21. This API is deprecated in 23.
            return activityManager.isInLockTaskMode();
        }

        return false;
    }

    /**
     * Ejecuta un comando para establecer la aplicación como administrador activo
     * ('dpm set-active-admin ...') o como device-owner ('dpm set-device-owner ...')
     * y devuelve el resultado.
     *
     * @param context
     * @return True si el comando se ha ejecutado con éxito, False en otro caso.
     */
    private static boolean execDpmCommand(Context context, int action) {
        boolean res = false;

        try {
            // Se obtiene el string que identifica al componente DeviceAdminReceiver
            String adminReceiverComponent = context.getPackageName() + "/" +
                    AdminReceiver.class.getCanonicalName();

            // Se utiliza el comando 'dpm' para establecer el paquete actual como
            // administrador o device-owner
            String dpmCommand;

            switch (action) {
                case ACTIVE_ADMIN:
                    dpmCommand = "dpm set-active-admin " + adminReceiverComponent + "\n";
                    break;

                case DEVICE_OWNER:
                    dpmCommand = "dpm set-device-owner " + adminReceiverComponent + "\n";
                    break;

                default:
                    return false;
            }

            //dpm set-device-owner com.movisat.ecosat/com.movisat.receivers.AdminReceiver

            Process process = Runtime.getRuntime().exec("su");
            if (process != null) {

                DataOutputStream os = new DataOutputStream(process.getOutputStream());
                BufferedReader in = new BufferedReader(new InputStreamReader(process.getInputStream()));
                os.writeBytes(dpmCommand);
                os.writeBytes("exit\n");
                os.flush();
                os.close();

                // A partir de la versión 7 de Android esto no funciona
                if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.M) {

                    process.waitFor();

                    // Se procesa la salida del comando para obtener el resultado del proceso
                    // Se lee solo la primera línea, que debería indicar el éxito del proceso
                    String line;
                    if ((line = in.readLine()) != null)
                        res = line.contains("Success");

                    process.destroy();

                } else
                    res = true;
            }

        } catch (Exception e) {

            debug(e.getMessage());
        }

        return false;
    }

    /**
     * Ejecuta el comando especificado como usuario root y espera a que el proceso termine.
     *
     * @param command Comando que se ejecutará.
     * @return Proceso finalizado del que puede utilizarse su InputStream para obtener
     * el resultado de la ejecución; null en otro caso.
     */
    public static boolean executeCommand(String command) {

        Process process = null;

        try {
            process = Runtime.getRuntime().exec("su");

            if (!command.endsWith("\n"))
                command += "\n";

            DataOutputStream dos = new DataOutputStream(process.getOutputStream());
            dos.writeBytes(command);
            dos.writeBytes("exit\n");
            dos.flush();
            dos.close();
            process.waitFor();

            int exitValue = process.exitValue();
            process.destroy();

            return exitValue == 0;
        } catch (Throwable e) {
            debug(e.getMessage());
        }

        return false;
    }

    public static String executeCommandWithResponse(String command) {
        StringBuilder output = new StringBuilder();
        try {
            Process process = Runtime.getRuntime().exec("su");
            DataOutputStream dos = new DataOutputStream(process.getOutputStream());
            if (!command.endsWith("\n"))
                command += "\n";
            dos.writeBytes(command);
            dos.writeBytes("exit\n");
            dos.flush();
            dos.close();

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
            reader.close();
            process.waitFor();
            process.destroy();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return output.toString();
    }


    /**
     * Habilita o deshabilita el botón de Home en el sistema.
     *
     * @param enabled true para habilitar el botón Home, false para deshabilitarlo.
     */
    public static void setHomeButtonEnabled(boolean enabled) {
        executeCommand("settings put secure user_setup_complete " + (enabled ? "1" : "0"));
    }

    private static void debug(String text) {

        Log.i("AdminUtils", text);
    }


    public static void restartApp(Context context, Class c) {
        Intent mStartActivity = new Intent(context, c);
        int mPendingIntentId = 123456;
        PendingIntent mPendingIntent = PendingIntent.getActivity(context, mPendingIntentId, mStartActivity, PendingIntent.FLAG_CANCEL_CURRENT);
        AlarmManager mgr = (AlarmManager) context.getSystemService(context.ALARM_SERVICE);
        mgr.set(AlarmManager.RTC, System.currentTimeMillis() + 100, mPendingIntent);
        System.exit(0);
    }

    public static void resetData() {
        executeCommand("pm clear com.movisat.ecosat");
    }

    /**
     * Otorga un permiso a una aplicación intentando todos los métodos posibles.
     *
     * @param permission  Permiso de Manifest.permission.
     */
    public static void grantPermission(String permission) {
        final String DRACO_PACKAGE = "com.movisat.draco";
        final String ECOSAT_PACKAGE = "com.movisat.ecosat";

        // 1. Intentar con pm grant
        boolean granted = executeCommand("pm grant " + ECOSAT_PACKAGE + " " + permission);
        if (granted) {
            System.out.println("Permiso otorgado con pm grant: " + permission);
            return;
        }

        // 2. Intentar con appops
        granted = executeCommand("appops set " + ECOSAT_PACKAGE + " " + permission + " allow");
        if (granted) {
            System.out.println("Permiso otorgado con appops: " + permission);
            return;
        }

        // 3. Si el permiso es WRITE_SETTINGS o SYSTEM_ALERT_WINDOW, usar settings
        if (permission.equals("WRITE_SETTINGS") || permission.equals("SYSTEM_ALERT_WINDOW")) {
            // Configurar ECOSAT con write_settings_package_name
            executeCommand("settings put system write_settings_package_name " + ECOSAT_PACKAGE);
            System.out.println("WRITE_SETTINGS asignado temporalmente a " + ECOSAT_PACKAGE);

            // Intentar otorgar el permiso con appops
            granted = executeCommand("appops set " + ECOSAT_PACKAGE + " " + permission + " allow");
            if (granted) {
                System.out.println("Permiso especial otorgado: " + permission);
            }

            // Revertir WRITE_SETTINGS a DRACO
//            executeCommand("settings put system write_settings_package_name " + DRACO_PACKAGE);
//            System.out.println("WRITE_SETTINGS restaurado a " + DRACO_PACKAGE);
        }

        if (!granted) {
            System.out.println("No se pudo otorgar el permiso: " + permission);
        }
    }
}
