package com.movisat.viewmodels.general;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.movisat.application.RutasApplication;
import com.movisat.serializers.JsonDateSerializer;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class ParametrosViewModel implements Serializable {

    public String imei;
    public int movil;
    public int idEmpresa;
    public int idUsuario;
    @JsonSerialize(using = JsonDateSerializer.class)
    public Date ini;
    @JsonSerialize(using = JsonDateSerializer.class)
    public Date fin;
    @JsonSerialize(using = JsonDateSerializer.class)
    public Date ultFechaMod;

    public Date password;
    public Date userName;
    public boolean inicio;
    public int paginado;
    public int pagina;
    public int totalPaginas;
    public String GUID;
    public List<Long> asignaciones;

    public List<Long> getAsignaciones() {
        return asignaciones;
    }

    public void setAsignaciones(List<Long> asignaciones) {
        this.asignaciones = asignaciones;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public int getMovil() {
        return movil;
    }

    public void setMovil(int movil) {
        this.movil = movil;
    }

    public int getIdEmpresa() {
        return idEmpresa;
    }

    public void setIdEmpresa(int idEmpresa) {
        this.idEmpresa = idEmpresa;
    }

    public int getIdUsuario() {
        return idUsuario;
    }

    public void setIdUsuario(int idUsuario) {
        this.idUsuario = idUsuario;
    }

    public Date getIni() {
        return ini;
    }

    public void setIni(Date ini) {
        this.ini = ini;
    }

    public Date getFin() {
        return fin;
    }

    public void setFin(Date fin) {
        this.fin = fin;
    }

    public Date getUltFechaMod() {
        return ultFechaMod;
    }

    public void setUltFechaMod(Date ultFechaMod) {
        this.ultFechaMod = ultFechaMod;
    }

    public Date getPassword() {
        return password;
    }

    public void setPassword(Date password) {
        this.password = password;
    }

    public Date getUserName() {
        return userName;
    }

    public void setUserName(Date userName) {
        this.userName = userName;
    }

    public boolean getInicio() {
        return inicio;
    }

    public void setInicio(boolean inicio) {
        this.inicio = inicio;
    }

}
