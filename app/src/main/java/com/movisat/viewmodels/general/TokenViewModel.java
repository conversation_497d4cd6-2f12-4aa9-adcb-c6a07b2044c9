package com.movisat.viewmodels.general;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TokenViewModel {
    private  String client_id;
    private  String access_token;
    private  String refresh_token;
    private  String token_type;
    private  String userName;
    private  int usuario_id;
    private  int empresa;
    private  int dias_visibles;
    private int expires_in;


    public String getClient_id() {
        return client_id;
    }

    public void setClient_id(String client_id) {
        this.client_id = client_id;
    }

    public String getAccess_token() {
        return access_token;
    }

    public void setAccess_token(String access_token) {
        this.access_token = access_token;
    }

    public String getRefresh_token() {
        return refresh_token;
    }

    public void setRefresh_token(String refresh_token) {
        this.refresh_token = refresh_token;
    }

    public String getToken_type() {
        return token_type;
    }

    public void setToken_type(String token_type) {
        this.token_type = token_type;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public int getUsuario_id() {
        return usuario_id;
    }

    public void setUsuario_id(int usuario_id) {
        this.usuario_id = usuario_id;
    }

    public int getEmpresa() {
        return empresa;
    }

    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }

    public int getDias_visibles() {
        return dias_visibles;
    }

    public void setDias_visibles(int dias_visibles) {
        this.dias_visibles = dias_visibles;
    }

    public int getExpires_in() {
        return expires_in;
    }

    public void setExpires_in(int expires_in) {
        this.expires_in = expires_in;
    }

    // TODO - Implementar el método isExpired
    private String expires;
    private String issued;

    // ".expires":"Mon, 19 Feb 2024 17:14:46 GMT"
    //".issued":"Mon, 19 Feb 2024 17:04:46 GMT"
    // "expires_in":599

    public boolean isExpired() {
        Date fechaExpiracion = parseDate(expires);
        Date fechaPeticion = parseDate(issued);
        if (fechaExpiracion != null && fechaPeticion != null) {
            long diferencia = fechaExpiracion.getTime() - fechaPeticion.getTime();
            long diferenciaSegundos = diferencia / 1000;
            System.out.println("Diferencia en segundos: " + diferenciaSegundos);
            return (int) (expires_in * 0.99) > diferenciaSegundos;
        }
        System.out.println("No se pudo calcular la fecha de expiración");
        return true;
    }

    private Date parseDate(String date) {
        if (date == null || date.isEmpty()) return null;

        SimpleDateFormat formato = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss zzz", Locale.ENGLISH);
        formato.setTimeZone(TimeZone.getTimeZone("GMT"));

        try {
            Date fecha = formato.parse(date);
            System.out.println("Fecha parseada: " + fecha);
            return fecha;
        } catch (ParseException e) {
            System.out.println("Error al parsear la fecha: " + e.getMessage());
            return null;
        }
    }
}
