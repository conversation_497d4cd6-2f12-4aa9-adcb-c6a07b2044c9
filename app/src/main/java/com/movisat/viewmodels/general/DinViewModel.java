package com.movisat.viewmodels.general;

import com.google.android.gms.maps.model.LatLng;

import java.util.Date;

// Entradas digitales
public class DinViewModel {
    public int id;
    public Date Fecha;
    public boolean valor;
    public LatLng posicion;
    public int entrada;

    public DinViewModel(int id, Date fecha, boolean valor, LatLng posicion, int entrada) {
        this.id = id;
        this.Fecha = fecha;
        this.valor = valor;
        this.posicion = posicion;
        this.entrada = entrada;
    }

    @Override
    public String toString() {
        return "DinViewModel{" +
              "id=" + id +
              ", Fecha=" + Fecha +
              ", valor=" + valor +
              ", posicion=" + posicion +
              ", entrada=" + entrada +
              '}';
    }

    public void setId(int id) {
        this.id = id;
    }
}
