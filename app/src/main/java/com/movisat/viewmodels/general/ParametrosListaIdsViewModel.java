package com.movisat.viewmodels.general;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.movisat.serializers.JsonDateSerializer;

import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ParametrosListaIdsViewModel {
    public List<Integer> IdsEnEquipo;

    @JsonSerialize(using = JsonDateSerializer.class)
    public Date UltimaFechaModificacion;
}
