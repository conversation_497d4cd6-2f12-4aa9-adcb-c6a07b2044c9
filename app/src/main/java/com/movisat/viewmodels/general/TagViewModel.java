package com.movisat.viewmodels.general;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TagViewModel implements Serializable{
    public  String Matricula;
    public  String Tag;
    public Date Fecha;

    public TagViewModel() {
    }

    public TagViewModel(String Matricula, String Tag) {
        this.Matricula = Matricula;
        this.Tag = Tag;
        this.Fecha = new Date();
    }

    public TagViewModel(String Matricula, String Tag, Date fecha) {
        this.Matricula = Matricula;
        this.Tag = Tag;
        this.Fecha = fecha;
    }
}
