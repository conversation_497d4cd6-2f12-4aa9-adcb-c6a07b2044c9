package com.movisat.viewmodels.general;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.movisat.models.rutas.ConfiguracionEcoRutasModel;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by avilla on 20/11/2015.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConfiguracionEcoRutasViewModel implements Serializable {

    public int Id;
    public int IdEmpresa;
    public String Nombre;
    public String Abreviatura;
    public String Descripcion;

    public Date FechaBaja;

    public Date FechaModificacion;

    public List<ConfiguracionEcoRutasModel> Detalle;
}
