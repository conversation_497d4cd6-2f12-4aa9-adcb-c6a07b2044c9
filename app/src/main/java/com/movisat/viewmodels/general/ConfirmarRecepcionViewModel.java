package com.movisat.viewmodels.general;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.movisat.serializers.JsonDateSerializer;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by avilla on 25/01/2017.
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class ConfirmarRecepcionViewModel implements Serializable{
    public String IMEI;
    public String Texto;

    @JsonSerialize(using = JsonDateSerializer.class)
    public Date Fecha;

    public ConfirmarRecepcionViewModel(){
       this(null, null, null);
    }
    public ConfirmarRecepcionViewModel(String imei, String texto, Date fecha)
    {
        this.IMEI = imei;
        this.Texto = texto;
        this.Fecha = fecha;
    }
}