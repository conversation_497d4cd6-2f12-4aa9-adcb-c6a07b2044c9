package com.movisat.viewmodels.trabajadores;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.movisat.serializers.JsonDateSerializer;

import java.io.Serializable;
import java.util.Date;


@JsonIgnoreProperties(ignoreUnknown = true)
public class DescansosHViewModel implements Serializable{
    
    public int Id;
    public int Empresa;
    @JsonSerialize(using = JsonDateSerializer.class)
    public Date Fecha;
    public int Movil;
    public int Descanso;
    public int Estado;
    public int Empleado;
}
