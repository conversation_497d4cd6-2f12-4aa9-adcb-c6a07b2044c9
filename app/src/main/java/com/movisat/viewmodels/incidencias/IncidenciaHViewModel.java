package com.movisat.viewmodels.incidencias;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.movisat.serializers.JsonDateSerializer;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
public class IncidenciaHViewModel implements Serializable{

    public int Id;

    public int IdEmpresa;

    public int Incidencia;

    public int Movil;

    public int Ruta;

    public int Tipo;

    public int Motivo;

    public int Elemento;

    public int TipoElem;

    public double Lat;

    public double Lng;

    public String Observaciones;

    @JsonSerialize(using = JsonDateSerializer.class)
    public Date Fecha;

    public ArrayList<byte[]> Imagenes;
}
