package com.movisat.viewmodels.rutas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RutasEnEquipoViewModel {

    private int Codigo;
    private int Empresa;
    private long IdAsignacion;

    public RutasEnEquipoViewModel(int codigo, int empresa, long idAsignacion)
    {
        Codigo = codigo;
        Empresa = empresa;
        IdAsignacion = idAsignacion;
    }

    public int getCodigo() {
        return Codigo;
    }

    public void setCodigo(int codigo) {
        Codigo = codigo;
    }

    public int getEmpresa() {
        return Empresa;
    }

    public void setEmpresa(int empresa) {
        Empresa = empresa;
    }

    public long getIdAsignacion() {
        return IdAsignacion;
    }

    public void setEmpresa(long idAsignacion) {
        IdAsignacion = idAsignacion;
    }
}
