package com.movisat.viewmodels.rutas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.movisat.serializers.JsonDateSerializer;

import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RutasEstadoViewModel implements Serializable {
    public int IdEmpresa;
    public int IdMovil;
    public int IdRuta;
    public double Lat;
    public double Lng;
    @JsonSerialize(using = JsonDateSerializer.class)
    public Date Fecha;
    public int Estado;
    public int IdRutaH;
    public long IdAsignacion;
    public String GuidRutaH;

    public RutasEstadoViewModel() {};

    public RutasEstadoViewModel(int idEmpresa, int idMovil, int idRuta,
            double latitud, double longitud, Date fecha, int estado, int idRutaH, long idAsignacion, String GuidRutaH) {
        this.IdEmpresa = idEmpresa;
        this.IdMovil = idMovil;
        this.IdRuta = idRuta;
        this.Lat = latitud;
        this.Lng = longitud;
        this.Fecha = fecha;
        this.Estado = estado;
        this.IdRutaH = idRutaH;
        this.IdAsignacion = idAsignacion;
        this.GuidRutaH = GuidRutaH;
    }

    public RutasEstadoViewModel clone() {
        return new RutasEstadoViewModel(IdEmpresa,IdMovil,IdRuta,Lat,Lng,Fecha,Estado,IdRutaH,IdAsignacion,GuidRutaH);
    }
}
