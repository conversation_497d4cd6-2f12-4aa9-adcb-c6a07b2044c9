package com.movisat.viewmodels.rutas;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.movisat.serializers.JsonDateSerializer;

import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ElementoProcesadoViewModel implements Serializable {
    public int IdEmpresa;
    public int IdRuta;
    public int IdRutaH;
    public int CodigoElemento;
    @JsonSerialize(using = JsonDateSerializer.class)
    public Date FechaProcesado;
    public double Lat;
    public double Lng;

    // Acera = 0
    // Elemento = 1
    // Linea = 2
    // Tramo = 3
    // Calle = 4
    // Incidencia = 5
    public int TipoElemento;
    public String GuidRutaH;

    public ElementoProcesadoViewModel(int idEmpresa, int idRuta, int idRutaH, int codigoElemento,
                Date fechaProcesado, double latitud, double longitud, int tipoElemento, String GuidRutaH) {
        this.IdEmpresa = idEmpresa;
        this.IdRuta = idRuta;
        this.IdRutaH = idRutaH;
        this.CodigoElemento = codigoElemento;
        this.FechaProcesado = fechaProcesado;
        this.Lat = latitud;
        this.Lng = longitud;
        this.TipoElemento = tipoElemento;
        this.GuidRutaH = GuidRutaH;
    }
}
