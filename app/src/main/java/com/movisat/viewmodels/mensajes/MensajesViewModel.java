package com.movisat.viewmodels.mensajes;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
public class MensajesViewModel implements Serializable{

    public int Codigo;

    public int IdEmpresa;

    public String Imei;

    public int CodigoMovil;

    public String Texto;

    public int Enviado;

    public Date Fecha;

    public int Leido;

    public int getCodigo() {
        return Codigo;
    }

    public void setCodigo(int codigo) {
        Codigo = codigo;
    }

    public int getIdEmpresa() {
        return IdEmpresa;
    }

    public void setIdEmpresa(int idEmpresa) {
        IdEmpresa = idEmpresa;
    }

    public String getImei() {
        return Imei;
    }

    public void setImei(String imei) {
        Imei = imei;
    }

    public int getCodigoMovil() {
        return CodigoMovil;
    }

    public void setCodigoMovil(int codigoMovil) {
        CodigoMovil = codigoMovil;
    }

    public String getTexto() {
        return Texto;
    }

    public void setTexto(String texto) {
        Texto = texto;
    }

    public int getEnviado() {
        return Enviado;
    }

    public void setEnviado(int enviado) {
        Enviado = enviado;
    }

    public Date getFecha() {
        return Fecha;
    }

    public void setFecha(Date fecha) {
        Fecha = fecha;
    }

    public int getLeido() {
        return Leido;
    }

    public void setLeido(int leido) {
        Leido = leido;
    }
}
