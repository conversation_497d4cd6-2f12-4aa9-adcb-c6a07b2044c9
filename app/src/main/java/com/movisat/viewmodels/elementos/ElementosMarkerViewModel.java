package com.movisat.viewmodels.elementos;

import com.google.android.gms.maps.model.Marker;
import com.movisat.models.elementos.ElementosModel;
import com.movisat.models.incidencias.IncidenciasRutaModel;

public class ElementosMarkerViewModel {
    public Marker marker;

    private ElementosModel elemento;
    private IncidenciasRutaModel incidencia;

    public ElementosMarkerViewModel(Marker marker, ElementosModel elemento) {
        this.marker = marker;
        this.elemento = elemento;
    }

    public ElementosMarkerViewModel() {
        marker = null;
        elemento = null;

    }

    // Indica si tiene un elemento o incidencia asignado.
    public boolean tieneObjetoAsignado() {
        if (elemento != null) return true;
        if (incidencia != null) return true;
        return false;
    }

    public boolean esDeElemento() {
        if (elemento != null) return true;
        return false;
    }

    public boolean esDeIncidencia() {
        if (incidencia != null) return true;
        return false;
    }

    public ElementosModel getElemento() {
        return elemento;
    }

    public IncidenciasRutaModel getIncidencia() {
        return incidencia;
    }

    public void setElemento(ElementosModel elemento) {
        this.incidencia = null;
        this.elemento = elemento;
    }

    public void setIncidencia(IncidenciasRutaModel incidencia) {
        this.elemento = null;
        this.incidencia = incidencia;
    }


    @Override
    public String toString() {
        if (esDeElemento()) return elemento.toString();
        if (esDeIncidencia()) return incidencia.toString();
        return "";
    }

}
