package com.movisat.viewmodels.elementos;

import androidx.annotation.NonNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.movisat.models.elementos.ElementosModel;
import com.movisat.models.incidencias.IncidenciasRutaModel;
import com.movisat.models.rutas.RutasElementosModel;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ElementosCercanosViewModel implements Comparable<ElementosCercanosViewModel>, Serializable {

    private RutasElementosModel elementoRuta;
    private ElementosModel elemento;
    private IncidenciasRutaModel incidencia;
    public float distancia;

    // Radio de busqueda de elementos cercanos para procesar
    public static final int RADIO_ELEMENTOS_DEFECTO = 30;
    public static final String CONF_RADIO_ELEMENTOS = "radioElementos";
    public static final String CONF_SENSOR_PROCESAR = "dracoSensor.sensorProcesar";

    // Número de elementos dentro del radio a partir de los cuales nos
    // permitirá procesar más de uno a la misma vez (0=No se permite procesar más de uno)
    public static final int ELEMENTOS_JUNTOS_DEFECTO = 0;
    public static final String CONF_ELEMENTOS_JUNTOS = "elementosJuntos";

    public static ElementosCercanosViewModel deElemento(RutasElementosModel elemRuta, ElementosModel elemento, float distancia) {
        return new ElementosCercanosViewModel(elemRuta, elemento, distancia, null);
    }

    public static ElementosCercanosViewModel deIncidencia(IncidenciasRutaModel incidencia, float distancia) {
        return new ElementosCercanosViewModel(null, null, distancia, incidencia);
    }

    private ElementosCercanosViewModel(RutasElementosModel elemRuta, ElementosModel elemento, float distancia, IncidenciasRutaModel incidencia) {
        this.elementoRuta = elemRuta;
        this.elemento = elemento;
        this.distancia = distancia;
        this.incidencia = incidencia;
    }

    public boolean esDeIncidencia() {
        return incidencia != null;
    }

    public boolean esDeElemento() {
        return elemento != null;
    }

    @Override
    public int compareTo(@NonNull ElementosCercanosViewModel another) {
        return Float.compare(distancia, another.distancia);
    }

    public RutasElementosModel getElementoRuta() {
        return elementoRuta;
    }

    public ElementosModel getElemento() {
        return elemento;
    }

    public IncidenciasRutaModel getIncidencia() {
        return incidencia;
    }
}
