package com.movisat.viewmodels.elementos;

import androidx.annotation.NonNull;

import com.movisat.models.elementos.ElementosModel;
import com.movisat.models.rutas.RutasElementosModel;

import java.io.Serializable;

public class ElementoProcesarAdapter implements Comparable, Serializable {
    public RutasElementosModel elementoRuta;
    public ElementosModel elemento;
    public float distancia;

    // Radio de busqueda de elementos cercanos para procesar
    public static final int RADIO_ELEMENTOS_DEFECTO = 30;
    public static final String CONF_RADIO_ELEMENTOS = "radioElementos";
    public static final String CONF_SENSOR_PROCESAR = "dracoSensor.sensorProcesar";

    // Número de elementos dentro del radio a partir de los cuales nos
    // permitirá procesar más de uno a la misma vez (0=No se permite procesar más de uno)
    public static final int ELEMENTOS_JUNTOS_DEFECTO = 0;
    public static final String CONF_ELEMENTOS_JUNTOS = "elementosJuntos";

    public ElementoProcesarAdapter(RutasElementosModel elemRuta, ElementosModel elemento, float distancia) {
        this.elementoRuta = elemRuta;
        this.elemento = elemento;
        this.distancia = distancia;
    }

    @Override
    public int compareTo(@NonNull Object another) {
        if(distancia > ((ElementoProcesarAdapter)another).distancia)
            return 1;
        if(distancia < ((ElementoProcesarAdapter)another).distancia)
            return -1;
        return 0;
    }
}
