package com.movisat.viewmodels.elementos;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.movisat.viewmodels.general.TagViewModel;
import com.movisat.viewmodels.mapa.DatosGeograficosViewModel;

import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ElementosViewModel {
    public DatosGeograficosViewModel DatosGeograficos;
    public TagViewModel Tag;
    public  int IdEmpresa;
    public  int Codigo;
    public  String Nombre;
    public  String Descripcion;
    public  int CodModelo;
    public  double Lng;
    public  double Lat;
    public  Date FechaBaja;
    public  Date FechaModificacion;

    public ElementosViewModel() {}
    public ElementosViewModel(int IdEmpresa, int Codigo,
                              String Nombre, String Descripcion,
                              int CodModelo, double Lng,
                              double Lat, Date FechaBaja,
                              Date FechaModificacion,
                              DatosGeograficosViewModel DatosGeograficos, TagViewModel Tag) {
        this.IdEmpresa = IdEmpresa;
        this.Codigo = Codigo;
        this.Nombre = Nombre;
        this.Descripcion = Descripcion;
        this.CodModelo = CodModelo;
        this.Lng = Lng;
        this.Lat = Lat;
        this.FechaBaja = FechaBaja;
        this.FechaModificacion = FechaModificacion;
        this.Tag = Tag;
        this.DatosGeograficos = DatosGeograficos;
    }
}
