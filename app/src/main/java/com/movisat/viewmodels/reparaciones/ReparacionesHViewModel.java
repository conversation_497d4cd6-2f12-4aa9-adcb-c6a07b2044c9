package com.movisat.viewmodels.reparaciones;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.movisat.serializers.JsonDateSerializer;

import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ReparacionesHViewModel implements Serializable{

    public int IdEmpresa;
    public int  CodigoMovil;
    public int  IdRuta;
    public int  IdRutaH;
    public int IdReparacion;
    public int CodigoElemento;
    public int  X;
    public int Y;

    @JsonSerialize(using = JsonDateSerializer.class)
    public Date Fecha;

    public String Observaciones;


    public ReparacionesHViewModel(){}

    public ReparacionesHViewModel(int idEmpresa, int codigoMovil, int idRuta, int idRutaH, int idReparacion,
                                  int codigoElemento, int x, int y, Date fecha, String observaciones) {
        IdEmpresa = idEmpresa;
        CodigoMovil = codigoMovil;
        IdRuta = idRuta;
        IdRutaH = idRutaH;
        IdReparacion = idReparacion;
        CodigoElemento = codigoElemento;
        X = x;
        Y = y;
        Fecha = fecha;
        Observaciones = observaciones;
    }
}
