package com.movisat.viewmodels.revisiones;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.movisat.serializers.JsonDateSerializer;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RevisionesViewModel implements Serializable {
    public int IdEmpresa;
    public int CodigoMovil;
    public int IdRevision;
    public int IdEmpleado;
    public String Observaciones = "";
    @JsonSerialize(using = JsonDateSerializer.class)
    public Date Fecha;
    public List<RevisionesDatosViewModel> Datos;

    public RevisionesViewModel(int idEmpresa, int idMovil, int idRevision, int idEmpleado,
                    Date fecha, String observaciones, List<RevisionesDatosViewModel> datos) {
        this.IdEmpresa = idEmpresa;
        this.CodigoMovil = idMovil;
        this.IdRevision = idRevision;
        this.IdEmpleado = idEmpleado;
        this.Fecha = fecha;
        this.Observaciones = observaciones != null ? observaciones : "";
        this.Datos = datos;
    }
}
