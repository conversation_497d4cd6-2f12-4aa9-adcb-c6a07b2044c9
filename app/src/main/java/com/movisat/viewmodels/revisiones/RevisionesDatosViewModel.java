package com.movisat.viewmodels.revisiones;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RevisionesDatosViewModel implements Serializable {
    public int IdAtributo;
    public String Dato;
    public String Observaciones = "";

    public RevisionesDatosViewModel(int idAtributo, String valor, String observaciones) {
        IdAtributo = idAtributo;
        Dato = valor;
        Observaciones = observaciones != null ? observaciones : "";
    }
}
