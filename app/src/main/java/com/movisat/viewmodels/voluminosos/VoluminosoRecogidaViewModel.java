package com.movisat.viewmodels.voluminosos;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.movisat.models.voluminosos.VoluminososModel;
import com.movisat.serializers.JsonDateSerializer;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
public class VoluminosoRecogidaViewModel implements Serializable {

    public int Tipo;
    public int Modelo;
    public int Empresa;
    public int Movil;
    @JsonSerialize(using = JsonDateSerializer.class)
    public Date Fecha;
    public double Latitud;
    public double Longitud;
    public String Observaciones;

    public static VoluminosoRecogidaViewModel desdeModelo(VoluminososModel model, int movil, double latitud, double longitud, String observaciones) {
        return new VoluminosoRecogidaViewModel(model.Tipo, model.Id,
                model.Empresa, movil, Calendar.getInstance().getTime(),
                latitud, longitud, observaciones);
    }

    public VoluminosoRecogidaViewModel(int tipo, int modelo, int empresa, int movil, Date fecha, double latitud, double longitud, String observaciones) {
        Tipo = tipo;
        Modelo = modelo;
        Empresa = empresa;
        Movil = movil;
        Fecha = fecha;
        Latitud = latitud;
        Longitud = longitud;
        Observaciones = observaciones;
    }

}
