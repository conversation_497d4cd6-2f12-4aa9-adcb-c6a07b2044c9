package com.movisat.viewmodels.mapa;


import android.location.Location;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PosicionGPSViewModel {
    public Location location;
    public double latitud;
    public double longitud;
    public float velocidad;
    public double altura;
    public float rumbo;
    public int numSatelites;
    public Date fecha;
    public boolean fakePosition;

    public PosicionGPSViewModel() {
    }

    public PosicionGPSViewModel(Location location, double latitud, double longitud, float velocidad,
                                double altura, float rumbo, int numSatelites,
                                Date fecha) {
        this.location = location;
        this.latitud = latitud;
        this.longitud = longitud;
        this.velocidad = velocidad;
        this.altura = altura;
        this.rumbo = rumbo;
        this.numSatelites = numSatelites;
        this.fecha = fecha;
        this.fakePosition = false;
    }
}
