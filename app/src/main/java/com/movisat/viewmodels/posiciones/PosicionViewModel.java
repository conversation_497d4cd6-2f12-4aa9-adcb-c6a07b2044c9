package com.movisat.viewmodels.posiciones;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.movisat.serializers.JsonDateSerializer;

import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PosicionViewModel implements Serializable {
	public double lat;
	public double lng;
	@JsonSerialize(using = JsonDateSerializer.class)
	public Date f;
	public float v;
	public double h;
	public double b;

	public PosicionViewModel(){};

	public PosicionViewModel(double lat, double lng, float velocidad, double altura,
			double rumbo, Date fecha) {
		this.lat = lat;
		this.lng = lng;
		this.v = velocidad;
		this.h = altura;
		this.b = rumbo;
		this.f = fecha;
	}
}
