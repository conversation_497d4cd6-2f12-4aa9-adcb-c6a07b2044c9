package com.movisat.viewmodels.sensores;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.movisat.serializers.JsonDateSerializer;

import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SensoresViewModel implements Serializable {
    public int Sensor;
    public int Movil;
    @JsonSerialize(using = JsonDateSerializer.class)
    public Date Fecha;
    public String Lectura;

    public SensoresViewModel(int idSensor, int codigoMovil, Date fecha, String lectura) {
        this.Sensor = idSensor;
        this.Movil = codigoMovil;
        this.Fecha = fecha;
        this.Lectura = lectura;
    }
}
