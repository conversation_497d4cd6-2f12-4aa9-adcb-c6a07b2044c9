package com.movisat.managers;

import com.jobqueue.JobManager;
import com.movisat.application.RutasApplication;
import com.movisat.dao.configuracion.ConfiguracionDAO;
import com.movisat.managers.log.LoggerManager;
import com.movisat.models.configuracion.ConfiguracionModel;

import java.util.List;


public class ConfiguracionManager {

    private static ConfiguracionManager instance = null;
    private static LoggerManager logger = LoggerManager.getInstance();
    private static JobManager jobManager = null;
    private static ConfiguracionDAO confDAO = new ConfiguracionDAO();

    public static synchronized ConfiguracionManager getInstance() {
        try {
            if (instance == null) {
                instance = new ConfiguracionManager();
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return instance;
    }

    private ConfiguracionManager() {
        try {
            jobManager = RutasApplication.getInstance().getJobManager();
            //logger = LoggerManager.getLogger();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Devuelve un modelo de configuracion a partir de su clave
     * @param clave
     * @return
     */
    public synchronized static ConfiguracionModel getConfBy(String clave)
    {
        try {
            ConfiguracionModel configuracionModel = confDAO.getConfBy(clave);
            return configuracionModel;
        }catch (Throwable e){
            logger.WriteError(e);
            return null;
        }
    }

    /**
     * Devuelve un modelo de configuracion a partir de su clave
     * si no existe devuelve el valor por defecto
     * @param clave
     * @param defValue
     */
    public synchronized static ConfiguracionModel getConfBy(String clave, String defValue)
    {
        ConfiguracionModel configuracionModel = ConfiguracionManager.getConfBy(clave);
        if (configuracionModel == null) {
            configuracionModel = new ConfiguracionModel(clave, defValue);
        }
        return configuracionModel;
    }

    /**
     * Guarda un valor de configuracion en BBDD
     * @param clave
     * @param valor
     * @return
     */
    public synchronized static ConfiguracionModel save(String clave, String valor)
    {
        try {
            ConfiguracionModel configuracionModel = confDAO.save(clave, valor);
            return configuracionModel;
        }catch (Throwable e){
            logger.WriteError(e);
            return null;
        }
    }

    /**
     * Guarda una lista de configuracion model
     * @param configuracionModel
     * @return
     */
    public synchronized static boolean save(List<ConfiguracionModel> configuracionModel)
    {
        try {
            return confDAO.save(configuracionModel);
        }catch (Throwable e){
            logger.WriteError(e);
            return false;
        }
    }

    /**
     * Elimina un valor de configuracion en BBDD
     *
     * @param clave
     * @return
     */
    public synchronized static void delete(String clave) {
        try {
            confDAO.delete(clave);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

}
