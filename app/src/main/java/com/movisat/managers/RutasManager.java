package com.movisat.managers;

import com.movisat.application.RutasApplication;
import com.movisat.dao.elementos.ElementosDAOProxy;
import com.movisat.dao.incidencias.IncidenciasRutaDAO;
import com.movisat.dao.rutas.RutasDAO;
import com.movisat.dao.rutas.RutasDAOProxy;
import com.movisat.dao.rutas.RutasElementosDAO;
import com.movisat.dao.rutas.RutasElementosDAOProxy;
import com.movisat.events.rutas.OnElementoProcesadoEvent;
import com.movisat.jobs.rutas.InsertarElementoProcesadoJob;
import com.movisat.jobs.rutas.InsertarEstadoRutaJob;
import com.movisat.listeners.gps.MyLocationListener;
import com.movisat.log.Logg;
import com.movisat.managers.log.LoggerManager;
import com.movisat.models.configuracion.ConfiguracionModel;
import com.movisat.models.elementos.ElementosModel;
import com.movisat.models.incidencias.IncidenciasRutaModel;
import com.movisat.models.rutas.RutasModel;
import com.movisat.viewmodels.mapa.PosicionGPSViewModel;
import com.movisat.viewmodels.rutas.ElementoProcesadoViewModel;
import com.movisat.viewmodels.rutas.RutasEstadoViewModel;

import java.util.Date;
import java.util.List;

import de.greenrobot.event.EventBus;

/**
 * Created by aortiz on 29/09/2015.
 */
public class RutasManager {
    private static final LoggerManager logger = LoggerManager.getInstance();
    private static final String TAG = "RutasManager";
    private static RutasManager instance = null;
    private static final int TIPO_ELEMENTO_PROCESADO = 1;
    private static final int TIPO_INCIDENCIA_PROCESADO = 5;
    private static final String CONF_ID_RUTAH = "conf-id-rutah";
    private static final RutasDAO rutasDAO = new RutasDAOProxy();
    private static RutasModel rutaSeleccionada;

    private RutasManager() {
    }

    public static synchronized RutasManager getInstance() {
        try {
            if (instance == null) {
                instance = new RutasManager();
            }
        } catch (Throwable e) {
            Logg.error(TAG, "[getInstance] " + e.getMessage());
        }
        return instance;
    }

    public synchronized boolean setInicioDeActividad(int codigoRuta, int idEmpresa, Date fecha) {
        boolean res = false;

        try {
            if (fecha == null) {
                Logg.error(TAG, "Fecha de inicio de actividad nula. Ruta: " + codigoRuta + ", Empresa: " + idEmpresa);
                fecha = new Date();
            }

            // Obtengo los datos de la ruta
            RutasModel ruta = rutasDAO.getRutaBy(codigoRuta, idEmpresa);

            if (ruta == null) return res;

            // Al iniciar actividad, limpiamos cualquier tag restante
            TagManager.getInstance().clear();
            Logg.info(TAG, "[setInicioDeActividad] Cola de tag limpia para ruta: " + codigoRuta);

            PosicionGPSViewModel posicion = MyLocationListener.getInstance().getUltimaPosicionGPS();

            // Cambio el estado de la ruta y pongo el IdRutaH a cero
            rutasDAO.setEstadoRuta(ruta.Codigo, ruta.Empresa, RutasModel.ESTADO_ACTIVIDAD_INICIADA, fecha);

            RutasApplication.getInstance().getJobManager().addJobInBackground(
                    new InsertarEstadoRutaJob(new RutasEstadoViewModel(
                            ruta.Empresa, ruta.CodMovil, ruta.Codigo,
                            posicion != null ? posicion.latitud : 0,
                            posicion != null ? posicion.longitud : 0, fecha,
                            RutasModel.ESTADO_ACTIVIDAD_INICIADA, 0, ruta.IdAsignacion, setUUID(ruta.Codigo))));

            res = true;
            logger.WriteInfo("Actividad iniciada. Empresa: " + ruta.Empresa + ", codigo: " + ruta.Codigo
                    + ", fecha: " + fecha + ", Estado actividad anterior: " + ruta.Estado + ", id asignación: " + ruta.IdAsignacion);
        } catch (Throwable e) {
            Logg.error(TAG, "[setInicioDeActividad] " + e.getMessage());
        }

        return res;
    }

    /**
     * Finaliza actividad y manda los datos al servidor
     *
     * @param codigoRuta Código de la ruta
     * @param idEmpresa  Id de la empresa
     * @return True si se ha finalizado la actividad
     */
    public synchronized boolean setFinDeActividad(int codigoRuta, int idEmpresa) {
        boolean res = false;

        try {
            // Obtengo los datos de la ruta
            RutasModel ruta = rutasDAO.getRutaBy(codigoRuta, idEmpresa);

            if (ruta == null) return res;

            Logg.info(TAG, "Finalizando actividad " + ruta.Codigo + " con estado " + ruta.Estado);
            // Compruebo si procede finalizar actividad
            if (ruta.Estado == RutasModel.ESTADO_RUTA_FINALIZADA) {
                // Cambio el estado de la ruta
                rutasDAO.setEstadoRuta(ruta.Codigo, ruta.Empresa, RutasModel.ESTADO_ACTIVIDAD_FINALIZADA, null);
            }

            // Al finalizar actividad, limpiamos cualquier tag restante
            TagManager.getInstance().clear();
            Logg.info(TAG, "[setFinDeActividad] Cola de tag limpia para ruta: " + codigoRuta);

            PosicionGPSViewModel posicion = MyLocationListener.getInstance().getUltimaPosicionGPS();

            // Este campo lo coge cuando envía el job
            // int idRutaH = 0; //rutasDAO.getIdRutaH(ruta.Codigo);
            // Si lo tenemos lo asignamos ya
            String uuid = getUUID(ruta.Codigo);
            if (uuid == null || uuid.isEmpty()) {
                Logg.error(TAG, "[setFinDeActividad] GuidRutaH vacío. No se puede enviar fin de actividad sin haber iniciado actividad.");
                return false;
            }
            int idRutaH = getIdRutaH(uuid);

            RutasApplication.getInstance().getJobManager().addJobInBackground(
                    new InsertarEstadoRutaJob(new RutasEstadoViewModel(
                            ruta.Empresa, ruta.CodMovil, ruta.Codigo,
                            posicion != null ? posicion.latitud : 0,
                            posicion != null ? posicion.longitud : 0, new Date(),
                            RutasModel.ESTADO_ACTIVIDAD_FINALIZADA, idRutaH, ruta.IdAsignacion, uuid)));

            res = true;
            logger.WriteInfo("Actividad finalizada. Empresa: " + ruta.Empresa + ", codigo: " + ruta.Codigo
                    + ", Estado anterior: " + ruta.Estado + ", id asignación: " + ruta.IdAsignacion);
        } catch (Throwable e) {
            Logg.error(TAG, "[setFinDeActividad] " + e.getMessage());
        }

        return res;
    }

    /**
     * Inicia ruta y manda los datos al servidor
     */
    public synchronized boolean setInicioDeRuta(int codigoRuta, int idEmpresa, Date fechaInicio) {
        boolean res = false;

        try {
            // Obtengo los datos de la ruta
            RutasModel ruta = rutasDAO.getRutaBy(codigoRuta, idEmpresa);

            if (ruta == null) return res;

            if (ruta.Estado == RutasModel.ESTADO_RUTA_INICIADA) {
                return true;
            }

            if (ruta.Estado != RutasModel.ESTADO_ACTIVIDAD_INICIADA) {
                Logg.error(TAG, "Actividad no iniciada " + ruta.Codigo);
                return false;
            }

            PosicionGPSViewModel posicion = MyLocationListener.getInstance().getUltimaPosicionGPS();

            // Cambio el estado de la ruta
            rutasDAO.setEstadoRuta(ruta.Codigo, ruta.Empresa, RutasModel.ESTADO_RUTA_INICIADA, null);

            // Este campo lo coge cuando envía el job
            // int idRutaH = 0; //rutasDAO.getIdRutaH(ruta.Codigo);
            // Si lo tenemos lo asignamos ya
            String uuid = getUUID(ruta.Codigo);
            if (uuid == null || uuid.isEmpty()) {
                Logg.error(TAG, "[setInicioDeRuta] GuidRutaH vacío. Inicie la actividad antes de iniciar ruta.");
                return false;
            }
            int idRutaH = getIdRutaH(uuid);

            RutasApplication.getInstance().getJobManager().addJobInBackground(
                    new InsertarEstadoRutaJob(new RutasEstadoViewModel(
                            ruta.Empresa, ruta.CodMovil,
                            ruta.Codigo, posicion != null ? posicion.latitud : 0,
                            posicion != null ? posicion.longitud : 0, fechaInicio,
                            RutasModel.ESTADO_RUTA_INICIADA, idRutaH, ruta.IdAsignacion, uuid)));
            res = true;
            logger.WriteInfo("Ruta iniciada. Empresa: " + ruta.Empresa + ", codigo: " + ruta.Codigo
                    + ", Estado actividad anterior: " + ruta.Estado + ", id asignación: " + ruta.IdAsignacion);
        } catch (Throwable e) {
            Logg.error(TAG, "[setInicioDeRuta] " + e.getMessage());
        }

        return res;
    }

    /**
     * Finaliza la ruta
     */
    public synchronized boolean setFinDeRuta(int codigoRuta, int idEmpresa) {
        boolean res = false;

        try {
            // Obtengo los datos de la ruta
            RutasModel ruta = rutasDAO.getRutaBy(codigoRuta, idEmpresa);

            if (ruta == null) return res;

            Logg.info(TAG, "Finalizando ruta " + ruta.Codigo + " con estado " + ruta.Estado);
            // Compruebo si procede finalizar ruta
            if (ruta.Estado == RutasModel.ESTADO_RUTA_INICIADA) {
                // Cambio el estado de la ruta
                rutasDAO.setEstadoRuta(ruta.Codigo, ruta.Empresa, RutasModel.ESTADO_RUTA_FINALIZADA, null);
            }

            PosicionGPSViewModel posicion = MyLocationListener.getInstance().getUltimaPosicionGPS();

            // Recupero la fecha del último elemento procesado para finalizar la ruta
            // con esa misma fecha, si no hay fecha cojo la fecha actual
            ConfiguracionModel configuracionModel =
                    ConfiguracionManager.getConfBy("rutas-ultima-fecha-elemento-procesado");

            // Quito el último elemento procesado para que no se pueda realizar
            // una descarga en vertedero hasta que se procese un elemento
            ConfiguracionManager.save("rutas-ultimo-elemento-procesado", "");

            Date fechaFinRuta;
            if (configuracionModel != null && !configuracionModel.valor.isEmpty()) {
                fechaFinRuta = new Date(Long.parseLong(configuracionModel.valor));
            } else {
                fechaFinRuta = new Date();
            }

            // Pongo todos los elementos de la ruta a "no procesados"
            rutasDAO.resetProcesadosRutaElementos(ruta.Codigo, ruta.Empresa);

            // Inicializo la fecha del último elemento procesado
            ConfiguracionManager.save("rutas-ultima-fecha-elemento-procesado", "");

            // Este campo lo coge cuando envía el job
            // int idRutaH = 0; //rutasDAO.getIdRutaH(ruta.Codigo);
            // Si lo tenemos lo asignamos ya
            String uuid = getUUID(ruta.Codigo);
            if (uuid == null || uuid.isEmpty()) {
                Logg.error(TAG, "[setFinDeRuta] GuidRutaH vacío. No se puede finalizar ruta sin haber iniciado actividad.");
                return false;
            }
            int idRutaH = getIdRutaH(uuid);

            RutasApplication.getInstance().getJobManager().addJobInBackground(
                    new InsertarEstadoRutaJob(new RutasEstadoViewModel(
                            ruta.Empresa, ruta.CodMovil,
                            ruta.Codigo, posicion != null ? posicion.latitud : 0,
                            posicion != null ? posicion.longitud : 0, fechaFinRuta,
                            RutasModel.ESTADO_RUTA_FINALIZADA, idRutaH, ruta.IdAsignacion, uuid)));

            res = true;
            logger.WriteInfo("Ruta finalizada. Empresa: " + ruta.Empresa + ", codigo: " + ruta.Codigo
                    + ", fecha última posición: " + fechaFinRuta + ", Estado actividad anterior: " + ruta.Estado + ", id asignación: " + ruta.IdAsignacion);
        } catch (Throwable e) {
            Logg.error(TAG, "[setFinDeRuta] " + e.getMessage());
        }

        return res;
    }

    /**
     * Cambia el estado del elemento a proceaado y envia al servidor el elemento en cuestión
     */
    public synchronized boolean setElementoProcesado(int codigoRuta, int idEmpresa, int idElemento,
                                                     Date fecha, boolean marcarProcesado,
                                                     boolean showMessage) {
        boolean res = false;
        try {
            // Obtengo los datos de la ruta
            RutasModel ruta = rutasDAO.getRutaBy(codigoRuta, idEmpresa);

            if (ruta != null) {

                // Inicio la ruta (si se trata del primer elemento procesado)
                setInicioDeRuta(codigoRuta, idEmpresa, fecha);

                // Compruebo si el elemento acaba de ser procesado hace menos de un minuto
                // para evitar que se procese dos veces el mismo elemento
                ConfiguracionModel cmFechaProcesado =
                        ConfiguracionManager.getConfBy("rutas-ultima-fecha-elemento-procesado");
                ConfiguracionModel cmIdElemento =
                        ConfiguracionManager.getConfBy("rutas-ultimo-elemento-procesado");

                if (cmFechaProcesado != null && !cmFechaProcesado.valor.isEmpty() &&
                        cmIdElemento != null && !cmIdElemento.valor.isEmpty()) {
                    boolean procesadoLessThanAMinuteAgo = Long.parseLong(cmFechaProcesado.valor) > (System.currentTimeMillis() - 60000);
                    int idElementoProcesado = Integer.parseInt(cmIdElemento.valor);
                    if (procesadoLessThanAMinuteAgo && idElementoProcesado == idElemento) {
                        // El elemento ya ha sido procesado
                        return true;
                    }
                }

                PosicionGPSViewModel posicion = asignarConfiguracionesDeProcesado(codigoRuta, idEmpresa, idElemento, fecha);

                RutasElementosDAO rutasElementosDAO = new RutasElementosDAOProxy();

                // Si se ha indicado que hay que quitarlo de la lista cambio el
                // estado del elemento a "procesado"
                if (marcarProcesado) {
                    rutasElementosDAO.setElementoProcesado(ruta.Codigo, ruta.Empresa, idElemento);
                }

                // Este campo lo coge cuando envía el job
                // int idRutaH = 0; //rutasDAO.getIdRutaH(ruta.Codigo);
                // Si lo tenemos lo asignamos ya
                String uuid = getUUID(ruta.Codigo);
                if (uuid == null || uuid.isEmpty()) {
                    Logg.error(TAG, "[setElementoProcesado] GuidRutaH vacío. No se puede procesar elementos sin haber iniciado actividad.");
                    return false;
                }
                int idRutaH = getIdRutaH(uuid);

                RutasApplication.getInstance().getJobManager().addJobInBackground(
                        new InsertarElementoProcesadoJob(new ElementoProcesadoViewModel(
                                ruta.Empresa, ruta.Codigo, idRutaH, idElemento, fecha,
                                posicion != null ? posicion.latitud : 0,
                                posicion != null ? posicion.longitud : 0,
                                TIPO_ELEMENTO_PROCESADO, uuid)));

                ElementosDAOProxy elementosDAO = new ElementosDAOProxy();
                ElementosModel elemento = elementosDAO.getElemento(idElemento, ruta.Empresa);

                if (showMessage) {
                    // Notifica el procesamiento del elemento.
                    EventBus.getDefault().post(OnElementoProcesadoEvent.deElemento(elemento));

//                    new Handler().postDelayed(() -> new ToastMensaje(MainActivity.getInstance()).
//                            show(BaseActivity.getInstance().getString(R.string.rutas_procesando_elemento_params,
//                                    elemento.Nombre), R.mipmap.procesar1), 5000);
                }

                res = true;
            }
        } catch (Throwable e) {
            Logg.error(TAG, "[setElementoProcesado] " + e.getMessage());
        }

        return res;
    }

    private PosicionGPSViewModel asignarConfiguracionesDeProcesado(int codigoRuta, int idEmpresa, int idElemento, Date fecha) {
        // Inicio la ruta (si se trata del primer elemento procesado)
        setInicioDeRuta(codigoRuta, idEmpresa, fecha);

        PosicionGPSViewModel posicion = MyLocationListener.getInstance().getUltimaPosicionGPS();

        // Guardo la fecha del último elemento procesado para finalizar la
        // ruta con esa misma fecha
        ConfiguracionManager.save("rutas-ultima-fecha-elemento-procesado", "" + fecha.getTime());

        // Guardo el elemento procesado, esto me sirve para saber cuando puedo realizar
        // descargas en el vertedero y cuando no
        ConfiguracionManager.save("rutas-ultimo-elemento-procesado", "" + idElemento);
        return posicion;
    }

    /**
     * Cambia el estado del elemento a proceaado y envia al servidor el elemento en cuestión
     */
    public synchronized boolean setElementosProcesados(int codigoRuta, int idEmpresa, List<ElementosModel> elementos,
                                                       Date fecha, boolean marcarProcesado, boolean showMessage) {
        boolean res = false;
        try {
            for (ElementosModel elemento : elementos) {
                Logg.info(TAG, "[setElementosProcesados] Procesando incidencia: " + elemento.toString2());
                setElementoProcesado(codigoRuta, idEmpresa, elemento.Codigo, fecha, marcarProcesado, showMessage);
            }
            res = true;
        } catch (Throwable e) {
            Logg.error(TAG, "[setElementosProcesados] " + e.getMessage());
        }

        return res;
    }

    /**
     * Cambia el estado de las incidencias a procesar y envia al servidor las incidencias.
     */
    public synchronized boolean setIncidenciasProcesadas(int codigoRuta, int idEmpresa, List<IncidenciasRutaModel> incidencias,
                                                         Date fecha, boolean marcarProcesado, boolean showMessage) {
        boolean res = false;
        try {
            for (IncidenciasRutaModel inci : incidencias) {
                Logg.info(TAG, "[setIncidenciasProcesadas] Procesando incidencia: " + inci.toString2());
                setIncidenciaProcesada(codigoRuta, idEmpresa, inci.Id, fecha, marcarProcesado, showMessage);
            }
            res = true;
        } catch (Throwable e) {
            Logg.error(TAG, "[setIncidenciasProcesadas] " + e.getMessage());
        }

        return res;
    }

    /**
     * Cambia el estado de la incidencia a procesar y envia al servidor la incidencia.
     */
    public synchronized boolean setIncidenciaProcesada(int codigoRuta, int idEmpresa, int idIncidencia,
                                                       Date fecha, boolean marcarProcesado,
                                                       boolean showMessage) {
        boolean res = false;
        try {

            // Obtengo los datos de la ruta
            RutasModel ruta = rutasDAO.getRutaBy(codigoRuta, idEmpresa );

            if (ruta != null) {

                // Inicio la ruta (si se trata del primer elemento procesado)
                PosicionGPSViewModel posicion = asignarConfiguracionesDeProcesado(codigoRuta, idEmpresa, idIncidencia, fecha);

                IncidenciasRutaDAO incidenciasRutaDAO = new IncidenciasRutaDAO();

                // Si se ha indicado que hay que quitarlo de la lista cambio el
                // estado del elemento a "procesado"
                if (marcarProcesado) {
                    incidenciasRutaDAO.setIncidenciaProcesada(ruta.Codigo, ruta.Empresa, idIncidencia);
                }

                // Este campo lo coge cuando envía el job
                // int idRutaH = 0; //rutasDAO.getIdRutaH(ruta.Codigo);
                String uuid = getUUID(ruta.Codigo);
                if (uuid == null || uuid.isEmpty()) {
                    Logg.error(TAG, "[setIncidenciaProcesada] GuidRutaH vacío. No se puede procesar incidencias sin haber iniciado actividad.");
                    return false;
                }
                int idRutaH = getIdRutaH(uuid);

                RutasApplication.getInstance().getJobManager().addJobInBackground(
                        new InsertarElementoProcesadoJob(new ElementoProcesadoViewModel(
                                ruta.Empresa, ruta.Codigo, idRutaH, idIncidencia, fecha,
                                posicion != null ? posicion.latitud : 0,
                                posicion != null ? posicion.longitud : 0,
                                TIPO_INCIDENCIA_PROCESADO, uuid)));

                IncidenciasRutaModel incidencia = incidenciasRutaDAO.getIncidencia(idIncidencia, ruta.Empresa);

                if (showMessage) {
                    EventBus.getDefault().post(OnElementoProcesadoEvent.deIncidencia(incidencia));
                }

                res = true;
            }
        } catch (Throwable e) {
            Logg.error(TAG, "[setIncidenciaProcesada] " + e.getMessage());
        }

        return res;
    }

    /**
     * Devuelve la ruta iniciada en el equipo, si la tiene, en caso contrario devuelve null
     *
     * @return Ruta iniciada
     */
    public RutasModel getRutaIniciada() {
        int idEmpresa = RutasApplication.getInstance().getEmpresa();
        if (idEmpresa <= 0) return null;
        try {
            return rutasDAO.getRutaIniciada(idEmpresa);
        } catch (Throwable e) {
            Logg.error(TAG, "[getRutaIniciada] " + e.getMessage());
        }
        return null;
    }

    public RutasModel getRutaSeleccionada() {
        return rutaSeleccionada;
    }

    public void setRutaIniciada() {
        rutaSeleccionada = rutasDAO.getRutaIniciada(RutasApplication.getInstance().getEmpresa());
    }

    public void setRutaSeleccionada(RutasModel ruta) {
        rutaSeleccionada = ruta;
    }

    private String setUUID(int codigoRuta) {
        String guid = java.util.UUID.randomUUID().toString();
        ConfiguracionManager.save("lastUUID" + codigoRuta, guid);
        return guid;
    }

    /**
     * Asegura que la ruta tenga un UUID asignado. Si no lo tiene, lo genera y lo persiste.
     * @param codigoRuta código de ruta
     * @return GuidRutaH no vacío
     */
    public synchronized String ensureUUID(int codigoRuta) {
        String guid = getUUID(codigoRuta);
        if (guid == null || guid.isEmpty()) {
            guid = setUUID(codigoRuta);
        }
        return guid;
    }

    public String getUUID(int codigoRuta) {
        String res = "";
        ConfiguracionModel conf = ConfiguracionManager.getConfBy("lastUUID" + codigoRuta);
        if (conf != null)
            res = conf.valor;
        return res;
    }

    public void setIdRutaH(String guidRutaH, int idRutaH) {
        try {
            if (guidRutaH == null || guidRutaH.isEmpty()) {
                Logg.warning(TAG, "[setIdRutaH] GuidRutaH vacío. No se guarda mapeo IdRutaH.");
                return;
            }
            if (idRutaH > 0)
                ConfiguracionManager.save(CONF_ID_RUTAH + guidRutaH, "" + idRutaH);
            else
                ConfiguracionManager.delete(CONF_ID_RUTAH + guidRutaH);
        } catch (Throwable e) {
            Logg.error(TAG, "[setIdRutaH] " + e.getMessage());
        }
    }

    public int getIdRutaH(String guidRutaH) {
        int idRutaH = 0;
        try {
            if (guidRutaH == null || guidRutaH.isEmpty()) {
                Logg.warning(TAG, "[getIdRutaH] GuidRutaH vacío. Devolviendo 0.");
                return 0;
            }
            ConfiguracionModel conf = ConfiguracionManager.getConfBy(CONF_ID_RUTAH + guidRutaH);
            if (conf != null && !conf.valor.isEmpty()) {
                idRutaH = Integer.parseInt(conf.valor);
            }
        } catch (Throwable e) {
            Logg.error(TAG, "[getIdRutaH] " + e.getMessage());
        }
        return idRutaH;
    }

    /**
     * Limpia el UUID asociado a la ruta.
     */
    public void clearUUIDByRuta(int codigoRuta) {
        try {
            ConfiguracionManager.save("lastUUID" + codigoRuta, "");
        } catch (Throwable e) {
            Logg.error(TAG, "[clearUUIDByRuta] " + e.getMessage());
        }
    }

}
