package com.movisat.application;

import com.movisat.log.ILoggProvider;
import com.movisat.log.LoggLine;
import com.movisat.managers.log.*;
import com.movisat.managers.log.LoggerManager.*;


public class LoggProviderLogger implements ILoggProvider {
    final LoggerManager logger = LoggerManager.getInstance();

    @Override
    public boolean isStarted() {
        return true;
    }

    @Override
    public void start() {
    }

    @Override
    public void write(LoggLine line) {
        TipoLog tipo = null;
        switch (line.logType) {
            case DATABASE:
            case SYNCHRONIZATION:
            case DEBUG:
                break;
            case INFO:
                tipo = TipoLog.Info;
                break;
            case WARNING:
                tipo = TipoLog.Warning;
                break;
            case CATASTROPHE:
            case ERROR:
                tipo = TipoLog.Error;
                break;
        }

        if(tipo == null) return;

        String type = line.logType.toString();
        String message =  "[" + line.datetime + "] [" + type + "] " + line.message;
        logger.Write(tipo, message);
    }
}
