package com.movisat.application;

import android.Manifest;
import android.app.Activity;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.Resources;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.text.InputType;
import android.util.Log;
import android.view.View;
import android.widget.EditText;

import androidx.core.app.ActivityCompat;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import androidx.multidex.MultiDex;

import com.activeandroid.ActiveAndroid;
import com.activeandroid.app.Application;
import com.environment.EnvironmentDebug;
import com.environment.EnvironmentDebugData;
import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;
import com.jobqueue.JobManager;
import com.jobqueue.OutBox;
import com.jobqueue.config.Configuration;
import com.jobqueue.network.NetworkUtil;
import com.movisat.activities.MainActivity;
import com.movisat.activities.BuildConfig;
import com.movisat.activities.R;
import com.movisat.activities.agenda.MyCallActivity;
import com.movisat.activities.splashScreen.SplashScreenActivity;
import com.movisat.callinglib.utils.PhoneUtils;
import com.movisat.dao.mensajes.MensajesDAO;
import com.movisat.dao.moviles.MovilesDAO;
import com.movisat.dao.rutas.RutasDAOProxy;
import com.movisat.dao.trabajadores.TrabajadoresDAO;
import com.movisat.jobs.elementos.ElementosJob;
import com.movisat.jobs.general.ConfirmarRecepcionJob;
import com.movisat.jobs.general.ModelosJob;
import com.movisat.jobs.incidencias.IncidenciasAsociacionJob;
import com.movisat.jobs.incidencias.IncidenciasModeloJob;
import com.movisat.jobs.incidencias.IncidenciasMotivoJob;
import com.movisat.jobs.incidencias.IncidenciasTipoJob;
import com.movisat.jobs.intranet.IntranetJob;
import com.movisat.jobs.intranet.IntranetLogJob;
import com.movisat.jobs.lecturallenado.LecturaLlenadoJob;
import com.movisat.jobs.mensajes.MensajesJob;
import com.movisat.jobs.mensajes.MensajesPredefinidosJob;
import com.movisat.jobs.moviles.MovilesJob;
import com.movisat.jobs.reparaciones.ReparacionesJob;
import com.movisat.jobs.revisiones.AtributosRevisionesJob;
import com.movisat.jobs.revisiones.RevisionesJob;
import com.movisat.jobs.rutas.AreasJob;
import com.movisat.jobs.rutas.AsignacionesJob;
import com.movisat.jobs.rutas.ProcesadoPorActividadJob;
import com.movisat.jobs.rutas.RutasJob;
import com.movisat.jobs.sincronizacion.SincronizacionJob;
import com.movisat.jobs.tags.TagsJob;
import com.movisat.jobs.trabajadores.CategoriaTrabajadoresJob;
import com.movisat.jobs.trabajadores.DescansosTrabajadoresJob;
import com.movisat.jobs.trabajadores.TrabajadoresJob;
import com.movisat.jobs.voluminosos.VoluminososModelJob;
import com.movisat.jobs.voluminosos.VoluminososTipoJob;
import com.movisat.listeners.gps.MyLocationListener;
import com.movisat.log.Logg;
import com.movisat.managers.ConfiguracionManager;
import com.movisat.managers.DracoManager;
import com.movisat.managers.ManagerTokenJob;
import com.movisat.managers.TTSManager;
import com.movisat.managers.log.LogManagerJobs;
import com.movisat.managers.log.LoggerManager;
import com.movisat.models.configuracion.ConfiguracionModel;
import com.movisat.models.moviles.MovilesModel;
import com.movisat.models.trabajadores.TrabajadoresModel;
import com.movisat.services.MyLocationService;
import com.movisat.services.RestClient;
import com.movisat.utilities.AdminUtils;
import com.movisat.utilities.RouteRecorder;
import com.movisat.utilities.Utils;
import com.movisat.viewmodels.general.ConfirmarRecepcionViewModel;
import com.movisat.viewmodels.mapa.PosicionGPSViewModel;
import com.splunk.mint.Mint;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.pedant.SweetAlert.SweetAlertDialog;


/**
 * Created by faroca on 17/07/2015.
 */
public class RutasApplication extends Application {
    protected static final LoggerManager logger = LoggerManager.getInstance();

    private static RutasApplication instance;
    private JobManager jobManager;
    private ManagerTokenJob jobManagerToken;
    private ArrayList<SincronizacionJob> jobs;
    private boolean isFinished = false;
    public boolean isPrimeraSincronizacion = false;
    private boolean isCallingLibInitialized = false;
    private String imei = "";

    // Tipo de software (EcoSAT)
    public static int TIPO_SOFTWARE = 10;

    //CLAVES DE VARIABLES DEL EQUIPO QUE SE GUARDAN EN CONFIGURACION
    public static final String CLAVE_IMEI_DISPOSITIVO = "imei-tablet";
    public static final String CLAVE_EMPRESA = "empresa";
    public static final String CLAVE_IMEI_MOVIL = "imei-movil";
    public static final String CLAVE_CODIGO_MOVIL = "codigo-movil";
    public static final String CLAVE_FIRMA = "firma";
    public static final String CLAVE_URL_API = "dracoConf.urlAPI";
    public static final String CLAVE_VER_SOFT = "dracoConf.verSoft";
    public static final String CLAVE_TIPO_SOFT = "dracoConf.tipoSoft";
    public static final String CLAVE_ZOOM_MAX = "zoom_max";
    public static final String CLAVE_ZOOM_MIN = "zoom_min";
    public static final String CLAVE_ZOOM_MOSTRAR_ELEMENTOS = "zoom_mostrar_elementos";
    public static final String CLAVE_DURACION_INCIDENCIAS = "duracion_incidencias";
    public static final String CLAVE_IMEI = "dracoGprs.imei";
    public static final String CLAVE_AGENDA = "dracoConf.agenda";
    public static final String CLAVE_PULSERA = "dracoSensor.bthRFID";
    public static final String CLAVE_MUEBLES_ENSERES = "btnMueblesEnseres";
    public static final String CLAVE_OCULTAR_DESCARGA = "btnOcultarDescarga";
    public static final String CLAVE_OCULTAR_PROCESAR = "btnOcultarProcesar";
    public static final String CLAVE_OCULTAR_PDF = "btnOcultarPdf";
    public static final String CLAVE_BRILLO_MINIMO = "brilloMinimo";
    public static final String CLAVE_LOGS_SINCRO = "logsSincro";
    public static final int VALUE_ZOOM_MAX = 19;
    public static final int VALUE_ZOOM_MIN = 13;
    public static final int VALUE_ZOOM_MOSTRAR_ELEMENTOS = 13;
    public static final int VALUE_INCIDENCIAS_DIAS_MAX = 30;
    public static final int VALUE_INCIDENCIAS_DIAS_MIN = 0;
    public static final int VALUE_INCIDENCIAS_DIAS_DEFAULT = 7;

    //NOMBRE DE LOS GRUPOS DE LOS JOBS DE SINCRONIZACION
    public static final String GROUP_JOB_MOVILES = "sincro-moviles";
    public static final String GROUP_JOB_MODELOS_ELEMENTOS = "sincro-modelos";
    public static final String GROUP_JOB__ELEMENTOS = "sincro-elementos";
    public static final String GROUP_JOB_TRABAJADORES_CATEGORIAS = "sincro-trabajadores-categorias";
    public static final String GROUP_JOB_TRABAJADORES = "sincro-trabajadores";
    public static final String GROUP_JOB_TRABAJADORES_DESCANSOS = "sincro-trabajadores-descansos";
    public static final String GROUP_JOB_INCIDENCIAS_TIPOS = "sincro-incidencias-tipo";
    public static final String GROUP_JOB_INCIDENCIAS_MODELOS = "sincro-incidencias-modelo";
    public static final String GROUP_JOB_INCIDENCIAS_MOTIVOS = "sincro-incidencias-motivo";
    public static final String GROUP_JOB_INCIDENCIAS = "sincro-incidencias";
    public static final String GROUP_JOB_MENSAJES = "sincro-mensajes";
    public static final String GROUP_JOB_MENSAJES_PREDEFINIDOS = "sincro-mensajes-predefinidos";
    public static final String GROUP_JOB_RUTAS = "sincro-rutas";
    public static final String GROUP_JOB_INTRANET = "sincro-intranet";
    public static final String GROUP_JOB_ATRIBUTOS_REVISIONES = "sincro-atributos-revisiones";
    public static final String GROUP_JOB_REVISIONES = "sincro-revisiones";
    public static final String GROUP_JOB_PROCESADO_ACTIVIDAD = "sincro-procesado-actividad";
    public static final String GROUP_JOB_REPARACIONES = "sincro-repraciones";
    public static final String GROUP_JOB_TAGS = "sincro-tags";
    public static final String GROUP_JOB_ASIGNACIONES = "sincro-asignaciones";
    public static final String GROUP_JOB_INCIDENCIAS_ASOCIACON = "sincro-asociaciones";

    public static final String GROUP_JOB_CONFIGURACION_INTRANET = "configuracion-intranet";
    public static final String GROUP_JOB_LECTURAS_LLENADOS = "sincro-lecturas-llenado";
    public static final String GROUP_JOB_AREAS = "sincro-areas";
    public static final String GROUP_JOB_VOLUMINOSOS_TIPOS = "sincro-voluminosos-tipo";
    public static final String GROUP_JOB_VOLUMINOSOS_MODELOS = "sincro-voluminosos-modelo";

    public static final String FIRST_SINCRO_MOVILES = "primera-sincro-moviles";
    public static final String FIRST_SINCRO_ELEMENTOS = "primera-sincro-elementos";

    //Nombre de las variables que se reciben de la Intranet
    public static final String INTRANET_VARIABLE_MODELO_RFID = "dracoSensor.bthRFID";

    public static NetworkUtil networkUtil = null;
    private Intent servicioGPS = null;

    private String imeiDispositivo = null;
    private String imeiMovil = null;
    private int empresa = 0;
    private String firma = null;
    private int codigoMovil = 0;
    private int numeroMensajesNoLeidos = 0;

    private int zoomMax = VALUE_ZOOM_MAX;
    private int zoomMin = VALUE_ZOOM_MIN;
    private int zoomMostrarElementos = VALUE_ZOOM_MOSTRAR_ELEMENTOS;

    public boolean movilCodeChanged = false;

    private Boolean isAlphanumericKeyboardMode;

    public RutasApplication() {
        instance = this;
    }

    public static final String ALPHANUMERIC_MODE = "isAlphanumericMode";

    private static final int REQUEST_CODE_PERMISSIONS = 1;

    @Override
    public void onCreate() {
        super.onCreate();
        Thread.setDefaultUncaughtExceptionHandler(new ResourceExceptionHandler(Thread.getDefaultUncaughtExceptionHandler()));
//
//        checkPermissions();

        // Para saber el ciclo de vida de la aplicación
//        if (BuildConfig.DEBUG)
//            registerActivityLifecycleCallbacks();

        Mint.initAndStartSession(this, "436f33be");
        isAlphanumericKeyboardMode = leerPreferenciaTecladoAlfanumerico();

        Logg.addProvider(new LoggProviderLogger());
        // Logg.addProvider(new LoggProviderConsole());

        // Se inicializa el motor de voz TextToSpeech (si se hace más tarde puede no inicializarse)
        TTSManager.get().init();

        Logg.info("RutasApplication", "Aplicación iniciada. Versión: " + BuildConfig.VERSION_CODE);
    }

    public void escribirPreferenciaTecladoAlfanumerico() {
        String value;
        isAlphanumericKeyboardMode = (isAlphanumericKeyboardMode == null) ? Boolean.TRUE : !isAlphanumericKeyboardMode;
        value = String.valueOf(isAlphanumericKeyboardMode.booleanValue());
        ConfiguracionManager.save(ALPHANUMERIC_MODE, value);
    }

    public void changeInputTypeIdentification(EditText inputText) {
        if (inputText != null)
            inputText.setInputType(isAlphanumericKeyboardMode != null && isAlphanumericKeyboardMode == Boolean.TRUE ? InputType.TYPE_CLASS_TEXT : InputType.TYPE_CLASS_NUMBER);
    }

    private Boolean leerPreferenciaTecladoAlfanumerico() {
        Boolean isAlphanumericMode = null;
        String value = ConfiguracionManager.getConfBy(ALPHANUMERIC_MODE) != null ? ConfiguracionManager.getConfBy(ALPHANUMERIC_MODE).valor : null;
        if (value != null) {
            try {
                isAlphanumericMode = Boolean.parseBoolean(value);
            } catch (Exception e) {
                return null;
            }
        }
        return isAlphanumericMode;
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
    }

    public String getImeiDispositivo() {
        return imeiDispositivo;
    }

    public void setImeiDispositivo(String imeiDispositivo) {
        this.imeiDispositivo = imeiDispositivo;
    }

    public void setAlphanumericKeyboardMode(Boolean isAlphanumericKeyboardMode) {
        this.isAlphanumericKeyboardMode = isAlphanumericKeyboardMode;
    }

    public Boolean isAlphanumericKeyboardMode() {
        return isAlphanumericKeyboardMode;
    }

    public void setImeiMovil(String imeiMovil) {
        this.imeiMovil = imeiMovil;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getImei() {
        return imei;
    }

    public int getEmpresa() {
        return empresa;
    }

    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }

    public String getFirma() {
        return firma;
    }

    public void setFirma(String firma) {
        this.firma = firma;
    }

    public int getCodigoMovil() {
        if (EnvironmentDebug.getData().getMobileCode() != null)
            return EnvironmentDebug.getData().getMobileCode();
        return codigoMovil;
    }

    public void setCodigoMovil(int codigoMovil) {

        this.codigoMovil = codigoMovil;
    }

    public int getIdConfiguracionMovil() {
        try {
            MovilesModel movil = new MovilesDAO().getMovil(codigoMovil);
            if (movil != null)
                return movil.IdConfiguracion;
        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return 0;
    }

    public MovilesModel getMovil() {
        try {
            return new MovilesDAO().getMovil(codigoMovil);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return null;
    }

    public void createJobManagerToken(Context context) {
        try {
            jobManagerToken = new ManagerTokenJob(context);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void configureJobManager() {
        try {
            // Este logger no es el que usamos para nosotros es uno especifico que
            // internamente usa el JobsManager
            com.noveogroup.android.log.Logger loggerJobs =
                    com.noveogroup.android.log.LoggerManager.getLogger();

            Configuration configuration = new Configuration.Builder(this)
                    .customLogger(new LogManagerJobs("JOBS", loggerJobs))
                    .id("JOBS")
                    .minConsumerCount(20)//Como minimo deben estar activos todos los jobs de sincronizacion y uno mas para los jobs de envio
                    .maxConsumerCount(100)// maximo 100 trabajos concurrentes
                    .loadFactor(1)// 1 trabajo por consumidor
                    .consumerKeepAlive(1)//Se queda vivo durante 0 segundos hasta comprobar si existe un nuevo trabajo
                    .build();
            jobManager = new JobManager(this, configuration);
            networkUtil = configuration.getNetworkUtil();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public JobManager getJobManager(boolean createNewInstance) {
        try {
            if (jobManager == null || createNewInstance)
                configureJobManager();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return jobManager;
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        MultiDex.install(this);
    }

    public JobManager getJobManager() {
        return getJobManager(false);
    }

    public void setJobManager(JobManager jobManager) {
        this.jobManager = jobManager;
    }

    public void setJobManagerToken(ManagerTokenJob jobManagerToken) {
        this.jobManagerToken = jobManagerToken;

    }

    public ManagerTokenJob getJobManagerToken(boolean createNewInstance) {
        try {
            if (jobManagerToken == null && createNewInstance) {
                createJobManagerToken(this);
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return jobManagerToken;
    }

    public ManagerTokenJob getJobManagerToken() {
        return getJobManagerToken(false);
    }


    public synchronized static RutasApplication getInstance() {
        return instance;
    }

    private void initializeVariablesAplicacion() {

        try {
            EnvironmentDebugData debugData = EnvironmentDebug.getData();
            ConfiguracionModel conf = null;

            isFinished = false;

            //Obtenemos el IMEI del dispositivo del núcleo (Draco)
            conf = DracoManager.getConfigBy(CLAVE_IMEI, "");
            if (conf != null)
                imei = conf.valor;
            String imeiInternal = Utils.getIMEI(this);
            imei = imeiInternal.isEmpty() ? imei : imeiInternal;

            if (debugData.getDeviceId() != null)
                imei = debugData.getDeviceId();

            imeiDispositivo = imei;
            imeiMovil = imei;

            // Asigno el IMEI a la agenda
            com.movisat.callinglib.utils.Utils.setIMEI(imei);

            ConfiguracionManager.save(CLAVE_IMEI_DISPOSITIVO, imei);
            ConfiguracionManager.save(CLAVE_IMEI_MOVIL, imei);

            //Obtenemos la url del servidor
            conf = DracoManager.getConfigBy(CLAVE_URL_API, "");
            if (conf != null) {
                String urlServer = conf.valor;
                if (debugData.getUrlServer() != null)
                    urlServer = debugData.getUrlServer();
                if (urlServer.isEmpty() || urlServer.equals("http://tecno01.movisat.com:8083")) {
                    ConfiguracionModel confServer = ConfiguracionManager.getConfBy("urlServ", "");
                    urlServer = confServer.valor;
                }
                if (!urlServer.isEmpty()) RestClient.setUrlApi(urlServer);
                ConfiguracionManager.save(CLAVE_URL_API, urlServer);
            }

            // Obtenemos el codigo de móvil
            conf = ConfiguracionManager.getConfBy(CLAVE_CODIGO_MOVIL, "0");
            int confCodigoMovil = Integer.parseInt(conf.valor);

            // De todas formas obtengo el código de móvil a partir del IMEI del dispositivo
            // aunque lo tenga guardado por si se ha cambiado el móvil de empresa
            boolean movilFound = asignaCodigoMovil();

            // Si ha cambiado el móvil, se borra la base de datos para sincronizar datos de nuevo.
            if (confCodigoMovil != 0 && confCodigoMovil != codigoMovil)
                showDeleteDatabaseDialog();

            // Evitamos que borre la base de datos durante las pruebas.
            if (EnvironmentDebug.isRelease()) {
                if (!movilFound) {
                    // TODO: En este caso, la aplicación no debería inicarse...
                    codigoMovil = confCodigoMovil;
                } else if (codigoMovil != confCodigoMovil) {
                    // Si el móvil ha cambiado, se debe haber reasignado el dispositivo.
                    // Si es así, se borra la base de datos para sincronizar datos de nuevo.
                    showDeleteDatabaseDialog();
                    movilCodeChanged = true;
                }
            }

            //Obtenemos la empresa
            empresa = 0;
            conf = ConfiguracionManager.getConfBy(CLAVE_EMPRESA);
            if (conf != null) {
                empresa = Integer.parseInt(conf.valor);
            }
            if (debugData.getCompanyId() != null)
                empresa = debugData.getCompanyId();


            //Obtenemos la firma
            conf = ConfiguracionManager.getConfBy(CLAVE_FIRMA);
            if ((conf == null || conf.valor.isEmpty() || conf.valor.equals("FEA8D2EF4A547284CB4EE5D03999E2DA"))
                    && imeiDispositivo != null && !imeiDispositivo.isEmpty()) {
                conf = ConfiguracionManager.save(CLAVE_FIRMA, Utils.getFirma(imeiDispositivo));
            }
            firma = conf.valor;

            //Obtenemos el zoom maximo
            conf = ConfiguracionManager.getConfBy(CLAVE_ZOOM_MAX);
            if (conf == null) {
                conf = ConfiguracionManager.save(CLAVE_ZOOM_MAX, String.valueOf(zoomMax));
            }
            zoomMax = (int) Float.parseFloat(conf.valor);

            //Obtenemos el zoom minimo
            conf = ConfiguracionManager.getConfBy(CLAVE_ZOOM_MIN);
            if (conf == null) {
                conf = ConfiguracionManager.save(CLAVE_ZOOM_MIN, String.valueOf(zoomMin));
            }
            zoomMin = (int) Float.parseFloat(conf.valor);

            //Obtenemos el zoom mostrar elementos
            conf = ConfiguracionManager.getConfBy(CLAVE_ZOOM_MOSTRAR_ELEMENTOS);
            if (conf == null) {
                conf = ConfiguracionManager.save(CLAVE_ZOOM_MOSTRAR_ELEMENTOS, String.valueOf(zoomMostrarElementos));
            }
            zoomMostrarElementos = (int) Float.parseFloat(conf.valor);

            // Inicializamos el listener para el GPS
            MyLocationListener.getInstance();

            numeroMensajesNoLeidos = new MensajesDAO().getMensajesNoLeidos();

            // Ejecutamos el comando para saber el brillo mínimo del dispositivo
            String output = AdminUtils.executeCommandWithResponse("dumpsys display | grep mScreenBrightnessRangeMinimum");
            if (!output.isEmpty()) {
                // Se espera una salida del tipo: "mScreenBrightnessRangeMinimum=5"
                String[] tokens = output.split("=");
                if (tokens.length > 1) {
                    String minBrightness = tokens[tokens.length - 1].trim();
                    ConfiguracionManager.save(CLAVE_BRILLO_MINIMO, minBrightness);
                }
            }

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public boolean asignaCodigoMovil() {
        boolean res = false;

        try {

            // Si no se ha asignado todavía obtengo el código de móvil a partr del IMEI del dispositivo
            MovilesDAO movilesDAO = new MovilesDAO();
            List<MovilesModel> listaMoviles = movilesDAO.getAllMoviles();

            if (listaMoviles == null) return false;

            for (MovilesModel mov : listaMoviles) {
                if (mov.Imei != null && mov.Imei.equals(imei)) {
                        setEmpresa(mov.Empresa);
                        saveCodigoMovil(mov.Codigo);
                        res = true;
                        break;
                }
            }
        } catch (Throwable e) {

            logger.WriteError(e);
        }

        return res;
    }

    public boolean isFinished() {
        return isFinished;
    }

    private void initializeJobs() {

        try {

            //Creamos manager para token
            jobManagerToken = getJobManagerToken(true);

            //Creamos manager para trabajos
            jobManager = getJobManager(true);


            //Array que contendra los jobs de sincronizacion
            jobs = new ArrayList<SincronizacionJob>();

            //Solicitamos el token
            jobManagerToken.getPeticionToken(ManagerTokenJob.OPERACION_TOKEN);

            //Añadimos el Job de la intranet para recuperar la configuración del dispositivo
            IntranetJob intranetJob = new IntranetJob();
            jobManager.addJobInBackground(intranetJob);

            // TODO: AOrtiz 09-04-2024
            // Iniciamos la bandeja de salida
            OutBox.getInstance();

            //Lanzamos todos los jobs de sincronizacion
            addJobSincro(new IntranetJob());                //Sincronizacion con la intranet
            addJobSincro(new MovilesJob());                 //Sincronizacion de moviles.
            addJobSincro(new ModelosJob());                 //Sincronizacion de modelos.
            addJobSincro(new ElementosJob());               //Sincronizacion de elementos.
            addJobSincro(new CategoriaTrabajadoresJob());   //Sincronizacion de categorias de trabajadores.
            addJobSincro(new TrabajadoresJob());            //Sincronizacion de trabajadores.
            addJobSincro(new DescansosTrabajadoresJob());   //Sincronizacion de descansos.
            addJobSincro(new IncidenciasTipoJob());         //Sincronizacion de tipos de incidencias.
            addJobSincro(new IncidenciasModeloJob());       //Sincronizacion de modelos de incidencias.
            addJobSincro(new IncidenciasMotivoJob());       //Sincronizacion de motivos de incidencias.
            addJobSincro(new IncidenciasAsociacionJob());   //Sincronizacion de asociacion de incidencias.
            addJobSincro(new MensajesJob());                //Sincronizacion de mensajes.
            addJobSincro(new MensajesPredefinidosJob());    //Sincronizacion de mensajes.

            addJobSincro(new RutasJob());                   //Sincronizacion de rutas.
            addJobSincro(new AreasJob());                   //Sincronizacion de áreas.
            addJobSincro(new AsignacionesJob());            //Sincronizacion de asignaciones.

            addJobSincro(new IntranetLogJob());             //Sincronización de Logs para enviar a intranet.
            addJobSincro(new AtributosRevisionesJob());     //Sincronización de los atributos de las revisiones
            addJobSincro(new RevisionesJob());              //Sincronización de las revisiones
            addJobSincro(new ProcesadoPorActividadJob());   //Sincronizacion de los tipos de procesado por actividad de ruta
            addJobSincro(new ReparacionesJob());            //Sincronizacion de reparaciones
            addJobSincro(new LecturaLlenadoJob());          //Sincronizacion de lecturas de llenado.

            addJobSincro(new TagsJob());                     //Sincronizacion de tags
            addJobSincro(new VoluminososTipoJob());          //Sincronizacion de tipos de voluminosos
            addJobSincro(new VoluminososModelJob());        //Sincronizacion de modelos de voluminosos

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    private void finalizeJobs() {

        try {
            if (jobs != null) {
                for (SincronizacionJob job : jobs) {
                    job.finalizar();
                }
            }

            if (jobManager != null) {
                jobManager.stop();
            }
            if (jobManagerToken != null) {
                jobManagerToken.finalizar();
            }
            jobManager = null;
            jobManagerToken = null;

            // TODO: AOrtiz 05-04-2024
            // Finalizamos el hilo de la bandeja de salida
            OutBox.getInstance().finalize();

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void initialize() {

        try {

            // Inicializo el refresco del watchdog
            initWatchDog();

            //Inicializamos la base de datos
            initializeDB();

            //Inicializamos variables de la aplicacion
            initializeVariablesAplicacion();

            //Inicializamos los jobs
            initializeJobs();

            // Se inicializa el GPS
            initializeGPS();

            // Se inicializa la librería de contactos y llamadas
            initializeCallingLib();

            // Se inicializa el servicio de grabación de posiciones. Solo CIEZA
            if (getEmpresa() == 582 || getEmpresa() == 484) {
                new RouteRecorder(this);
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }


    public void checkPermissions() {
        // Permisos normales que se solicitan con requestPermissions
        List<String> permissionsNeeded = new ArrayList<>();

        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            permissionsNeeded.add(Manifest.permission.ACCESS_FINE_LOCATION);
        }

        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            permissionsNeeded.add(Manifest.permission.ACCESS_COARSE_LOCATION);
        }

        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
            permissionsNeeded.add(Manifest.permission.CALL_PHONE);
        }

        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            permissionsNeeded.add(Manifest.permission.CAMERA);
        }

        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.CHANGE_WIFI_STATE) != PackageManager.PERMISSION_GRANTED) {
            permissionsNeeded.add(Manifest.permission.CHANGE_WIFI_STATE);
        }

        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.WAKE_LOCK) != PackageManager.PERMISSION_GRANTED) {
            permissionsNeeded.add(Manifest.permission.WAKE_LOCK);
        }

        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.NFC) != PackageManager.PERMISSION_GRANTED) {
            permissionsNeeded.add(Manifest.permission.WAKE_LOCK);
        }
        
        // Permisos Bluetooth para Android 12+ (API 31+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                permissionsNeeded.add(Manifest.permission.BLUETOOTH_CONNECT);
            }
        }

        // Permisos especiales que se solicitan con un intent
        List<String> specialPermissions = new ArrayList<>();
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.WRITE_SETTINGS) != PackageManager.PERMISSION_GRANTED) {
            specialPermissions.add(Manifest.permission.WRITE_SETTINGS);
        }

        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.SYSTEM_ALERT_WINDOW) != PackageManager.PERMISSION_GRANTED) {
            specialPermissions.add(Manifest.permission.SYSTEM_ALERT_WINDOW);
        }

        if (!permissionsNeeded.isEmpty()) {
            ActivityCompat.requestPermissions(MainActivity.getInstance(),
                  permissionsNeeded.toArray(new String[permissionsNeeded.size()]),
                  REQUEST_CODE_PERMISSIONS);
        }

        // Permisos especiales
        for (String permission : specialPermissions) {
            switch (permission) {
                case Manifest.permission.WRITE_SETTINGS:
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        if (!Settings.System.canWrite(this)) {
                            Intent intent = new Intent(Settings.ACTION_MANAGE_WRITE_SETTINGS);
                            intent.setData(Uri.parse("package:" + getPackageName()));
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            startActivity(intent);
                        }
                    }
                    break;

                case Manifest.permission.SYSTEM_ALERT_WINDOW:
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        if (!Settings.canDrawOverlays(this)) {
                            Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                                  Uri.parse("package:" + getPackageName()));
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            startActivity(intent);
                        }
                    }
                    break;
            }
        }
    }

    public void finalize() {

        try {
            isFinished = true;

            // Finalizamos el servicio de grabación de posiciones
            finalizeGPS();

            //Finalizamos todos los trabajos de sincronizacion
            finalizeJobs();

            // Se finaliza la librería de contactos y llamadas
            finalizeCallingLib();

            killProcess();

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    private void finalizeGPS() {
        try {
            if (servicioGPS != null)
                stopService(servicioGPS);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void initializeGPS() {
        try {
            ConfiguracionModel saveRuta = ConfiguracionManager.getConfBy("dracoGps.grabarRuta");
            if (saveRuta == null)
                return;
            if (saveRuta.valor != null && saveRuta.valor.equals("1")) {
                servicioGPS = new Intent(this, MyLocationService.class);
                if (startService(servicioGPS) != null) {
                    logger.WriteInfo("Servicio GPS iniciado");
                } else {
                    logger.WriteWarning("Servicio GPS fallido!!!");
                }
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public int getNumeroMensajesNoLeidos() {
        return numeroMensajesNoLeidos;
    }

    public void setNumeroMensajesNoLeidos(int numeroMensajesNoLeidos) {
        this.numeroMensajesNoLeidos = numeroMensajesNoLeidos;
    }

    /**
     * Iniciamos la Base de Datos
     */
    private void initializeDB() {
        try {
            ActiveAndroid.initialize(this);
            applyFixes();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    private void applyFixes() {
        try {
            // Obtengo el fix-version
            ConfiguracionModel fixVersionConf = ConfiguracionManager.getConfBy("fix-version");
            String fixVersion = fixVersionConf != null ? fixVersionConf.valor : null;

            // 240524: Cuando se actualiza la app con ruta iniciada
            // hay que hacer un fix para que se actualice el lastUUID y el conf-id-rutah
            if (fixVersion == null || Integer.parseInt(fixVersion) < 240524) {
                fix240524();
                ConfiguracionManager.save("fix-version", "240524");
            }

            // 250909: Limpieza de claves huérfanas sin sufijo que pueden provocar lecturas/escrituras erróneas
            if (fixVersion == null || Integer.parseInt(fixVersion) < 250909) {
                fix250909();
                ConfiguracionManager.save("fix-version", "250909");
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    private void fix240524() {
        ConfiguracionModel empresaConf = ConfiguracionManager.getConfBy("empresa");
        int empresa = empresaConf != null ? Integer.parseInt(empresaConf.valor) : 0;
        if (empresa == 0) {
            throw new RuntimeException("No se ha podido obtener la empresa");
        }

        // Obtengo la ruta iniciada
        int idRuta = new RutasDAOProxy().getRutaIniciadaId(empresa);
        if (idRuta == 0) {
            return; // No hay ruta iniciada
        }

        // Intento obtener el lastUUID de la tabla configuraciones con clave "lastUUID<idRuta>"
        String lastUUIDKey = "lastUUID";
        ConfiguracionModel lastUUIDValueConf = ConfiguracionManager.getConfBy(lastUUIDKey + idRuta);
        String lastUUIDValue = lastUUIDValueConf != null ? lastUUIDValueConf.valor : null;

        // Si ya existe el lastUUID nuevo para esta ruta, no hago nada
        if (lastUUIDValue != null && !lastUUIDValue.isEmpty()) {
            return;
        }

        // Si no existe, obtengo el valor de lastUUID de la tabla configuraciones
        lastUUIDValueConf = ConfiguracionManager.getConfBy(lastUUIDKey);
        lastUUIDValue = lastUUIDValueConf != null ? lastUUIDValueConf.valor : null;
        if (lastUUIDValue == null || lastUUIDValue.isEmpty()) {
            return; // No hay lastUUID -> no hay nada que hacer
        }

        // Obtengo el valor de conf-id-rutah(idRuta) de la tabla configuraciones
        String confIdRutaHKey = "conf-id-rutah";
        ConfiguracionModel confIdRutaHValueConf = ConfiguracionManager.getConfBy(confIdRutaHKey + idRuta);
        String confIdRutaHValue = confIdRutaHValueConf != null ? confIdRutaHValueConf.valor : null;
        if (confIdRutaHValue == null || confIdRutaHValue.isEmpty()) {
            return; // No hay conf-id-rutah(idRuta) -> no hay nada que hacer
        }
        // Guardo el idRutaH con clave "conf-id-rutah<lastUUID>"
        ConfiguracionManager.save(confIdRutaHKey + lastUUIDValue, confIdRutaHValue);

        // Guardo el valor de lastUUID con clave "lastUUID<idRuta>"
        ConfiguracionManager.save(lastUUIDKey + idRuta, lastUUIDValue);

        // Borro los registros antiguos: "conf-id-rutah<idRuta>" y "lastUUID"
        ConfiguracionManager.delete(confIdRutaHKey + idRuta);
        ConfiguracionManager.delete(lastUUIDKey);
    }

    private void fix250909() {
        try {
            // Borro posibles claves sin sufijo que no deben existir
            ConfiguracionManager.delete("conf-id-rutah");
            // Ya se borra en 240524, pero nos aseguramos
            ConfiguracionManager.delete("lastUUID");
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void synchronizeAll() {
        try {
            if (jobs != null) {
                for (SincronizacionJob job : jobs) {
                    job.synchronizeNow();
                }
            }

            // Notifico a la agenda para que sincronice también
            PhoneUtils.forceSync();

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public boolean isMovilSeleccionado() {
        return (codigoMovil > 0);
    }

    public boolean hasEmpresa() {
        return empresa > 0;
    }


    public void saveImeiMovil(String imei) {
        try {
            ConfiguracionManager.save(RutasApplication.CLAVE_IMEI_MOVIL, imei);
            setImeiMovil(imei);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void saveImeiDispositivo(String imei) {
        try {
            ConfiguracionManager.save(RutasApplication.CLAVE_IMEI_DISPOSITIVO, imei);
            setImeiDispositivo(imei);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void saveEmpresa(int empresa) {
        try {
            ConfiguracionManager.save(RutasApplication.CLAVE_EMPRESA, "" + empresa);
            setEmpresa(empresa);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void saveCodigoMovil(int codigo) {
        try {
            ConfiguracionManager.save(RutasApplication.CLAVE_CODIGO_MOVIL, "" + codigo);
            setCodigoMovil(codigo);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void changedVehiculo() {
        try {
            numeroMensajesNoLeidos = new MensajesDAO().getMensajesNoLeidos();
            /* Esta llamada envia al servidor un mensaje a la tabla de comunicaciones para ver cuando se asigno el movil */
            //sendInfoChangeMovil();
            synchronizeAll();
        } catch (Exception e) {
            logger.WriteError(e);
        }
    }

    private void sendInfoChangeMovil() {
        try {

            String mensaje = "Dispositivo " + new MovilesDAO().getMovil(getImeiDispositivo()).Codigo + " asociado al movil: " + getCodigoMovil();
            createJobConfirmacion(getImeiDispositivo(), mensaje);

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void createJobConfirmacion(String imei, String mensaje, Date fecha) {
        ConfirmarRecepcionViewModel confirmacion = new ConfirmarRecepcionViewModel(imei, mensaje, fecha);
        if (jobManager != null)
            jobManager.addJobInBackground(new ConfirmarRecepcionJob(confirmacion));
    }

    public void createJobConfirmacion(String imei, String mensaje) {
        createJobConfirmacion(imei, mensaje, new Date());
    }


    public SincronizacionJob getJobSincronizacion(String grupo) {
        try {
            if (jobs != null) {
                for (SincronizacionJob job : jobs)
                    if (job.getGrupo().equals(grupo))
                        return job;
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return null;
    }

    public boolean isSincronizando() {
        if (jobs != null) {
            for (SincronizacionJob job : jobs) {
                if (job.isSincronizando()) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean isSincronizando(String groupJob) {
        try {
            SincronizacionJob job = getJobSincronizacion(groupJob);
            if (job != null)
                return job.isSincronizando();
        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return false;
    }

    public boolean isRutaIniciada() {
        boolean res = false;
        try {
            res = new RutasDAOProxy().isRutaIniciada(getEmpresa());
        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return res;
    }

    public boolean isTrabajadorIdentificado() {
        try {
            TrabajadoresDAO trabajadoresDAO = new TrabajadoresDAO();
            List<TrabajadoresModel> trabajadoresIdentificados = trabajadoresDAO.getTrabajadoresIdentificados(getCodigoMovil());
            if (trabajadoresIdentificados != null && trabajadoresIdentificados.size() > 0) {
                return true;
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return false;
    }

    public boolean hasNetwork() {
        boolean res = false;
        try {
            ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo activeNetInfo = connectivityManager.getActiveNetworkInfo();
            res = (activeNetInfo != null && activeNetInfo.isConnectedOrConnecting());
        } catch (Throwable e) {
            logger.WriteError(e);
        }
        return res;
    }

    private void addJobSincro(SincronizacionJob job) {
        try {
            jobs.add(job);
            jobManager.addJobInBackground(job);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public boolean isPrimeraSincronizacion() {
        try {
            ConfiguracionManager.getInstance();
            ConfiguracionModel confMoviles = ConfiguracionManager.getConfBy(FIRST_SINCRO_MOVILES);
            ConfiguracionManager.getInstance();
            ConfiguracionModel confElementos = ConfiguracionManager.getConfBy(FIRST_SINCRO_ELEMENTOS);
            if (confMoviles == null || confElementos == null) {
                isPrimeraSincronizacion = true;
            } else {
                isPrimeraSincronizacion = false;
                asignaCodigoMovil();
            }
            return isPrimeraSincronizacion;
        } catch (Throwable e) {
            logger.WriteError(e);
            return false;
        }
    }


    private void initializeCallingLib() {

        try {
            // Solo se inicializa la librería de llamadas si el nivel de la API >= 23 y se ha
            // configurado a "1" el parámetro "dracoConf.agenda" desde la intranet
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {

                ConfiguracionModel conf = DracoManager.getConfigBy(RutasApplication.CLAVE_AGENDA, "0");
                if (conf != null && conf.valor.equals("1")) {

                    isCallingLibInitialized = true;

                    String urlApi = "";

                    // Obtenemos la url de la API para pasarsela a la App de la agenda
                    conf = DracoManager.getConfigBy(CLAVE_URL_API, "");
                    if (conf != null)
                        urlApi = conf.valor;

                    // Asigna la aplicación anfitriona y la URL para llamadas a la API
                    PhoneUtils.setHomeApp(this);
                    PhoneUtils.setUrlApi(urlApi);

                    // Inicializo la librería e inicio el proceso de sincronización de contactos
                    PhoneUtils.enableCalling(MyCallActivity.class);
                    PhoneUtils.initSynchro();

                    logger.WriteInfo("Agenda y llamadas habilitadas");
                }
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }


    private void finalizeCallingLib() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M)
            return;
        try {
            if (isCallingLibInitialized) {
                PhoneUtils.disableCalling();
                isCallingLibInitialized = false;
                logger.WriteInfo("Agenda y llamadas deshabilitadas");
            }
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }


    public boolean isCallingLibInitialized() {
        return isCallingLibInitialized;
    }


    /**
     * Comprueba el estado de los servicios de Google Play y en caso de necesitar
     * una actualización (en cuyo caso no se mostrarían los mapas), se manda un
     * mensaje al Draco para que actualice la versión de Google Play Services.
     */
    public int checkGooglePlayServices() {
        int status = 0;

        try {

            status = getGoogleApiStatus();
            logger.WriteInfo("Estado de Google Play Services: " + status);

            if (status == ConnectionResult.SERVICE_VERSION_UPDATE_REQUIRED) {
                logger.WriteInfo("Los servicios de Google Play necesitan ser actualizados. Informando al Draco...");
                DracoManager.sendNotification(DracoManager.NOTIFY_GOOGLE_PLAY_SERVICES, null);
            }

        } catch (Throwable e) {
            logger.WriteError(e);
        }

        return status;
    }


    /**
     * Devuelve el estado de la API de Google.
     *
     * @return Código de estado.
     */
    public int getGoogleApiStatus() {

        try {

            GoogleApiAvailability googleAPI = GoogleApiAvailability.getInstance();
            return googleAPI.isGooglePlayServicesAvailable(this);

        } catch (Throwable e) {
            logger.WriteError(e);
        }

        return ConnectionResult.UNKNOWN;
    }


    private PosicionGPSViewModel fakeGPSPosition = null;


    public PosicionGPSViewModel getFakeGPSPosition() {

        try {
            if (fakeGPSPosition == null) {
                double latitud = Double.parseDouble(ConfiguracionManager.getConfBy("fakeGPS_latitud").valor);
                double longitud = Double.parseDouble(ConfiguracionManager.getConfBy("fakeGPS_longitud").valor);
                float velocidad = Float.parseFloat(ConfiguracionManager.getConfBy("fakeGPS_velocidad").valor);
                double altura = Double.parseDouble(ConfiguracionManager.getConfBy("fakeGPS_altura").valor);
                float rumbo = Float.parseFloat(ConfiguracionManager.getConfBy("fakeGPS_rumbo").valor);
                int numSatelites = Integer.parseInt(ConfiguracionManager.getConfBy("fakeGPS_numSatelites").valor);

                fakeGPSPosition = new PosicionGPSViewModel(null, latitud, longitud,
                        velocidad, altura, rumbo, numSatelites, new Date());
            }

            // Siempre pongo la fecha actual a la posición inventada
            fakeGPSPosition.fecha = new Date();
            fakeGPSPosition.fakePosition = true;

        } catch (Throwable e) {
            logger.WriteError(e);
        }

        return fakeGPSPosition;
    }


    /**
     * Muestra un diálogo informativo de que la base de datos será borrada y la aplicación se
     * reiniciará para sincronizar de nuevo los datos. Esta función se utiliza tras una asociación
     * del dispositivo a un vehículo distinto.
     */
    public void showDeleteDatabaseDialog() {
        try {
            String message = getString(R.string.delete_database);

            final SweetAlertDialog dlg = new SweetAlertDialog(MainActivity.getInstance(),
                    SweetAlertDialog.WARNING_TYPE)
                    .setTitleText(MainActivity.getInstance().getString(R.string.atencion))
                    .setContentText(message)
                    .setConfirmText(MainActivity.getInstance().getString(R.string.dialog_ok));

            dlg.setCancelable(false);

            dlg.setOnShowListener(new DialogInterface.OnShowListener() {
                @Override
                public void onShow(DialogInterface dialog) {
                    // El diálogo no se puede cancelar: solo aceptar
                    dlg.findViewById(R.id.cancel_button).setVisibility(View.INVISIBLE);
                }
            });

            dlg.setConfirmClickListener(new SweetAlertDialog.OnSweetClickListener() {
                @Override
                public void onClick(SweetAlertDialog sweetAlertDialog) {
                    try {
                        deleteDatabase("rutas.db");
                        deleteDatabase("outbox.db");
                        deleteDatabase("db_JOBS");
                        deleteDatabase("db_TOKEN");
                        resetApp();

                    } catch (Throwable e) {
                        logger.WriteError(e);
                    }
                }
            }).show();

        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    public void resetApp() {
        try {
            Intent mStartActivity = new Intent(getApplicationContext(), SplashScreenActivity.class);
            int mPendingIntentId = 123456;
            PendingIntent mPendingIntent = PendingIntent.getActivity(getApplicationContext(), mPendingIntentId, mStartActivity, PendingIntent.FLAG_CANCEL_CURRENT);
            AlarmManager mgr = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
            mgr.set(AlarmManager.RTC, System.currentTimeMillis() + 500, mPendingIntent);
            System.exit(0);
        } catch (Throwable e) {
            logger.WriteError(e);
        }
    }

    /**
     * Mata el proceso tras un cierto retardo. Esta función soluciona un problema al inicializar
     * una instancia de TextToSpeech. La inicialización se realiza con éxito al inicio de la app
     * si el proceso no existía antes. Sin embargo, si se cierra manualmente la aplicación, el
     * proceso no muere. Al volver a iniciar la aplicación con el proceso aún existente, el
     * objeto TextToSpeech no llega a inicializarse.
     * TODO: Ver cuál es el mecanismo para inicializar TextToSpeech limpiamente.
     */
    private void killProcess() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    Thread.sleep(2000);
                    android.os.Process.killProcess(android.os.Process.myPid());
                } catch (Throwable e) {
                }
            }
        }).start();
    }

    /**
     * Refresca el contador para el watchdog
     */
    private void initWatchDog() {

        new Thread(new Runnable() {

            @Override
            public void run() {

                try {

                    while (!isFinished) {

                        Intent intent = new Intent();
                        intent.setAction("com.movisat.dracowd.action.WD_COUNT_ECOSAT");
                        //        ContentValues values = new ContentValues();
                        //        values.put("id", 1);
                        //        values.put("nombre", "prueba");
                        //        intent.putExtra("values", values);
                        intent.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
                        sendBroadcast(intent);

                        Thread.sleep(15000);
                    }

                } catch (Throwable e) {
                }
            }
        }).start();
    }

    public class ResourceExceptionHandler implements Thread.UncaughtExceptionHandler {
        private final Thread.UncaughtExceptionHandler defaultHandler;

        public ResourceExceptionHandler(Thread.UncaughtExceptionHandler defaultHandler) {
            this.defaultHandler = defaultHandler;
        }

        @Override
        public void uncaughtException(Thread t, Throwable e) {
            if (e instanceof Resources.NotFoundException) {
                Logg.error("uncaughtException", "Resource not found" + e);
            }
            Logg.error("uncaughtException", "Undefined: " + e);
            defaultHandler.uncaughtException(t, e);
        }
    }

    private void registerActivityLifecycleCallbacks() {
        registerActivityLifecycleCallbacks(new ActivityLifecycleCallbacks() {
            private static final String TAG = "ActivityLifecycle";

            private final FragmentManager.FragmentLifecycleCallbacks fragmentLifecycleCallbacks = new FragmentManager.FragmentLifecycleCallbacks() {
                private static final String TAG = "FragmentLifecycle";

                private boolean isInternalFragment(Fragment f) {
                    return f.getClass().getName().startsWith("androidx.fragment.app.ReportFragment");
                }

                @Override
                public void onFragmentPreAttached(FragmentManager fm, Fragment f, Context context) {
                    if (isInternalFragment(f)) return;
                    Log.d(TAG, f.getClass().getName() + " preAttached");
                }

                @Override
                public void onFragmentCreated(FragmentManager fm, Fragment f, Bundle savedInstanceState) {
                    super.onFragmentCreated(fm, f, savedInstanceState);
                    if (isInternalFragment(f)) return;
                    Log.d(TAG, f.getClass().getName() + " created");
                }

                @Override
                public void onFragmentStarted(FragmentManager fm, Fragment f) {
                    if (isInternalFragment(f)) return;
                    Log.d(TAG, f.getClass().getName() + " started");
                }

                @Override
                public void onFragmentResumed(FragmentManager fm, Fragment f) {
                    if (isInternalFragment(f)) return;
                    Log.d(TAG, f.getClass().getName() + " resumed");
                }

                @Override
                public void onFragmentPaused(FragmentManager fm, Fragment f) {
                    if (isInternalFragment(f)) return;
                    Log.d(TAG, f.getClass().getName() + " paused");
                }

                @Override
                public void onFragmentStopped(FragmentManager fm, Fragment f) {
                    if (isInternalFragment(f)) return;
                    Log.d(TAG, f.getClass().getName() + " stopped");
                }

                @Override
                public void onFragmentDestroyed(FragmentManager fm, Fragment f) {
                    if (isInternalFragment(f)) return;
                    Log.d(TAG, f.getClass().getName() + " destroyed");
                }
            };

            @Override
            public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
                Log.d(TAG, activity.getLocalClassName() + " created");

                if (activity instanceof FragmentActivity) {
                    ((FragmentActivity) activity).getSupportFragmentManager().registerFragmentLifecycleCallbacks(fragmentLifecycleCallbacks, true);
                }
            }

            @Override
            public void onActivityStarted(Activity activity) {
                Log.d(TAG, activity.getLocalClassName() + " started");
            }

            @Override
            public void onActivityResumed(Activity activity) {
                Log.d(TAG, activity.getLocalClassName() + " resumed");
            }

            @Override
            public void onActivityPaused(Activity activity) {
                Log.d(TAG, activity.getLocalClassName() + " paused");
            }

            @Override
            public void onActivityStopped(Activity activity) {
                Log.d(TAG, activity.getLocalClassName() + " stopped");
            }

            @Override
            public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
                Log.d(TAG, activity.getLocalClassName() + " saveInstanceState");
            }

            @Override
            public void onActivityDestroyed(Activity activity) {
                Log.d(TAG, activity.getLocalClassName() + " destroyed");

                if (activity instanceof FragmentActivity) {
                    ((FragmentActivity) activity).getSupportFragmentManager().unregisterFragmentLifecycleCallbacks(fragmentLifecycleCallbacks);
                }

//                Debug.MemoryInfo memoryInfo = new Debug.MemoryInfo();
//                Debug.getMemoryInfo(memoryInfo);
//
//                Log.d("ActivityLifecycleMemory", "Total Private Dirty: " + memoryInfo.getTotalPrivateDirty() + " KB");
//                Log.d("ActivityLifecycleMemory", "Total PSS: " + memoryInfo.getTotalPss() + " KB");
//                Log.d("ActivityLifecycleMemory", "Total Shared Dirty: " + memoryInfo.getTotalSharedDirty() + " KB");
            }
        });
    }
}
