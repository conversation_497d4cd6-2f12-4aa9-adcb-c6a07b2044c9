package com.movisat.jobs.rutas;

import com.movisat.application.RutasApplication;
import com.movisat.events.rutas.OnErrorEstadoRutaEvent;
import com.movisat.events.rutas.OnSavedEstadoRutaEvent;
import com.movisat.jobs.general.Priority;
import com.movisat.jobs.sincronizacion.SendJob;
import com.movisat.log.Logg;
import com.movisat.managers.RutasManager;
import com.movisat.models.rutas.RutasModel;
import com.movisat.services.HttpResult;
import com.movisat.services.RestClient;
import com.movisat.viewmodels.rutas.RutasEstadoViewModel;
import com.movisat.viewmodels.rutas.RutasHViewModel;
import com.movisat.viewmodels.rutas.IdRutaHViewModel;

import de.greenrobot.event.EventBus;
import retrofit.Response;

public class InsertarEstadoRutaJob extends SendJob {
    public RutasEstadoViewModel estadoRuta;

    public InsertarEstadoRutaJob(RutasEstadoViewModel registro) {
        this(registro, Priority.HIGH);
    }

    public InsertarEstadoRutaJob(RutasEstadoViewModel registro, int priority) {
        super(priority);
        this.estadoRuta = registro;
    }

    @Override
    protected void newJob() {
        RutasApplication.getInstance().getJobManager().addJobInBackground(new InsertarEstadoRutaJob(estadoRuta, Priority.CRITICAL));
    }

    @Override
    public void notifyOK() {
        EventBus.getDefault().post(new OnSavedEstadoRutaEvent(estadoRuta));
    }

    @Override
    public void notifyError() {
        EventBus.getDefault().post((new OnErrorEstadoRutaEvent(estadoRuta)));
    }

    @Override
    public Response sendData() throws Exception {
        Response<RutasHViewModel> response = null;

        try {
            // Validación temprana de GuidRutaH
            if (estadoRuta.GuidRutaH == null || estadoRuta.GuidRutaH.isEmpty()) {
                if (estadoRuta.Estado == RutasModel.ESTADO_ACTIVIDAD_INICIADA) {
                    String guid = RutasManager.getInstance().ensureUUID(estadoRuta.IdRuta);
                    estadoRuta.GuidRutaH = guid;
                } else {
                    Logg.error("InsertarEstadoRutaJob", "GuidRutaH vacío para estado=" + estadoRuta.Estado + 
                            ". No se puede enviar este estado sin haber iniciado actividad.");
                    return null;
                }
            }
            if (estadoRuta.Estado != RutasModel.ESTADO_ACTIVIDAD_INICIADA && estadoRuta.IdRutaH == 0) {
                int idRutaH = RutasManager.getInstance().getIdRutaH(estadoRuta.GuidRutaH);

                if (idRutaH == 0) {
                    Logg.warning("InsertarEstadoRutaJob", "IdRutaH es 0 para el GuidRutaH " + estadoRuta.GuidRutaH + ". Intentando obtener mediante API.");
                    Response<IdRutaHViewModel> responseApi = RestClient.get().getIdRutaHByGuid(estadoRuta.GuidRutaH).execute();
                    if (responseApi.isSuccess() && responseApi.body() != null && responseApi.body().IdRutaH > 0) {
                        idRutaH = responseApi.body().IdRutaH;
                    } else {
                        Logg.error("InsertarEstadoRutaJob", "No se puede enviar el estado de la ruta porque no se ha obtenido el IdRutaH. GuidRutaH: " + estadoRuta.GuidRutaH);
                        return null;
                    }
                }

                estadoRuta.IdRutaH = idRutaH;
            }

            if (estadoRuta.Estado == RutasModel.ESTADO_ACTIVIDAD_FINALIZADA) {
                RutasEstadoViewModel estado_aux = estadoRuta.clone(); // Por qué?
                response = RestClient.get().insertEstadoRutaV2(estado_aux).execute();
            } else response = RestClient.get().insertEstadoRutaV2(estadoRuta).execute();

            if (response != null && response.isSuccess()) {
                if (estadoRuta.Estado == RutasModel.ESTADO_ACTIVIDAD_INICIADA) {
                    // Con el inicio de actividad me quedo con el IdRutaH que me devuelve el servidor
                    RutasHViewModel rutaH = response.body();
                    RutasManager.getInstance().setIdRutaH(estadoRuta.GuidRutaH, rutaH.Id);
                } else if (estadoRuta.Estado == RutasModel.ESTADO_ACTIVIDAD_FINALIZADA && response.code() == HttpResult.OK) {
                    RutasManager.getInstance().setIdRutaH(estadoRuta.GuidRutaH, 0);
                    RutasManager.getInstance().clearUUIDByRuta(estadoRuta.IdRuta);
                }
            }

        } catch (Throwable e) {
            Logg.error("InsertarEstadoRutaJob", e.toString());
            throw e;
        }

        return response;
    }
}
