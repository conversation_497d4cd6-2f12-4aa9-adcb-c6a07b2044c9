package com.movisat.jobs.rutas;

import com.movisat.application.RutasApplication;
import com.movisat.events.rutas.OnErrorElementoProcesadoEvent;
import com.movisat.events.rutas.OnSavedElementoProcesadoEvent;
import com.movisat.jobs.general.Priority;
import com.movisat.jobs.sincronizacion.SendJob;
import com.movisat.log.Logg;
import com.movisat.managers.RutasManager;
import com.movisat.managers.log.LoggerManager;
import com.movisat.services.RestClient;
import com.movisat.utilities.Utils;
import com.movisat.viewmodels.rutas.ElementoProcesadoViewModel;

import de.greenrobot.event.EventBus;
import retrofit.Response;

public class InsertarElementoProcesadoJob extends SendJob {
    public ElementoProcesadoViewModel elementoProcesado;
    private int idRutaH = 0;

    public InsertarElementoProcesadoJob(ElementoProcesadoViewModel elemento) {
        this(elemento, Priority.HIGH);
    }

    public InsertarElementoProcesadoJob(ElementoProcesadoViewModel elemento, int priority) {
        super(priority);
        this.elementoProcesado = elemento;
        setIdRutaH();
    }

    @Override
    protected void newJob() {
        RutasApplication.getInstance().getJobManager().
                addJobInBackground(new InsertarElementoProcesadoJob(elementoProcesado, Priority.CRITICAL));
    }

    @Override
    public void notifyOK() {
        EventBus.getDefault().post(new OnSavedElementoProcesadoEvent(elementoProcesado));
    }

    @Override
    public void notifyError() {
        EventBus.getDefault().post((new OnErrorElementoProcesadoEvent(elementoProcesado)));
    }

    private void setIdRutaH() {
        idRutaH = RutasManager.getInstance().getIdRutaH(elementoProcesado.GuidRutaH);
    }

    @Override
    public Response sendData() throws Exception {
        Response response = null;
        try {
            // Validación de GuidRutaH: si falta, intento recuperarlo desde la ruta
            if (elementoProcesado.GuidRutaH == null || elementoProcesado.GuidRutaH.isEmpty()) {
                String guid = RutasManager.getInstance().getUUID(elementoProcesado.IdRuta);
                if (guid != null && !guid.isEmpty()) {
                    elementoProcesado.GuidRutaH = guid;
                } else {
                    Logg.error("InsertarElementoProcesadoJob", "GuidRutaH vacío. Abortando envío.");
                    return null;
                }
            }
            // Recalculo idRutaH si no está cargado aún
            if (idRutaH == 0)
                setIdRutaH();

            // Si todavía no tengo el campo IdRutaH no puedo seguir
            if (idRutaH == 0)
                return null;

            elementoProcesado.IdRutaH = idRutaH;

            LoggerManager.getInstance().WriteInfo("Enviando procesado. Fecha: " +
                    Utils.datetimeToString(elementoProcesado.FechaProcesado, "yyyy-MM-dd HH:mm:ss") +
                    " Elemento: " + elementoProcesado.CodigoElemento);

            response = RestClient.get().insertElementosProcesadosV2(elementoProcesado).execute();
        } catch (Throwable e) {
            throw e;
        }
        return response;
    }
}
