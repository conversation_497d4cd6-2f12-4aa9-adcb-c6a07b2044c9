package com.environment;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;

import com.movisat.utilities.MD5;
import com.movisat.utilities.Utils;
import com.movisat.utils.FactoryCachedParam;
import com.movisat.utils.Union;

public class Cache {
    public static FactoryCachedParam<Union<Context, String>, BitmapDrawable> bitmap = new FactoryCachedParam<>(p -> {
        Bitmap bitmap = Utils.base64StringToBitmap(p.object2);
        return new BitmapDrawable(p.object1.getResources(), bitmap);
    }, p -> {
        // Lo mapeamos a MD5 para no almacenar en memoria el base64.
        MD5 md5 = new MD5();
        md5.update(p.object2);
        return md5.toString();
    });

/*     public static FactoryCachedListParam<Union<Context, Integer>, BitmapDescriptor> bitmapDescriptor = new FactoryCachedListParam<>(p -> {
         Bitmap bmp = Utils.getBitmapFromVectorDrawable(p.object1, p.object2);
         return BitmapDescriptorFactory.fromBitmap(bmp);
     }, p -> p.object2.toString());*/

}
