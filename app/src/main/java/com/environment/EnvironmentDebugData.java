package com.environment;

import com.movisat.application.RutasApplication;
import com.movisat.viewmodels.mapa.PosicionGPSViewModel;

public class EnvironmentDebugData {

    //================================================================================
    // Fields
    //================================================================================
    private String projectKey = null;
    private String urlServer = null;
    private Integer companyId = null;
    private Boolean canSynchronize = null;
    private String databasePath = null;
    private String deviceId = null;
    private Integer mobileCode = null;
    private Boolean isFakePosition = null;
    private Integer nearElementsOrIncidenceRange = null;
    private Integer navigationType = null;

    //================================================================================
    // Constructors
    //================================================================================
    public EnvironmentDebugData() {
    }

    public EnvironmentDebugData(
            String projectKey,
            String urlServer,
            Integer companyId,
            Boolean canSynchronize,
            String databasePath,
            String deviceIdValue,
            Integer mobileCode,
            Boolean isFakePosition,
            Integer nearElementsOrIncidenceRange,
            Integer navigationType
    ) {
        this.projectKey = projectKey;
        this.urlServer = urlServer;
        this.companyId = companyId;
        this.canSynchronize = canSynchronize;
        this.databasePath = databasePath;
        this.deviceId = deviceIdValue;
        this.mobileCode = mobileCode;
        this.isFakePosition = isFakePosition;
        this.nearElementsOrIncidenceRange = nearElementsOrIncidenceRange;
        this.navigationType = navigationType;
    }

    //================================================================================
    // Getters
    //================================================================================
    public String getProjectKey() {
        if (EnvironmentDebug.isRelease) return null;
        return projectKey;
    }

    public String getUrlServer() {
        if (EnvironmentDebug.isRelease) return null;
        return urlServer;
    }

    public Integer getCompanyId() {
        if (EnvironmentDebug.isRelease) return null;
        return companyId;
    }

    public Boolean getCanSynchronize() {
        if (EnvironmentDebug.isRelease) return null;
        return canSynchronize;
    }

    public String getDatabasePath() {
        if (EnvironmentDebug.isRelease) return null;
        return databasePath;
    }

    public String getDeviceId() {
        if (EnvironmentDebug.isRelease) return null;
        return deviceId;
    }

    public Integer getMobileCode() {
        if (EnvironmentDebug.isRelease) return null;
        return mobileCode;
    }

    public PosicionGPSViewModel getPosition() {
        if (EnvironmentDebug.isRelease) return null;
        if (isFakePosition == null || !isFakePosition) return null;
        return RutasApplication.getInstance().getFakeGPSPosition();
    }

    public Integer getNearElementsOrIncidenceRange() {
        if (EnvironmentDebug.isRelease) return null;
        return nearElementsOrIncidenceRange;
    }

    public Integer getNavigationType() {
        if (EnvironmentDebug.isRelease) return null;
        return navigationType;
    }
}
