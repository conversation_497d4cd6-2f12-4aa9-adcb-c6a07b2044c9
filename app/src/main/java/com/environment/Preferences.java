package com.environment;

import com.movisat.dao.configuracion.ConfiguracionDAO;
import com.movisat.managers.ConfiguracionManager;
import com.movisat.managers.DracoManager;
import com.movisat.models.configuracion.ConfiguracionModel;
import com.movisat.utils.Parser;
import com.movisat.utils.Preference;
import com.movisat.utils.PreferenceListString;
import com.movisat.utils.Utilss;

public class Preferences {

    public static String positionsReachedValue(double lat, double lng) {
        return String.valueOf(lat) + "," + String.valueOf(lng);
    }

    /**
     * Almacena las posiciones alcanzadas por el navegador.
     * <p>
     * El formato debe ser 'lat,long'.
     * <p>
     * Cambio realizado para recordar los destinos alcanzados o saltados manualmente. Al reiniciar o
     * suspender la aplicación, se perdía el estado de los elementos saltados y tenía que volver a
     * pulsar para mostrar su último destino.
     */
    public static final PreferenceListString positionsReached = new PreferenceListString(new Preference<String>(
            () -> {
                ConfiguracionModel m = DracoManager.getConfigBy("resourcesReached", "");
                if (m == null) return "";
                if (m.valor == null) return "";
                return m.valor;
            },
            value -> DracoManager.setConfigBy("resourcesReached", value)
    ));

    public static final Preference<Boolean> isNightModeMap = new Preference<Boolean>(
            () -> {
                ConfiguracionModel nightModeConfig = ConfiguracionManager.getConfBy("map_nightModeOn");
                return nightModeConfig != null && nightModeConfig.valor.equals("1");
            },
            value -> {
                ConfiguracionManager.save("map_nightModeOn", value ? "1" : "0");
            }
    );

    public static final Preference<String> nightHoursToday = new Preference<String>(
            () -> {
                ConfiguracionModel nightModeConfig = ConfiguracionManager.getConfBy("nightHoursToday");
                return nightModeConfig != null ? nightModeConfig.valor : "19:30:00-7:30:00";
            },
            value -> {
                ConfiguracionManager.save("nightHoursToday", value);
            }
    );

    public static final Preference<Boolean> isPlayedNavigatorVoices = new Preference<Boolean>(
            () -> {
                ConfiguracionModel value = ConfiguracionManager.getConfBy("isPlayedNavigatorVoices");
                // Si es null, devolvemos true porque es el valor por defecto.
                return value == null || value.valor.equals("1");
            },
            value -> {
                ConfiguracionManager.save("isPlayedNavigatorVoices", value ? "1" : "0");
            }
    );

}
