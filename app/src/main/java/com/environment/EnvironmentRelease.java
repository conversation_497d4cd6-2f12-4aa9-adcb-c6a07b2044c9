package com.environment;

import com.here.sdk.core.LocationListener;
import com.movisat.log.BuildConfig;
import com.movisat.log.Logg;
import com.movisat.managers.DracoManager;
import com.movisat.models.configuracion.ConfiguracionModel;
import com.movisat.viewmodels.mapa.PosicionGPSViewModel;

import java.util.Date;

public class EnvironmentRelease {

    //================================================================================
    // Fields
    //================================================================================
    /**
     * Indica si se debe mostrar el tab del navegador.
     * Se restablece cada vez que se abre una nueva ruta.
     */
    public static boolean isShowedNavigatorTab = false;
    /**
     * Variable para versiones de testeo que indica si el navegador Here debe simular la navegación.
     * La variable no se puede usar en EnvironmentDebug porque en release se desactivan.
     * <b>Es importante recordar no subir esta variable a [true] en versiones de producción.</b>
     */
    public static boolean isSimulatedNavigation = false;
    /**
     * Indica si la navegación simulada está activa.
     */
    public static boolean isSimulatedNavigationStarted = false;
    /**
     * Posición GPS simulada.
     * Previamente se debe activar la navegación simulada.
     */
    public static PosicionGPSViewModel simulatedPosition = null;

    //================================================================================
    // Getters
    //================================================================================
    public static NavigatorType getNavigatorType() {
        NavigatorType navigatorType = NavigatorType.disable;
        try {
            ConfiguracionModel navigatorConf = DracoManager.getConfigBy("tipo_navegador", "0");
            if (navigatorConf != null) {
                // Si el valor es 3 o 1, se activa el navegador Here.
                // TODO: el valor 3 es el antiguo, se deben ir migrando los proyectos a 1.
                if (navigatorConf.valor.equals("1") || navigatorConf.valor.equals("3"))
                    navigatorType = NavigatorType.here;
                else if (navigatorConf.valor.equals("2")) navigatorType = NavigatorType.herePro;
            }

            if (navigatorType == NavigatorType.disable && BuildConfig.DEBUG && EnvironmentDebug.getData().getNavigationType() != null) {
                if (EnvironmentDebug.getData().getNavigationType().equals(1))
                    navigatorType = NavigatorType.here;
                else if (EnvironmentDebug.getData().getNavigationType().equals(2))
                    navigatorType = NavigatorType.herePro;
                else if (EnvironmentDebug.getData().getNavigationType().equals(3))
                    navigatorType = NavigatorType.here;
            }
        } catch (Exception e) {
            Logg.error("EnvironmentRelease", "[getNavigatorType] " + e.getMessage());
        }
        return navigatorType;
    }

    /*
     * Crea un LocationListener para simular la posición GPS.
     */
    public static LocationListener createSimulatedLocationListener() {
        if (!isSimulatedNavigation) return null;
        return location -> EnvironmentRelease.simulatedPosition = new PosicionGPSViewModel(
              new android.location.Location("HereLocationProvider"),
              location.coordinates.latitude,
              location.coordinates.longitude,
              0,
              0,
              0,
              6,
              new Date()
        );
    }
}