package com.jobqueue;

import static android.content.Context.MODE_PRIVATE;
import static android.content.Context.MODE_WORLD_READABLE;
import static android.content.Context.MODE_WORLD_WRITEABLE;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.movisat.application.RutasApplication;
import com.movisat.jobs.general.Priority;
import com.movisat.jobs.sincronizacion.SendJob;
import com.movisat.log.Logg;
import com.movisat.managers.ManagerTokenJob;
import com.movisat.managers.log.LoggerManager;
import com.movisat.services.HttpResult;
import com.movisat.services.RestClient;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInput;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import retrofit.Response;

public class OutBox extends Thread {
    private static final String TAG = "OutBox";
    private SQLiteDatabase db;
    private static OutBox instance = null;
    private static final LoggerManager log = LoggerManager.getInstance();
    private volatile boolean stop = false;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss",
            new Locale("es", "ES"));
    private ObjectMapper bjectMapper = new ObjectMapper();
    private static String lastJson = "";

    // Constantes para ver el estado de la conexión
    private static final String NOT_INTERNET = "No hay conexión a Internet";
    private static final String NOT_URL_API = "No se ha configurado URL Api";
    private static final String NOT_TOKEN = "No hay token válido";
    private static final String OK = "Ok";

    // Enumerado para ver el resultado del envío
    private enum SEND_RESULT {
        OK, ERROR, CANCEL
    }

    private OutBox() {
        try {
            // Abre la BD y crea la tablas necesarias
            if (openDatabase()) {
                checkDatabase();
                this.start();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // Crea o devuelve una instancia única
    public static synchronized OutBox getInstance() {
        if (instance == null)
            instance = new OutBox();
        return instance;
    }

    // FInaliza el hilo de la bandeja de salida
    public void finalize() {

        stop = true;
    }

    // Intenta abrir la base de datos durante 10 segundos
    protected boolean openDatabase() {
        try {

            for (int i = 0; i < 10 && db == null; i++) {
                if (android.os.Build.VERSION.SDK_INT >= 24) // Android 7 y posteriores
                    db = RutasApplication.getInstance().openOrCreateDatabase("outbox.db", MODE_PRIVATE, null);
                else
                    db = RutasApplication.getInstance().openOrCreateDatabase("outbox.db",
                            MODE_PRIVATE | MODE_WORLD_READABLE | MODE_WORLD_WRITEABLE, null);
                if (db == null)
                    Thread.sleep(1000);
            }

        } catch (Throwable e) {
            debug(e.getMessage());
        }

        return db != null;
    }

    // Crea las tablas necesarias
    protected void checkDatabase() {
        try {

            // Creo la base de datos y los índices si no existen
            db.execSQL("CREATE TABLE IF NOT EXISTS [outbox](" +
                    "[id] INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "[create_at] DATETIME NOT NULL," +
                    "[class] VARCHAR(100)," +
                    "[json] TEXT," +
                    "[endpoint] TEXT," +
                    "[result_code] INTEGER," +
                    "[result_text] TEXT," +
                    "[delivery_at] DATETIME," +
                    "[intents] INTEGER DEFAULT 0," +
                    "[last_intent] DATETIME," +
                    "[canceled_at] DATETIME," +
                    "[object] VARBINARY," +
                    "[priority] INTEGER DEFAULT 0)"
            );

            db.execSQL("CREATE INDEX IF NOT EXISTS [idx_canceled_at] ON [outbox]([canceled_at] ASC)");
            db.execSQL("CREATE INDEX IF NOT EXISTS [idx_delivery_at] ON [outbox]([delivery_at] ASC)");
            db.execSQL("CREATE INDEX IF NOT EXISTS [idx_priority_create_at] ON [outbox]([priority] ASC, [create_at] ASC)");

            db.execSQL("PRAGMA wal_checkpoint(FULL);");

        } catch (Throwable e) {
            debug(e.getMessage());
        }
    }

    // Almacena un registro en BD
    public void save(Job job) {
        try {

            // Realizo la conversión de valores
            int priority = 0;
            switch (job.getPriority()) {
                case Priority.CRITICAL:
                    priority = 0;
                    break;
                case Priority.HIGH:
                    priority = 1;
                    break;
                case Priority.MEDIUM:
                    priority = 2;
                    break;
                case Priority.LOW:
                    priority = 3;
            }

            ContentValues values = new ContentValues();
            values.put("create_at", dateFormat.format(new Date()));
            values.put("priority", priority);
            values.put("class", job.getClass().getSimpleName());
            values.put("object", serialize(job));
            values.put("json", bjectMapper.writeValueAsString(job));

            if (db.insert("outbox", null, values) > 0)
                job.onAdded();

        } catch (Throwable e) {
            debug(e.getMessage());
        }
    }

    // Hilo que lee y envía los registros almacenados
    @Override
    public void run() {
        long readyToSendTimer = 0;
        boolean dataSent = false;

        debug("Bandeja de salida iniciada");

        while (!stop) {
            try {
                dataSent = false;

                // Leo de la BD el primer registro pendiente de envío por orden de prioridad
                Cursor cursor;
                if ((cursor = db.query("outbox", new String[]{"id", "intents", "object", "json"},
                        "delivery_at IS NULL AND canceled_at IS NULL", null,
                        null, null, "priority ASC, create_at ASC")) != null) {

                    if (cursor.moveToFirst()) {

                        // Solo intento enviar si tengo conexión a Internet, la URL de la API y un token válido
                        String errorMessaje = isReadyToSend();
                        if (errorMessaje.equals(OK)) {
                            readyToSendTimer = 0;

                            // Recupero la información del registro leido y deserializo el Job
                            long id = cursor.getLong(0);
                            int intents = cursor.getInt(1);
                            byte[] object = cursor.getBlob(2);
                            SendJob job = (SendJob) deserialize(object);
                            String json = cursor.getString(3);

                            // Si hay 10 intentos lo regsitro en Sentry
                            if (intents == 10) {
                                Logg.catastrophe(TAG, "Se ha intentado enviar más de 10 veces el registro "
                                      + id + ". Job: " + job.getClass().getSimpleName() + " - " + bjectMapper.writeValueAsString(job));
                            }

                            // Creo el contenido para actualizar el registro en BD
                            ContentValues values = new ContentValues();
                            Date date = new Date();

                            // Mantis 6586: Doble asociación de elementos en EcoSAT
                            // Si el JSON es igual al último enviado, no lo envío
                            if (json.equals(lastJson)) {
                                Logg.catastrophe(TAG, "Se ha intentado enviar el mismo registro dos veces seguidas. Job: " + job.getClass().getSimpleName() + " - " + json);
                                values.put("result_text", "Se ha intentado enviar el mismo registro dos veces seguidas");
                                values.put("canceled_at", dateFormat.format(date));
                            } else if (job != null) {
                                // Envío la información al servidor
                                SEND_RESULT sendResult = sendData(job, values);

                                // Incremento el número de intentos y actualizo la fecha del
                                // último intento y de envío si procede
                                values.put("intents", intents + 1);
                                if (sendResult == SEND_RESULT.OK) {
                                    values.put("delivery_at", dateFormat.format(date));
                                    dataSent = true;
                                    lastJson = json;
                                }
                                values.put("object", serialize(job));
                                values.put("json", bjectMapper.writeValueAsString(job));
                                values.put("last_intent", dateFormat.format(date));

                            } else {
                                values.put("result_text", "Fallo al deserializar JOB");
                                values.put("canceled_at", dateFormat.format(date));
                            }

                            // Actualizo el estado del registro en BD
                            db.update("outbox", values, "id=" + id, null);

                            // Notifico el resultado del envío
                            if (job != null) {
                                if (dataSent)
                                    job.notifyOK();
                                else
                                    job.notifyError();
                            }
                        } else {
                            // Si no puedo enviar información lo escribo en el log cada 30 segundos
                            if (new Date().getTime() - readyToSendTimer > 30000L) {
                                if (readyToSendTimer > 0) {
                                    readyToSendTimer = 0;
                                    debug("No se puede enviar información al servidor!!");
                                    // Escribo el motivo por el que no se puede enviar
                                    debug(errorMessaje);
                                } else
                                    readyToSendTimer = new Date().getTime();
                            }
                        }
                    }

                    cursor.close();
                }
            } catch (Throwable e) {
                debug(e.getMessage());
            }

            // El hilo sólo duerme si no hay nada que enviar
            try {
                if (!dataSent)
                    Thread.sleep(1000);
            } catch (InterruptedException e) {
            }
        }

        debug("Bandeja de salida finalizada");
    }

    // LLama al método SendData del Job y devuelve el resultado
    protected SEND_RESULT sendData(SendJob job, ContentValues values) {
        SEND_RESULT result = SEND_RESULT.ERROR;
        Response httpResponse = null;
        int resultCode = 0;
        String resultText = "";
        String endpoint = "";

        try {
            httpResponse = job.sendData();

            endpoint = getEndpointFromResponse(httpResponse);
            resultCode = httpResponse != null ? httpResponse.code() : HttpResult.UNKNOWN_ERROR;
            resultText = "HTTP - " + resultCode;

            switch (resultCode) {
                case HttpResult.OK:
                    result = SEND_RESULT.OK;
                    break;

                case HttpResult.UNAUTHORIZED:
                    // Pido de nuevo el token a la API
                    RutasApplication.getInstance().getJobManagerToken().getPeticionToken(ManagerTokenJob.OPERACION_TOKEN);
                    break;
            }
        } catch (Throwable e) {
            resultText = "Excepción: " + e.getMessage();
            debug(e.getMessage());
        }

        values.put("endpoint", endpoint);
        values.put("result_code", resultCode);
        values.put("result_text", resultText);

        return result;
    }

    protected String getEndpointFromResponse(Response response) {
        String url = "";
        try {
            url = response != null ? response.raw().request().httpUrl().toString() : "";
        } catch (Throwable e) {
        }
        return url;
    }

    // Comprueba si se dan las condiciones para enviar información al servidor
    protected String isReadyToSend() {
        try {
            if (!isNetworkAvailable())
                return NOT_INTERNET;

            if (RestClient.getUrlApi().isEmpty())
                return NOT_URL_API;

            ManagerTokenJob managerTokenJob = RutasApplication.getInstance().getJobManagerToken();
            if (managerTokenJob == null || !managerTokenJob.isTokenValido())
                return NOT_TOKEN;
        } catch (Throwable e) {
            return e.getMessage();
        }
        return OK;
    }

    // Devuelve si hay conexión a Internet
    private boolean isNetworkAvailable() {
        boolean res = false;
        try {
            ConnectivityManager connectivityManager = (ConnectivityManager) RutasApplication.getInstance().
                    getSystemService(RutasApplication.CONNECTIVITY_SERVICE);
            NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            res = activeNetworkInfo != null && activeNetworkInfo.isAvailable() && activeNetworkInfo.isConnected();
        } catch (Throwable e) {
            debug(e.getMessage());
        }
        return res;
    }

    // Serializa un objeto en un array de bytes
    protected byte[] serialize(final Object obj) {
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ObjectOutputStream out = new ObjectOutputStream(bos);
            out.writeObject(obj);
            out.flush();
            return bos.toByteArray();
        } catch (Throwable e) {
            return null;
        }
    }

    // Deserializa un objeto desde un array de bytes
    protected Object deserialize(byte[] bytes) {
        try {
            ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
            ObjectInput in = new ObjectInputStream(bis);
            return in.readObject();
        } catch (Throwable e) {
            return null;
        }
    }

    // Escribe en el archivo LOG
    protected void debug(String text) {
        log.WriteDebug(getClass().getSimpleName() + " : " + text);
    }

}
