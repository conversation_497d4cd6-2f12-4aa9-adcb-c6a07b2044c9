<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.movisat.activities">

    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_INTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" android:minSdkVersion="31" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.INJECT_EVENTS" />
    <uses-permission android:name="android.permission.NFC" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.WRITE_SMS" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.CAMERA"
        tools:ignore="ProtectedPermissions" />

    <!-- Needed for HERE positioning. -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <!--
 The following two permissions are not required to use
            Google Maps Android API v2, but are recommended.
    -->
    <uses-permission android:name="com.google.maps.android.utils.permission.MAPS_RECEIVE" />

    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera"
        android:required="true"
        android:requiredFeature="true"/>

    <permission
        android:name="com.google.maps.android.utils.permission.MAPS_RECEIVE"
        android:protectionLevel="signature" />

    <application
        android:name="com.movisat.application.RutasApplication"
        android:largeHeap="true"
        android:allowBackup="true"
        android:autoRemoveFromRecents="true"
        android:icon="@mipmap/rutas_icon"
        android:label="@string/app_name"
        android:screenOrientation="landscape"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:replace="android:icon, label, android:name, android:theme">
        <!-- Set your credentials for the HERE SDK. -->
        <meta-data android:name="com.here.sdk.access_key_id" android:value="NelTCYuCjtoWMisV8QyHyw" />
        <meta-data android:name="com.here.sdk.access_key_secret" android:value="5MR6njdMDh_GwgfaZPLEuo_psaA41KWyqY-uGjEZ2deOSvzZ4U4kB7vlZga97yV05IJr18K2Zt_rdKieWzPmbw" />

        <!-- Required: set your sentry.io project identifier (DSN) -->
        <meta-data android:name="io.sentry.dsn" android:value="https://<EMAIL>/****************" />
        <!-- enable automatic breadcrumbs for user interactions (clicks, swipes, scrolls) -->
        <meta-data android:name="io.sentry.traces.user-interaction.enable" android:value="true" />
        <!-- enable screenshot for crashes -->
        <meta-data android:name="io.sentry.attach-screenshot" android:value="true" />
        <!-- enable view hierarchy for crashes -->
        <meta-data android:name="io.sentry.attach-view-hierarchy" android:value="true" />
        <!-- enable the performance API by setting a sample-rate, adjust in production env -->
        <meta-data android:name="io.sentry.traces.sample-rate" android:value="0.2" />
        <!-- enable profiling when starting transactions, adjust in production env -->
        <meta-data android:name="io.sentry.traces.profiling.sample-rate" android:value="0.2" />

        <!-- Sirve para que el mapa no provoque que la APP crashee al hacer pruebas en algunos equipos -->
        <!-- <uses-library android:name="org.apache.http.legacy" android:required="false" /> -->
        <meta-data
            android:name="com.google.android.maps.v2.API_KEY"
            android:value="AIzaSyAgWVhIYu_UkdO1szK4I-VV4wRKA70FK3w" />

        <activity
            android:name=".splashScreen.SplashScreenActivity"
            android:launchMode="singleTop"
            android:screenOrientation="landscape"
            android:theme="@style/AppTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".MainActivity"
            android:label="@string/app_name"
            android:launchMode="singleTop"
            android:screenOrientation="landscape"
            android:theme="@style/AppTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
            </intent-filter>
        </activity>
        <activity
            android:name=".rutas.RutasActivity"
            android:label="RUTAS"
            android:launchMode="singleTask"
            android:parentActivityName=".MainActivity"
            android:screenOrientation="landscape"
            android:exported="true"
            android:theme="@style/AppTheme">
        </activity>
        <activity
            android:name=".activacion.ActivacionActivity"
            android:label="ACTIVACION"
            android:launchMode="singleTop"
            android:parentActivityName=".MainActivity"
            android:screenOrientation="landscape"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".rutas.RutasFragmentActivity"
            android:configChanges="keyboardHidden|orientation"
            android:label="RUTAS"
            android:launchMode="singleTop"
            android:parentActivityName=".MainActivity"
            android:screenOrientation="landscape">
        </activity>
        <activity
            android:name=".ajustes.TabInformacionActivity"
            android:configChanges="keyboardHidden|orientation"
            android:label="RUTAS"
            android:launchMode="singleTop"
            android:parentActivityName=".MainActivity"
            android:screenOrientation="landscape">
        </activity>
        <activity
            android:name=".screenSlide.ScreenSlideActivity"
            android:label="AYUDA"
            android:launchMode="singleTop"
            android:parentActivityName=".MainActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name=".moviles.MovilesActivity"
            android:label="MOVILES"
            android:launchMode="singleTop"
            android:parentActivityName=".MainActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name=".map.MapActivity"
            android:label="INCIDENCIA"
            android:launchMode="standard"
            android:parentActivityName=".MainActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name=".personal.PersonalActivity"
            android:label="PERSONAL"
            android:launchMode="standard"
            android:parentActivityName=".MainActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name=".personal.PersonalIdentificacionActivity"
            android:label="PERSONAL"
            android:launchMode="singleTop"
            android:parentActivityName=".personal.PersonalActivity"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".personal.PersonalDescansoActivity"
            android:label="PERSONAL"
            android:launchMode="singleTop"
            android:screenOrientation="landscape" />
        <activity
            android:name=".rutas.InformacionElementoActivity"
            android:label="INFORMACIÓN"
            android:launchMode="singleTop"
            android:screenOrientation="landscape" />
        <activity
            android:name=".lecturallenado.LecturaLlenadoActivity"
            android:label="LLENADO"
            android:launchMode="singleTop"
            android:parentActivityName=".MainActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name=".lecturallenado.LecturaLlenadoContenedoresActivity"
            android:label="LLENADO"
            android:launchMode="singleTop"
            android:screenOrientation="landscape" />
        <activity
            android:name=".descargas.DescargasActivity"
            android:label="DESCARGA"
            android:launchMode="singleTop"
            android:screenOrientation="landscape" />
        <activity
            android:name=".incidencias.IncidenciaElementoActivity"
            android:label="INCIDENCIAS DE ELEMENTOS"
            android:launchMode="standard"
            android:screenOrientation="landscape" />
        <activity
            android:name=".incidencias.IncidenciaTiposActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="standard"
            android:screenOrientation="landscape" />
        <activity
            android:name=".incidencias.IncidenciaModelosActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="standard"
            android:screenOrientation="landscape" />
        <activity
            android:name=".incidencias.IncidenciaMotivosActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="standard"
            android:screenOrientation="landscape" />
        <activity
            android:name=".incidencias.IncidenciaObservacionesActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="standard"
            android:screenOrientation="landscape" />
        <activity
            android:name=".incidencias.IncidenciaFotoActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="standard"
            android:screenOrientation="landscape" />
        <activity
            android:name=".incidencias.CameraSelectionActivity"
            android:launchMode="standard"
            android:screenOrientation="landscape" />
        <activity
            android:name=".incidencias.CamaraInternaActivity"
            android:screenOrientation="landscape"
            android:exported="false" />
        <activity
            android:name=".rutas.SensoresActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="standard"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".mensajeria.MensajeriaActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="singleTop"
            android:screenOrientation="landscape" />
        <activity
            android:name=".mensajeria.MensajeriaNuevoActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="singleTop"
            android:screenOrientation="landscape" />
        <activity
            android:name=".mensajeria.MensajeriaPredefinidosActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="singleTop"
            android:screenOrientation="landscape" />
        <activity
            android:name=".mensajeria.MensajeriaRecibidosActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="singleTop"
            android:screenOrientation="landscape" />
        <activity
            android:name=".mensajeria.MensajeriaEnviadosActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="singleTop"
            android:screenOrientation="landscape" />
        <activity
            android:name=".ajustes.AjustesActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="singleTop"
            android:screenOrientation="landscape" />
        <activity
            android:name=".ajustes.VolumenActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="singleTop"
            android:screenOrientation="landscape" />
        <activity
            android:name=".ajustes.BrilloActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="singleTop"
            android:screenOrientation="landscape" />
        <activity
            android:name=".ajustes.IdentificacionActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="singleTop"
            android:screenOrientation="landscape" />
        <activity
            android:name=".ajustes.DiagnosisActivity"
            android:configChanges="keyboardHidden|orientation"
            android:label="Diagnosis"
            android:launchMode="singleTop"
            android:parentActivityName=".MainActivity"
            android:screenOrientation="landscape"/>
        <activity
            android:name=".ajustes.RutasAjustesActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="singleTop"
            android:screenOrientation="landscape" />
        <activity
            android:name=".ajustes.ConfiguracionMapActivity"
            android:label="Configuración"
            android:launchMode="singleTop"
            android:screenOrientation="landscape" />
        <activity
            android:name=".rutas.ElementoProcesarActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="singleTask"
            android:screenOrientation="landscape" />
        <activity
            android:name=".rutas.ElementoProcesarNivelLlenadoActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="standard"
            android:screenOrientation="landscape" />
        <activity
            android:name=".rutas.ElementoProcesarPodaActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="standard"
            android:screenOrientation="landscape" />
        <activity
            android:name=".rutas.ElementoProcesarReparacionActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="standard"
            android:screenOrientation="landscape" />
        <activity
            android:name=".rutas.ElementoProcesarLavadoActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="standard"
            android:screenOrientation="landscape" />
        <activity
            android:name=".rutas.NumeroElementosProcesarActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="standard"
            android:screenOrientation="landscape" />
        <activity
            android:name=".revisiones.RevisionesActivity"
            android:label="REVISIONES"
            android:launchMode="standard"
            android:parentActivityName=".MainActivity"
            android:screenOrientation="landscape"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".revisiones.RevisionesAtributosActivity"
            android:label="REVISIONES"
            android:launchMode="singleTask"
            android:parentActivityName=".revisiones.RevisionesActivity"
            android:screenOrientation="landscape"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".revisiones.RevisionesObservacionesActivity"
            android:label="REVISIONES"
            android:launchMode="singleTask"
            android:parentActivityName=".revisiones.RevisionesAtributosActivity"
            android:screenOrientation="landscape"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".reparaciones.ReparacionesActivity"
            android:label="REPARACIONES"
            android:launchMode="standard"
            android:screenOrientation="landscape" />
        <activity
            android:name=".reparaciones.ReparacionesObservacionesActivity"
            android:label="REPARACIONES OBSERVACIONES"
            android:launchMode="standard"
            android:screenOrientation="landscape" />
        <activity
            android:name=".wificamera.WifiCameraActivity"
            android:configChanges="keyboardHidden|orientation"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden" />
        <activity android:name=".ExitActivity" />
        <activity
            android:name=".ayuda.HelpActivity"
            android:label="@string/help_ecorutas"
            android:theme="@style/AppThemeBackButton" />
        <activity
            android:name=".voluminosos.VoluminososTiposActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="standard"
            android:screenOrientation="landscape" />
        <activity
            android:name=".voluminosos.VoluminososModelosActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="standard"
            android:screenOrientation="landscape" />
        <activity
            android:name=".voluminosos.VoluminososObservacionesActivity"
            android:label="MOVISAT RUTAS  vehiculo:"
            android:launchMode="standard"
            android:screenOrientation="landscape" />

        <receiver
            android:name="com.movisat.receivers.EcosatBroadcast"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.movisat.draco.action.NEW_SENSOR" />
                <action android:name="com.movisat.draco.action.NO_SIM" />
                <action android:name="android.intent.action.ACTION_SHUTDOWN" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.movisat.receivers.AdminReceiver"
            android:label="@string/app_name"
            android:permission="android.permission.BIND_DEVICE_ADMIN">
            <meta-data
                android:name="android.app.device_admin"
                android:resource="@xml/device_admin" />

            <intent-filter>
                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.movisat.services.MyLocationService"
            android:icon="@mipmap/rutas_icon" />

        <meta-data
            android:name="AA_MODELS"
            android:value="com.movisat.models.elementos.ElementosModel,
                            com.movisat.models.modelos.ModelosModel,
                            com.movisat.models.configuracion.ConfiguracionModel,
                            com.movisat.models.trabajadores.DescansosModel,
                            com.movisat.models.configuracion.LogsModel,
                            com.movisat.models.incidencias.IncidenciasModeloModel,
                            com.movisat.models.incidencias.IncidenciasMotivoModel,
                            com.movisat.models.incidencias.IncidenciasTipoModel,
                            com.movisat.models.incidencias.IncidenciasRutaModel,
                            com.movisat.models.mensajes.MensajesModel,
                            com.movisat.models.moviles.MovilesModel,
                            com.movisat.models.moviles.EntradaDigitalResiduoModel,
                            com.movisat.models.moviles.PdfsModel,
                            com.movisat.models.mensajes.MensajesPredefinidosModel,
                            com.movisat.models.rutas.RutasModel,
                            com.movisat.models.revisiones.RevisionesModel,
                            com.movisat.models.revisiones.RevisionesDatosModel,
                            com.movisat.models.revisiones.RevisionesAtributosModel,
                            com.movisat.models.rutas.RutasElementosModel,
                            com.movisat.models.trabajadores.TrabajadoresModel,
                            com.movisat.models.trabajadores.CategoriaTrabajadoresModel,
                            com.movisat.models.rutas.SensoresModel,
                            com.movisat.models.rutas.ConfiguracionEcoRutasModel,
                            com.movisat.models.rutas.ProcesadoPorActividadModel,
                            com.movisat.models.reparaciones.ReparacionesModel,
                            com.movisat.models.elementos.LecturasLlenadoModel,
                            com.movisat.models.incidencias.IncidenciasModel,
                            com.movisat.models.rutas.AreasModel,
                            com.movisat.models.rutas.AreaPuntosModel,
                            com.movisat.models.rutas.RutaPuntosModel,
                            com.movisat.models.tags.TagsModel,
                            com.movisat.models.voluminosos.VoluminososModel,
                            com.movisat.models.voluminosos.VoluminososTipoModel,
                            com.movisat.models.rutas.AsignacionesModel" />
        <meta-data
            android:name="AA_DB_NAME"
            android:value="rutas.db" />
        <!-- Indica la versión de la base de datos, se aumenta cuando se crea un nuevo script de migración en: assets/migrations-->
        <meta-data
            android:name="AA_DB_VERSION"
            android:value="25" /> <!-- <meta-data -->
        <!-- android:name="com.here.android.maps.appid" -->
        <!-- android:value="Qi5YFy4IJ0dQWTgMMlla" /> -->
        <!-- <meta-data -->
        <!-- android:name="com.here.android.maps.apptoken" -->
        <!-- android:value="_mrEb65K1ezbjpyAhpFadQ" /> -->
        <!-- <meta-data -->
        <!-- android:name="com.here.android.maps.license.key" -->
        <!-- android:value="BlDqIgYaFt62j8PleyH2RE5J0Zi15o3WlmTOUeja5Xhr30UFzdRH2Nuz/2HSGd+Av1rAwMR11repBZi6zpjzdA1zyn4pgWfTh4yVbaxczBKHoaKv/wffX7IAoNo55Sfu6jwYY2ixPJviXqRiPAOiq0e5kqQB0G/ros/AHklOjjpYnhmS/DQLTJGQRLZ6DrL+/U9OK+NaRzesMGKcQR57Lm8avl8D0PRyH+yfuzm/NYOpTIpwB9+h9sADSdlJLaHt7IfHYUaDfO+oVtAl04kSKS52Bk9TxTBhxbA6kbsJeMFAGasHvkAo+ZEPt0waGNx9Xk/ak3zM2ZjXjjJ5G9Ws7KhVxz/XsVPjFcxX2IegIkfu8Ha5Q15DJghxq6KRQrZgqbXTfhN6rASCyNMh6poorhvBDrNKUKY8aAuqRHP9/URpoktg7Ny/kS5VwjzfSpmF+/5qolxBZ+ax2akKBvAkchm5pft8XcTjv0sTR5MVSLLwkMvY6na4+QW6G9Xo+Z/YTJVsizbQhforV87Fc6s5bKetM7uBCalTZuoP2Z8qIkaTa9kYn/CTtbbOQlhMNyIGfxDCXdbBYbxxw7cSCn0TvV0UCF8Yp5kCX+YAjBDyd/zhUf4iSmDZdlsoJ8VCguKTeSTlpUVhuh1Lmbr5lFI1ucsRhn6nySkUrOuV6yT88NM=" /> -->
        <!-- <service -->
        <!-- android:name="com.here.android.mpa.service.MapService" -->
        <!-- android:exported="true" -->
        <!-- android:label="HereMapService" -->
        <!-- android:process="global.Here.Map.Service.v2"> -->
        <!-- <intent-filter> -->
        <!-- <action android:name="com.here.android.mpa.service.MapService"></action> -->
        <!-- </intent-filter> -->
        <!-- </service> -->
        <!-- #################### CALLING LIB #################### -->
        <!-- <activity android:name="com.movisat.callinglib.activities.ContactsActivity" -->
        <!-- android:screenOrientation="landscape"/> -->
        <activity
            android:name=".agenda.MyContactsActivity"
            android:screenOrientation="landscape"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".agenda.MyCallActivity"
            android:screenOrientation="landscape">
            <intent-filter>
                <action android:name="android.intent.action.DIAL" />

                <data android:scheme="tel" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.DIAL" />
            </intent-filter>
        </activity>
        <activity
            android:name=".NfcActivity"
            android:exported="true">

            <intent-filter>
                <action android:name="android.nfc.action.TECH_DISCOVERED" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <meta-data
                android:name="android.nfc.action.TECH_DISCOVERED"
                android:resource="@xml/nfc_tech_list" />
        </activity>
        <provider
            android:name="com.activeandroid.content.ContentProvider"
            android:authorities="com.movisat.ecosat"
            android:enabled="true"
            android:exported="false" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>
        
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="{myApplicationId}.androidx-startup"
            tools:replace="android:authorities,exported"
            android:exported="true" >
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup" />
        </provider>

        <service
            android:name="com.movisat.callinglib.phone.MyInCallService"
            android:permission="android.permission.BIND_INCALL_SERVICE">
            <meta-data
                android:name="android.telecom.IN_CALL_SERVICE_UI"
                android:value="true" />

            <intent-filter>
                <action android:name="android.telecom.InCallService" />
            </intent-filter>
        </service>
    </application>

</manifest>