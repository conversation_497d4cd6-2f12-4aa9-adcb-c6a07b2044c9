apply plugin: 'com.android.application'

repositories {
    mavenCentral()
    maven {
        url "https://oss.sonatype.org/content/repositories/snapshots/"
    }
    maven {
        url "https://maven-other.tuya.com/repository/maven-releases"
    }
    maven {
        url "https://jitpack.io"
    }
}

// FIRMA - INI
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}
// FIRMA - FIN

android {
    // FIRMA - INI
    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled false // No se puede activar el minify, la app no funciona en DMV2
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    // FIRMA - FIN
    compileSdkVersion 31
    packagingOptions {
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/DEPENDENCIES'
    }
    defaultConfig {
        applicationId 'com.movisat.ecosat'
        minSdkVersion 22
        multiDexEnabled true
        targetSdkVersion 31
        versionCode 25091001
        versionName '25091001'
        vectorDrawables.useSupportLibrary = true
        // Comentar para ejecutar en emulador.
        ndk {
            abiFilters 'armeabi-v7a'
        }
    }
    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }
    flavorDimensions "version"
    productFlavors {
        signedConfig {
            minSdkVersion 22
            signingConfig signingConfigs.release
            targetSdkVersion 28
            dimension "version"
        }
    }
    dexOptions {
        incremental true
        javaMaxHeapSize "4g"
    }
    aaptOptions {
        cruncherEnabled = false
    }
    compileOptions {
        sourceCompatibility 1.8
        targetCompatibility 1.8
    }
}
def version_retrofit = "2.0.0-beta2"

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.aar', '*.jar'], exclude: ['*mock*.jar'])
    implementation project(':sweetalert:library')
    implementation project(':api')
    implementation files('libs/mint-5.1.0.jar')
    implementation files('libs/jts-1.13.jar')
    implementation files('libs/Loqjapi.jar')
    implementation files('libs/LTTS_wrapper.jar')
    implementation files('libs/Workmates.jar')
    implementation 'com.google.android.material:material:1.3.0'
    implementation 'com.google.code.gson:gson:2.8.6'
    implementation "androidx.work:work-runtime:2.7.1"
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'org.mockito:mockito-core:1.10.19'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation "androidx.recyclerview:recyclerview:1.1.0"
    implementation 'androidx.camera:camera-core:1.1.0'
    implementation 'androidx.camera:camera-camera2:1.1.0'
    implementation 'androidx.camera:camera-lifecycle:1.1.0'
    implementation 'androidx.camera:camera-view:1.1.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    implementation 'androidx.multidex:multidex:2.0.0'
    implementation "androidx.fragment:fragment:1.2.5"
    implementation 'androidx.navigation:navigation-fragment:2.2.2'
    implementation 'androidx.navigation:navigation-ui:2.2.2'
    implementation "androidx.appcompat:appcompat:1.2.0"
    testImplementation 'org.hamcrest:hamcrest-library:1.1'
    implementation 'com.github.castorflex.smoothprogressbar:library:1.1.0'
    implementation 'com.noveogroup.android:android-logger:1.3.5'
    implementation 'de.greenrobot:eventbus:2.4.0'
    implementation "com.squareup.retrofit:retrofit:${version_retrofit}"
    implementation 'com.squareup.okhttp:okhttp-urlconnection:2.4.0'
    implementation 'com.squareup.okhttp:okhttp:2.4.0'
    implementation 'com.tuya.smart:tuyaCamera:3.0.6' // Anterior
//    implementation group: 'com.tuya.smart', name: 'tuyaCamera', version: '3.11.0r119h2' // No funciona
    implementation "com.squareup.retrofit:converter-jackson:${version_retrofit}"
    implementation "com.squareup.retrofit:adapter-rxjava:${version_retrofit}"
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.6.1'
    implementation 'me.zhanghai.android.materialprogressbar:library:1.0.2'
    implementation 'commons-net:commons-net:3.3'
    implementation 'com.google.android.gms:play-services-maps:18.2.0'
    implementation 'com.github.mhiew:android-pdf-viewer:3.2.0-beta.3'
    implementation project(':callinglib')
    implementation project(':utils')
    implementation project(':log')
    implementation project(':log_provider_sentry')
    implementation 'io.sentry:sentry-android:5.5.2'
}
